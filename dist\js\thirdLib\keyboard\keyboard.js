function MQkeyBoard(el,param,cb){
    this.$el = el;
    this.cb = cb;
    this.type = param.type || 0;
    this.isMQ = param.isMQ;
    this.bgImgUrl = param.data.bgImgUrl || './img/';
    this.renderKeyBoard(param);
    this.initMQ();
}
MQkeyBoard.prototype = {
    fillMQFillList: new Map(),
    MQIndex: 0,
    currMQ: 0,
    waitRemoveOptionlist: [],
    config: {
        spaceBehavesLikeTab: true,
        autoOperatorNames: 'abcdtestxyz',//测试配置，修改默认识别单词
    },
    algeMathObj: [
        {name: '+', value: '+',src:'1-5.png'},
        {name: '-', value: '-',src:'2-5.png'},
        {name: '×', value: '\\times',src:'3-5.png'},
        {name: '÷', value: '\\div',src:'4-5.png'},
        {name: '正负', value: '\\pm',src:'4-4.png'},
        {name: '等于', value: '=',src:'5-5.png'},
        {name: '约等于', value: '\\approx',src:'5-4.png'},
        {name: '不等于', value: '\\neq',src:'6-5.png'},
        {name: '分数', value: '\\frac{}{}',src:'1-1.png',goleft:true},
        {name: '根号', value: '\\sqrt{}',src:'3-1.png',goleft:true},
        {name: '平方', value: '{}^2',src:'4-1.png'},
        {name: '绝对值', value: '\\left|{}\\right|',src:'2-1.png',goleft:true},
        {name: '大于', value: '>',src:'7-2.png'},
        {name: '小于', value: '<',src:'7-3.png'},
        {name: '大于等于', value: '\\geq',src:'7-4.png'},
        {name: '小于等于', value: '\\leq',src:'7-5.png'},
        {name: '方根', value: '\\sqrt[]{}',src:'7-1.png'},
        {name: 'n次方', value: '^{}',src:'6-1.png',goleft:true},
        {name: '立方', value: '{}^3',src:'5-1.png',goleft:true},
        {name: '下标', value: '_{}',src:'8-1.png',goleft:true},
        {name: '交集', value: '\\cap',src:'8-5.png'},
        {name: '并集', value: '\\cup',src:'8-4.png'},
        {name: '包含于', value: '\\in',src:'9-4.png'},
        {name: '不包含于', value: '\\ni',src:'9-5.png'},
        {name: '求和', value: '\\sum_{}^{}{}',src:'9-3.png',goleft:true},
        {name: 'log', value: 'log_{}{}',src:'10-1.png',goleft:true},
        {name: 'lg', value: 'lg{}',src:'10-2.png'},
        {name: 'ln', value: 'ln{}',src:'10-3.png'},
        {name: '上下标', value: '{}_{}^{}',src:'9-1.png',goleft:true},
        {name: '阶乘', value: '{}!',src:'11-1.png'},
        {name: '积分', value: '\\∫_{}^{}',src:'9-2.png',goleft:true},
        {name: '极限', value: '\\lim_{n\\to\\infty}x_n',src:'10-4.png'},
        {name: '无穷', value: '\\infty',src:'10-5.png'},
        {name: '千分之', value: '{}‰',src:'5-3.png'},
        {name: '单引号', value: '{}\\prime',src:'11-2.png'},
        {name: '双引号', value: '{}\\prime\\prime',src:'11-3.png'},
        {name: '摄氏度', value: '℃',src:'12-1.png'},
        {name: '华氏度', value: '℉',src:'12-2.png'},
        {name: '千米', value: 'km',src:'12-5.png'},
        {name: '千克', value: 'kg',src:'11-5.png'},
        {name: '分米', value: 'ml',src:'13-2.png'},
        {name: '[ )', value: '[ )',src:'', isText: true,goleft:true},
        {name: '( ]', value: '( ]',src:'', isText: true,goleft:true},
        { name: "①", value: "①", src: "" ,isText:true},
        { name: "②", value: "②", src: "" ,isText:true},
        { name: "③", value: "③", src: "" ,isText:true},
        { name: "④", value: "④", src: "" ,isText:true},
        { name: "⑤", value: "⑤", src: "" ,isText:true},
        { name: "⑥", value: "⑥", src: "" ,isText:true},
        { name: "⑦", value: "⑦", src: "" ,isText:true},
        { name: "⑧", value: "⑧", src: "" ,isText:true},
        { name: "⑨", value: "⑨", src: "" ,isText:true},
        { name: "⑩", value: "⑩", src: "" ,isText:true}
    ],
    geomeMathObj: [
        {name: '角', value: '\\angle',src:'1-1.png'},
        {name: '垂直', value: '\\perp',src:'1-2.png'},
        {name: '平行', value: '\{//}',src:'1-3.png'},
        {name: 'Δ', value: '\\Delta',src:'1-4.png'},
        {name: '上矢量', value: '\\overline{}',src:'1-5.png',goleft:true},
        {name: 'π', value: '\\pi',src:'2-3.png'},
        {name: 'πr', value: '\\pi{r}',src:'2-4.png'},
        {name: 'πr2', value: '\\pi{r^2}',src:'2-5.png'},
        {name: 'α', value: '\\alpha',src:'3-1.png'},
        {name: 'β', value: '\\beta',src:'3-2.png'},
        {name: 'λ', value: '\\lambda',src:'3-3.png'},
        {name: 'θ', value: '\\theta',src:'3-4.png'},
        {name: 'φ', value: '\\varphi',src:'3-5.png'},
        {name: 'η', value: '\\eta', src: '4-1.png'},
        {name: 'μ', value: '\\mu',src:'4-2.png'},
        {name: 'ε', value: '\\varepsilon',src:'4-3.png'},
        {name: 'sin', value: 'sin',src:'5-1.png'},
        {name: 'cos', value: 'cos',src:'5-2.png'},
        {name: 'tan', value: 'tan',src:'5-3.png'},
        {name: 'cot', value: 'cot',src:'5-4.png'},
        {name: 'Φ', value: '\\Phi',src:'4-5.png'},
        {name: 'δ', value: '\\delta',src:'4-4.png'},
        {name: '相似', value: '\\sim',src:'2-1.png'},
        {name: '全等', value: '\\cong',src:'2-2.png'},
        {name: '圆点', value: '\\bigodot',src:'6-5.png'},
        {name: 'arcsin', value: 'arcsin',src:'6-1.png'},
        {name: 'arccos', value: 'arccos',src:'6-2.png'},
        {name: 'arctan', value: 'arctan',src:'6-3.png'},
        {name: 'arccot', value: 'arccot',src:'6-4.png'},
        {name: '度数', value: '°',src:'5-5.png'},
    ],
    renderKeyBoard: function (param) {
        var self = this;
        self.$el.empty();

        var keyPad = $('<div class="mathKeypad"></div>');
        var keyMl = $('<div class="mathKeypad-ml"></div>');
        var keyPadTop = $('<div class="mathKeypad-top"><span class="mathKeypad-title">'+param.data.title+'</span><span class="mathKeypad-close">×</span></div>');
        var keyPadOptionWrap = $('<div class="mathKeypad-option"></div>');
        var keyPadShow = $('<a class="add-option">+添加备选答案<span>  (学生答对任一个都算对)</span></a><div class="btn_box">' +
            '<input type="button" class="delete">' +
            '<input type="button" value="清空" class="clear">' +
            '<input type="button" value="我要输入公式" class="open isOpened"  '+(self.isMQ?"":"style='visibility:hidden;'")+'>' +
            '<input type="button" value="代数" class="alge active" style="display: none">' +
            '<span class="blue-triangle alge-trian"></span>'+
            '<input type="button" value="几何" class="geome" style="display:none;">' +
            '<span class="blue-triangle geome-trian"></span>'+
            '<input type="button" value="完成" class="ok active">' +
            '</div>');
        var keyMain = $('<div class="mathKeypad-main">');
        var algeHtml = "";
        var geomeHtml = "";
        keyPad.appendTo(this.$el||document.body);
        keyMl.appendTo(this.$el||document.body);
        if(param.type==0){
            keyPadTop.appendTo(keyPad);
        }
        keyPadOptionWrap.appendTo(keyPad);
        keyPadShow.appendTo(keyPad);
        keyMain.appendTo(keyPad);
        self.algeMathObj.forEach(function (item) {
            algeHtml += '<div class="keyButton algeBtn" data-direction="'+(item.goleft?"1":"0")+'" data-key="' + item.value + '" style="background:#fff url(' + self.getKeybordSrc(true,item.src) + ') no-repeat center/100%">';
            if( item.isText ){
                algeHtml += item.value;
            }
            algeHtml += '</div>';
        });
        self.geomeMathObj.forEach(function (item) {
            geomeHtml += '<div class="keyButton geomeBtn" data-direction="'+(item.goleft?"1":"0")+'" data-key="' + item.value + '" style="display: none;background:#fff url(' + self.getKeybordSrc(false,item.src) + ') no-repeat center/100%;">';
            if( item.isText ){
                geomeHtml += item.value;
            }
            geomeHtml += '</div>';
        });
        keyMain.append(algeHtml);
        keyMain.append(geomeHtml);
        if (param.css)
            keyPad.css(param.css);
        self.add_listener(keyPad);
        self.loadOption(param.data.answer);
        self.getOptionFocus();
    },
    initMQ: function () {
        var self = this;
        $(".mathKeypad,.mathKeypad-ml").show();
        //中文输入特殊处理
        $('.mathKeypad').delegate(".mq-textarea textarea", "compositionstart", function () {
            $(this).addClass("ime-on");
            return false;
        }).delegate(".mq-textarea textarea", "compositionend", function () {
            $(this).removeClass("ime-on");
            return false;
        });
        //键盘输入监听
        $('.mathKeypad').delegate(".mq-textarea textarea", "keyup", function (event) {
            // 开启输入法时，不处理键盘事件
            if ($(this).is(".ime-on")) {
                return false;
            }

            var content = $(this).val();
            var length = $(this).parent().siblings('.mq-root-block').text().length-1;
            if(content.length+length>200){
                content = content.substr(0,200-length);
                parent.layer.msg('最多支持输入200个字符！')
            }
            self.fillMQFillList.get(self.currMQ).write(content);
            $(this).val('');
            return false
        });

        // 输入监听,文本框宽度设置
        $(".mathKeypad-option").delegate('.fill-small-ans input','input propertychange', function(){
            var value = $(this).val();
            if(value.length>200){
                value = value.substr(0,200);
                parent.layer.msg('最多支持输入200个字符！')
            }
            $(this).val(value)
            $(this).siblings('.input-width').text(value);
        });
    },
    addValue: function (newValue,goleft) {
        var mqf = this.fillMQFillList.get(this.currMQ);
        mqf.focus();
        mqf.write(newValue);
        if(goleft){
            mqf.keystroke("Left");
        }
    },
    add_listener: function () {
        var self = this;
        $(".mathKeypad-close").off().on('click',function () {
            self.$el.hide()
            $('.mathKeypad-ml').hide();
        });

        $(".keyButton").off().on('click', function () {
            var goleft = $(this).data("direction");
            self.addValue($(this).data("key"),goleft);
        });

        $(".add-option").off().on('click', function () {
            if (self.fillMQFillList.size >= 10) {
                parent.layer.msg("最多支持添加10个备选答案~")
                return;
            }
            var noAns = false;
            self.fillMQFillList.forEach(function (mq) {
                var text;
                if(self.isMQ){
                    text = mq.latex();
                }else{
                    text = mq.val().trim();
                }
                if(text=='')
                    noAns = true;
            })
            if(noAns){
                parent.layer.msg("答案不可为空~")
                self.getOptionFocus();
                return;
            }
            self.addOption("");
            self.getOptionFocus();
        });

        $(".alge").off().on('click', function () {
            $(this).addClass('active').siblings('.geome').removeClass('active');
            $(this).siblings('.alge-trian').show().siblings('.geome-trian').hide();
            $(".algeBtn").show().siblings('.geomeBtn').hide();
            self.fillMQFillList.get(self.currMQ).focus();
            self.layOut();
        });
        $(".geome").off().on('click', function () {
            $(this).addClass('active').siblings('.alge').removeClass('active');
            $(this).siblings('.geome-trian').show().siblings('.alge-trian').hide();
            $(".algeBtn").hide().siblings('.geomeBtn').show();
            self.fillMQFillList.get(self.currMQ).focus();
            self.layOut();
        });

        $(".ok").off().on('click', function () {
            var data = [];
            var sameAns = false;
            var noAns = false;

            self.fillMQFillList.forEach(function (mq) {
                var text;
                if(self.isMQ){
                    text = mq.latex();
                    //（）括号暂不公式化
                    text = text.replace("\\left","").replace("\\right","").replace("<"," < ").replace(">"," > ");
                }else{
                    text = mq.val().trim();
                }
                if (text != ""){
                    text = text.replace(/\\ /g, " ");
                    data.forEach(function(item){
                        if(text==item.name)
                            sameAns = true;
                    });
                    var iscontainFormula = /\\+|\^+|\_+/.test(text);
                    data.push({name:text,containFormula:iscontainFormula});
                }else{
                    noAns = true;
                }
            })
            if(noAns){
                parent.layer.msg("答案不可为空~")
                self.getOptionFocus();
                return;
            }
            if(sameAns){
                parent.layer.msg("存在相同备选答案,请重新编辑~")
                self.getOptionFocus();
                return;
            }

            if(!noAns&&!sameAns)
                self.cb&&self.cb(data);
        });
        $(".open").off().on('click', function (e) {
            var target = $(e.currentTarget);
            if (target.hasClass('isOpened')) {
                target.val('收起').removeClass('isOpened');
                target.siblings().not('.ok').show();
                $('.mathKeypad-main').show();
                target.siblings('.alge').trigger('click');
            } else {
                target.val('我要输入公式').addClass('isOpened');
                target.siblings().not('.ok').hide();
                $('.mathKeypad-main').hide();
            }
            self.fillMQFillList.get(self.currMQ).focus();
            self.layOut();
        });

        $(".clear").off().on('click', function () {
            self.fillMQFillList.get(self.currMQ).latex('').focus();
        });

        $(".delete").off().on('click', function () {
            self.fillMQFillList.get(self.currMQ).keystroke("Backspace").focus();
        });
    },
    loadOption: function (answer) {
        var self = this;
        this.fillMQFillList.clear();

        if (answer.detailAnswer.length > 0) {
            answer.detailAnswer.forEach(function (item) {
                self.addOption(item.blankAnswer);
            });
        } else {
            self.addOption("");
        }
    },
    addOption: function (value) {
        var self = this;
        var html = '<div data-index="' + self.MQIndex + '" class="item-div"><div class="fill-small-ans"></div><a class="remove-small-ans">×</a></div>';
        $('.mathKeypad-option').append(html);
        var mqMathField;
        if(self.isMQ){
            var mq = MathQuill.getInterface(2);
            mqMathField = mq.MathField($(".mathKeypad-option [data-index=" + self.MQIndex + "] .fill-small-ans")[0], self.config);
            value = value.replace(/\s/g,'\\ ');
            mqMathField.latex(value);
        }else{
            $(".mathKeypad-option [data-index=" + self.MQIndex + "] .fill-small-ans").append("<input type='text' spellcheck ='false' value='"+value+"' /><span class='input-width'>"+value+"</span>");
            mqMathField = $(".mathKeypad-option [data-index=" + self.MQIndex + "] .fill-small-ans input");
        }
        self.fillMQFillList.set(self.MQIndex, mqMathField);
        $(".mathKeypad-option .item-div").off().on('mousedown', function () {
            self.currMQ = $(this).data("index");
        });

        $('.mathKeypad-option .item-div .remove-small-ans').off().on("click", function (e) {
            self.removeOption(e);
        });
        self.fillMQFillList.forEach(function (mq) {
            mq.blur();
        });
        self.MQIndex++;
        self.toggleRemoveBtn();
        self.layOut();
    },
    removeOption: function (e) {
        var item = $(e.currentTarget).parent(".item-div");
        this.fillMQFillList.delete(item.data("index"));
        item.remove();
        this.toggleRemoveBtn();
        this.getOptionFocus();
    },
    getOptionFocus: function(){
        this.currMQ = $('.item-div:last').data('index');
        this.fillMQFillList.get(this.currMQ).focus();
    },
    getKeybordSrc: function(isalge,src){
        var str = isalge?'ilgeimg':'geomimg';
        return this.bgImgUrl+str+'/'+src;
    },
    toggleRemoveBtn: function(){
        var removeBtn = $('.remove-small-ans');
        var length = removeBtn.length;
        length>1?removeBtn.css('display','inline-block'):removeBtn.hide();
        $('.mathKeypad-option').scrollTop(100*length);
    },
    layOut:function(){
        var keyPad = $('.mathKeypad');
        var width = keyPad.width();
        var height = keyPad.height();
        if( this.type==0 ){
            keyPad.css({marginTop:-height/2,marginLeft:-width/2});
        }else{
            var index = parent.layer.getFrameIndex(window.name);
            var keyMainH = $('.mathKeypad-main').get(0).offsetHeight;
            var opn_H = $('.mathKeypad-option').outerHeight(true);
            var opt_H = $('.add-option').outerHeight(true);
            var btn_H = $('.btn_box').outerHeight(true);
            height = keyMainH + opn_H + opt_H +  btn_H + 55;
            parent.layer.style(index,{
                width:width,
                height:height,
                top: '50%',
                left:'50%',
                marginTop:-height/2,
                marginLeft:-width/2

            });
            keyPad.css({"top":0,"left":0});
        }
    },
    Trim:function (str){
        return str.replace(/(^(\\\s+)*)|((\\\s+)*$)/g, "");
    }
}