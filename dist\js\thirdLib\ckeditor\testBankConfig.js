"use strict";

/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-05-16 14:31:39
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-08-07 15:34:17
 */
CKEDITOR.editorConfig = function (config) {
  // CKEDITOR.config.wiriscontextpath = "/formula_plugins/";
  // config.language = 'zh-cn';
  config.allowedContent = true;
  config.skin = 'moono-lisa';
  config.width = $("#htmlDiv").width() + 52;
  //高度配置
  // config.height = $("#htmlDiv").height() + 200;
  // config.toolbar = 'Full';
  // config.startupOutlineBlocks = false;
  // config.extraPlugins += (config.extraPlugins.length == 0 ? '' : ',') + 'ckeditor_wiris';
  config.removeDialogTabs = 'image:advanced;image:Link';
  //预览区域显示内容
  config.image_previewText = ' ';
  // 文件上传地址配置
  var actionUrl = '/testbank/testBank/uploadEditorImgNew';
  config.filebrowserUploadUrl = getQueryString("url") + actionUrl + '?htmlurl=' + CutHtml.v.htmlUrl + '&_csrf=' + $("meta[name='_csrf_token']").attr("content");
  //工具栏配置
  config.toolbar = [['Font'], ['Bold', 'Underline', 'Subscript', 'Superscript', 'RemoveFormat', "UnderDot", "SolidWaveline"], ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"], ['-', 'Image', 'Table', 'SpecialChar', 'ckeditor_wiris_formulaEditor'], ['-', 'Undo', 'Redo']];
  config.font_names = '宋体/宋体;黑体/黑体;仿宋/仿宋;楷体/楷体;隶书/隶书;幼圆/幼圆;微软雅黑/微软雅黑;' + config.font_names;
  config.enterMode = CKEDITOR.ENTER_P;
  config.shiftEnterMode = CKEDITOR.ENTER_BR;
  config.extraPlugins = "UnderDot";
  // config.removePlugins = 'elementspath,contextmenu,liststyle,tabletools,tableselection';
  // config.toolbar.push({name:'wiris', items:['ckeditor_wiris_formulaEditor', 'ckeditor_wiris_formulaEditorChemistry']});
};
