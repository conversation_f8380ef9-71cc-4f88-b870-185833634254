/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'hu', {
	alt: 'Alternatív szöveg',
	border: 'Keret',
	btnUpload: '<PERSON>üldés a szerverre',
	button2Img: 'Szeretne a kiválasztott képgombból sima képet csinálni?',
	hSpace: 'Vízsz. táv',
	img2Button: 'Szeretne a kiválasztott képből képgombot csinálni?',
	infoTab: 'Alaptulajdonságok',
	linkTab: 'Hivatkozás',
	lockRatio: 'Ar<PERSON>y megtart<PERSON>a',
	menu: 'Kép tulajdons<PERSON>gai',
	resetSize: 'Eredeti méret',
	title: '<PERSON>é<PERSON> tulaj<PERSON>',
	titleButton: 'Képgomb tulajdon<PERSON>',
	upload: 'Feltöltés',
	urlMissing: 'Hiányzik a kép URL-je.',
	vSpace: 'Függ. táv',
	validateBorder: 'A keret méretének egész számot kell beírni!',
	validateHSpace: 'Vízszintes távolságnak egész számot kell beírni!',
	validateVSpace: 'Függőleges távolságnak egész számot kell beírni!'
} );
