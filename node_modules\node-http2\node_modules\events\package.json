{"_from": "events@1.1.1", "_id": "events@1.1.1", "_inBundle": false, "_integrity": "sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==", "_location": "/node-http2/events", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "events@1.1.1", "name": "events", "escapedName": "events", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/node-http2"], "_resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "_shasum": "9ebdb7635ad099c70dcc4c2a1f5004288e8bd924", "_spec": "events@1.1.1", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\node-http2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeditoolkit.com"}, "bugs": {"url": "http://github.com/Gozala/events/issues/"}, "bundleDependencies": false, "deprecated": false, "description": "<PERSON>de's event emitter for all engines.", "devDependencies": {"mocha": "~1.21.4", "zuul": "~1.10.2"}, "engines": {"node": ">=0.4.x"}, "homepage": "https://github.com/Gozala/events#readme", "id": "events", "keywords": ["events", "eventEmitter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listeners"], "license": "MIT", "main": "./events.js", "name": "events", "repository": {"type": "git", "url": "git://github.com/Gozala/events.git", "web": "https://github.com/Gozala/events"}, "scripts": {"test": "mocha --ui qunit -- tests/index.js && zuul -- tests/index.js"}, "version": "1.1.1"}