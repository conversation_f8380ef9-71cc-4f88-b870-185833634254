'use strict';
module.exports = function (grunt) {
    // Time how long tasks take. Can help when optimizing build times
    require('time-grunt')(grunt);

    // Automatically load required grunt tasks
    require('jit-grunt')(grunt, {
        useminPrepare: 'grunt-usemin'
    });

    grunt.loadNpmTasks('grunt-contrib-connect');

    // Configurable paths
    var config = {
        html: 'html',
        dist: 'dist',
        uglifySrc: ['{,**/}js/testbank/*.js', '{,**/}js/util/*.js'], // 移除第三方库，避免破坏其原有结构
        target: '', // 目标项目，*代表全部项目 对应单个项目的文件夹
        src: 'cutTestPaper' // 源目录路径
    };

    var port = 6161;
    // Define the configuration for all the tasks
    grunt.initConfig({
        config: config,
        connect: {
            server: {
                options: {
                    port: port,
                    livereload: true,
                    keepalive: true,
                    // base: '.',
                    open: {
                        target: `http://localhost:${port}/cutTestPaper/matchQues.html?mk=iclass30`,
                        appName: 'open',
                    },
                }
            }
        },
        clean: {
            dist: {
                files: [{
                    dot: true,
                    src: [
                        '<%= config.dist %>/<%= config.target %>'
                    ]
                }]
            }
        },
        // Copies remaining files to places other tasks can use
        copy: {
            dist: {
                files: [{
                    expand: true,
                    dot: true,
                    cwd: '<%= config.src %>',
                    src: '**',
                    dest: '<%= config.dist %>/<%= config.target %>'
                }]
            }
        },
        cssmin: {
            dist: {
                files: [{
                    expand: true,
                    dot: true,
                    cwd: '<%= config.dist %>/<%= config.target %>',
                    src: ['{,**/}*.css', '!{,**/}js/thirdLib/**/*'],
                    dest: '<%= config.dist %>/<%= config.target %>'
                }]
            }
        },
        babel: {
            options: {
                sourceMap: false,
                presets: [
                    ['@babel/preset-env', {
                        // 禁用模块转换，避免自动添加严格模式
                        modules: false
                    }]
                ],
                "plugins": [
                    // 明确禁用严格模式转换
                    ["@babel/plugin-transform-strict-mode", { "strict": false }]
                ]
            },
            dist: {
                files: [{
                    expand: true,
                    cwd: '<%= config.dist %>/<%= config.target %>', //js目录下
                    src: ['{,**/}*.js', '!{,**/}js/thirdLib/**/*'], //所有js文件，排除第三方库
                    dest: '<%= config.dist %>/<%= config.target %>'  //输出到此目录下
                }]
            }
        },
        uglify: {
            options: {
                mangle: true, //混淆变量名
                comments: 'false' //false（删除全部注释），some（保留@preserve @license @cc_on等注释）
            },
            build: {
                files: [{
                    expand: true,
                    dot: true,
                    cwd: '<%= config.dist %>/<%= config.target %>',
                    src: config.uglifySrc,
                    dest: '<%= config.dist %>/<%= config.target %>'
                }]
            }
        },
        htmlmin: {
            dist: {
                options: {
                    removeComments: true,
                    collapseWhitespace: true,
                    minifyCSS: true,
                    minifyJS: true
                },
                files: [{
                    expand: true,
                    dot: true,
                    cwd: '<%= config.dist %>/<%= config.target %>',
                    dest: '<%= config.dist %>/<%= config.target %>',
                    src: ['{,**/}*.html', '!{,**/}js/thirdLib/**/*']
                }]
            }
        },
        concurrent: {
            dist: [
                'cssmin',
                'htmlmin',
                'babel',
                'uglify'
            ]
        }
    });

    grunt.registerTask('default', function (target) {
        grunt.task.run([
            'clean',
            'copy:dist',
            'concurrent:dist'
        ])
    });

    grunt.registerTask('dev', function (target) {
        grunt.task.run([
            'connect'
        ])
    });
};
