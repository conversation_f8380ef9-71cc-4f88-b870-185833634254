<!DOCTYPE HTML><html><head><title>题目设置</title><style>body,html{height:-webkit-fill-available}body{font-family:"Microsoft YaHei",serif;user-select:none}body,dd,dl,h1,h2,h3,h4,h5,h6,p{margin:0}li,ol,ul{margin:0;padding:0;list-style:none}img{border:none}.set-window{display:flex;height:-webkit-fill-available;width:100%;flex-wrap:wrap;align-items:flex-start;justify-content:center;border:1px solid #e3e8ee;box-sizing:border-box;border-radius:3px;background-color:#fff;padding:20px 27px 0;font-size:16px}.set-main{width:100%;padding:0}.set-main>.set-line{margin:0 0 15px;width:100%;display:flex;align-items:center;justify-content:flex-start;height:45px}.set-line{margin:12px 0;font-size:16px}.set-line-title{width:85px;text-align:left;color:#4a4a4a}.split-icon{margin:0 20px}.set-select{width:150px;height:36px;line-height:36;cursor:pointer;text-indent:20px;font-size:16px;border:1px solid #d9ddea}.set-input{width:150px;height:32px;line-height:32;text-indent:20px;font-size:16px;border:1px solid #d9ddea}#set-ques-name{width:350px}select[disabled=disabled]{cursor:no-drop}option{font-size:16px;cursor:pointer}input[type=checkbox]{opacity:1}input[type=checkbox]:after{position:relative;top:-2px;width:15px;height:20px;content:'';display:block;background:url(../images/assign/select_icon.png) no-repeat 0 -54px/100%;cursor:pointer}input[type=checkbox]:hover:after{background-position-y:-110px}input[type=checkbox].checked:after,input[type=checkbox].checked:hover:after{background-position-y:1px}label{cursor:pointer;margin-left:5px}.set-operation{position:absolute;bottom:0;height:70px;border-top:1px solid #d9ddea;width:100%;display:flex;align-items:center;justify-content:flex-end}.set-operation a{width:130px;height:34px;border:1px solid #d9ddea;border-radius:3px;margin:0 20px;line-height:34px;text-align:center;font-size:16px;cursor:pointer}.set-operation a.cancel{background-color:#fff;color:#878787}.set-operation a.cancel:hover{background-color:#f6f7fa}.set-operation a.set-operation-ok{background-color:#3e73f6;color:#fff}.set-operation a.set-operation-ok:hover{background-color:#3166ea}</style></head><body><div class="set-window"><div class="set-main"><div class="set-line set-ans-or-ques"><input class="" type="checkbox" id="set-ques-isAns" name="set-ques-isAns" value=""> <label for="set-ques-isAns">标记为答案解析</label></div><div class="set-line set-ques-type"><span class="set-line-title">题型</span> <select class="set-select"><option value="8">单选题</option><option value="1">多选题</option><option value="2">判断题</option><option value="3">填空题</option><option value="6">简答题</option><option value="7">填空题智批</option></select></div><div class="set-line set-ques-name"><span class="set-line-title">题目名称</span> <input type="text" id="set-ques-name" class="set-input" name="set-ques-name" value="" autocomplete="off"></div><div class="set-line set-ques-option"><span class="set-line-title">选项</span> <select class="set-select"><option value="2">2</option><option value="3">3</option><option selected="selected" value="4">4</option><option value="5">5</option><option value="6">6</option><option value="7">7</option><option value="8">8</option><option value="9">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option></select></div><div class="set-line set-ques-sort"><span class="set-line-title">题号</span><div class="sinput-one short"><input type="text" class="set-input set-num-start"></div><span class="split-icon">-</span><div class="sinput-one short"><input type="text" class="set-input set-num-end"></div></div><div class="set-line mergeQues" style="display:none"><input class="" type="checkbox" id="set-ques-merge" name="set-ques-merge" value=""> <label for="set-ques-merge">合并题目</label></div></div><div class="set-operation"><a class="cancel">取消</a> <a class="set-operation-ok">确定</a></div></div></body></html><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script type="text/javascript">$(function(){function e(e){e=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),e=window.location.search.substr(1).match(e);return null!=e?decodeURIComponent(e[2]):null}var s=!1,t=8,u=e("title")||"单选题",i=4,n=!1,a=1,c=1,o=!1,l=!1;$("#set-ques-isAns").change(function(){this.checked?($(this).addClass("checked"),$(".set-ques-sort,.set-ques-title,.set-ques-type,.set-ques-name").hide(),$(".set-ques-type .set-select").attr("disabled",!0),"1"!=t&&"8"!=t||$(".set-ques-option").hide(),"7"==t&&$(".set-correct-type").hide()):($(this).removeClass("checked"),$(".set-ques-sort,.set-ques-title,.set-ques-type,.set-ques-name").show(),$(".set-ques-type .set-select").attr("disabled",!1),"1"!=t&&"8"!=t||$(".set-ques-option").show(),"7"==t&&$(".set-correct-type").show()),s=this.checked}),$("#set-ques-merge").change(function(){this.checked?$(this).addClass("checked"):$(this).removeClass("checked"),l=this.checked}),$(".set-ques-type .set-select").change(function(){t=this.value,"1"==this.value||"8"==this.value?$(".set-ques-option").show():$(".set-ques-option").hide(),"7"==this.value?$(".set-correct-type").show():$(".set-correct-type").hide(),"3"==this.value||"6"==this.value?$(".mergeQues").show():$(".mergeQues").hide(),["单选题","多选题","判断题","填空题","简答题","填空题智批"].includes($("#set-ques-name").val())&&$("#set-ques-name").val(this.selectedOptions[0].text)}),$(".set-ques-option .set-select").change(function(){i=this.value}),$("#set-ques-isAuto").change(function(){n=this.checked}),$("#set-ques-name").on("input",function(){e=this.value;var e=e=(e=(e+="").replace(/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,"")).replace(/(^(\\\s+)*)|((\\\s+)*$)/g,"");$(this).val(e)}),$(".set-operation-ok").click(function(){o||(o=!0,a=$(".set-ques-sort .set-num-start").val(),c=$(".set-ques-sort .set-num-end").val(),u=$("#set-ques-name").val(),s||0!=a&&0!=c&&""!=a&&""!=c&&""!==u?parent.CutHtml.batch_parseDataToCutData({isAns:s,quesType:s?-1:t,quesName:u,quesOption:i,isFill:n,startSort:a,endSort:c,isMerge:l}):parent.layer.msg("题目不正确，请确认!"))}),$(".cancel").click(function(){parent.layer.closeAll()}),a=e("startSort"),c=e("endSort"),$(".set-ques-sort .set-num-start").val(a),$(".set-ques-sort .set-num-end").val(c),$("#set-ques-name").val(u),"true"==e("isBigStem")&&$("#set-ques-isAns").click()})</script>