<!DOCTYPE html><head><meta charset="UTF-8"><title>课堂3.0-制卷</title><link rel="stylesheet" href="css/base.css"><link rel="stylesheet" href="css/testbank/common.css"><link rel="stylesheet" href="css/testbank/testbank.css"><link rel="stylesheet" href="js/thirdLib/layui/css/layui.css"><link rel="stylesheet" href="css/avalonComponent.css"><link rel="stylesheet" href="css/testbank/matchAns.css"></head><body><div><div class="header-mask" id="header-mask"><div class="loading-cover"></div><div class="loading-box"><div class="img"></div><div class="loading-txt"></div></div></div><div id="matchingPrompt" style="display:none"><div class="cover"></div><div class="check_win"><div class="check_main"><span class="check_tip">完成匹配，请复核</span></div></div></div><div class="main"><div class="cur-opr-wrap"><div class="cur-opr-box"><div class="btn-box-top" style=""><div class="tip-box"><div class="tip"><span class="blue"></span> <a>: 题目</a></div><div class="tip"><span class="red"></span> <a>: 答案解析</a></div></div><div class="siblin"><span class="topButton next" id="done"><span class="txt">下一步</span></span></div><div class="siblin-blue"></div><span class="tip"><span class="txt">自动匹配有误或未匹配的点此更改哦！</span> <a class="close"></a> <span class="arrow"></span></span><div class="skip topButton return" id="return"><span class="txt">上一步</span></div></div></div></div><div class="cut-main"><div class="cut-left"><div class="word-area"><h1 class="testBank-title" id="testPaperName"></h1><div id="htmlDiv" class="cutImageArea"></div></div></div><div class="cut-right"><div id="topicMask" class="set-que-win-bg" style="display:none"></div><div class="r-hd-box"><div class="lib-tip-close"><span class="txt">请核对答案与解析的匹配结果</span></div><span class="icon"></span><div class="blue-tip-line"></div><span class="txt">答案解析预览</span> <span class="topButton" id="student-preview">学生预览</span></div><div class="check-tip"><span class="txt">请核对答案与解析的匹配结果</span></div><div id="topicPreview"></div></div><div class="clearfix"></div></div><div id="topicSetting"></div></div><script id="bqHtml" type="text/html"><div class="question-show">
            {{# for(var i=0,l=d.length;i<l;i++){}}
            <div id="bq_{{d[i].Id}}" class="bqload">
                <div class="bqname">{{d[i].Name}}</div>
                {{# if(d[i].CutHtmlUrl==""){}}
                {{#}else{}}
                <!--题干展示 样式写在stemload上 htmlload为全局需要用的-->
                <div class='sliceMainClass'>
                    <div class='selectedContent htmlload' data-htmlurl='{{d[i].CutHtmlUrl}}'></div>
                    <div class='sliceMaskLayer set'></div>
                </div>
                {{#}}}
                <div class="questionload">
                    {{#for(var j=0,qLen=d[i].qList.length;j<qLen;j++){ }}
                    <div id="q_{{d[i].qList[j].Id}}" class='sliceMainClass question'
                         data-type="{{d[i].qList[j].TypeCode}}" data-qSort="{{d[i].qList[j].qSrot}}">
                        <div class='selectedContent htmlload' data-htmlurl="{{d[i].qList[j].CutHtmlUrl}}"></div>
                        <div class='sliceMaskLayer set'></div>
                        <div class="pre-next">
                            {{# if(i==0&&j==0){}}
                            <a class="selectQuestion next">下一题</a>
                            {{#}else{}}
                            {{# if(i==l-1&&j==qLen-1){}}
                            <a class="selectQuestion pre">上一题</a>
                            <a class="selectQuestion next nextend">完成</a>
                            {{#}else{}}
                            <a class="selectQuestion pre">上一题</a>
                            <a class="selectQuestion next">下一题</a>
                            {{#}}}
                            {{#}}}
                        </div>
                    </div>

                    {{# if(/[1278]/.test(d[i].qList[j].TypeCode)){}}
                    <div id="answer_{{d[i].qList[j].Id}}" class='sliceMainClass question-answer '>
                        <div class='selectedContent addBefore'>
                            {{# if(d[i].qList[j].TypeCode=="7"){}}
                            <div class="fillCorrect" data-id='{{d[i].qList[j].Id}}' data-json='{{d[i].qList[j].Answer}}'></div>
                            {{#}else if(d[i].qList[j].TypeCode=="2"){}}
                            {{# if(d[i].qList[j].Answer=="A"){}}
                            正确
                            {{#}else{}}
                            错误
                            {{#}}}
                            {{#}else{}}
                            <div>{{d[i].qList[j].Answer}}</div>
                            {{#}}}
                        </div>
                        <div class='sliceMaskLayer'></div>
                        <div class="cutset showset">
                            <a class="setclass setcompleted objective">答案</a>
                        </div>
                    </div>
                    {{#}else if(d[i].qList[j].AnswerHtmlId!=""){}}
                    <div id="answer_{{d[i].qList[j].Id}}" class='sliceMainClass question-answer'>
                        <div class='selectedContent htmlload' data-htmlurl="{{d[i].qList[j].AnswerHtmlUrl}}"></div>
                        <div class='sliceMaskLayer'></div>
                        <div class="cutset showset">
                            <a class="setclass setcompleted subjective">答案</a>
                        </div>
                    </div>
                    {{#}else if(d[i].qList[j].AnswerHtmlId==""){ }}

                    <div id="set_answer_{{d[i].qList[j].Id}}" class='sliceMainClass set-question-answer'>
                        <div class="selectedContent setanswer">
                            <a>设置答案</a>
                        </div>
                        <div class="sliceMaskLayer"></div>
                    </div>
                    {{#}}}
                    {{# if(d[i].qList[j].ParsHtmlId!=""){ }}

                    <div id="analysis_{{d[i].qList[j].Id}}" class='sliceMainClass question-analysis'>
                        <div class='selectedContent htmlload' data-htmlurl="{{d[i].qList[j].ParsHtmlUrl}}"></div>
                        <div class='sliceMaskLayer'></div>
                        <div class="cutset showset">
                            <a class="setclass setcompleted">解析</a>
                        </div>
                    </div>



                    {{#}else{ }}
                    <div id="set_analysis_{{d[i].qList[j].Id}}" class='sliceMainClass set-question-analysis'>
                        <div class="selectedContent setanalysis">
                            <a>设置解析</a>
                        </div>
                        <div class="sliceMaskLayer"></div>
                    </div>
                    {{#}}}
                    {{#}}}
                </div>
            </div>
            {{#}}}
        </div></script><script id="previewHtml" type="text/html">{{# for(var i=0,l=d.length;i
        <l;i++){}}
        <div id="bq_{{d[i].Id}}" class="preview_bqload">
            <!--大题名称-->
            <div class="preview_tit">{{d[i].Name}}</div>
            <div class="preview_questionload">
                {{#for(var j=0,qLen=d[i].qList.length;j
                <qLen
                ;j++){ }}
                <div class="sub" id="p_{{d[i].qList[j].Id}}">
                    <span class="sort">{{d[i].qList[j].Number}}</span>
                    <span class='answer {{# if(d[i].qList[j].Answer!=""||d[i].qList[j].AnswerHtmlId!=""){}}exist{{#}}}'>答案</span>
                    <span class='analysis {{# if(d[i].qList[j].ParsHtmlId!=""){}}exist{{#}}}'>解析</span>
                </div>
                {{#}}}
            </div>
        </div>
        {{#}}}</script><script id="cutHtml" type="text/html"><div class="cut-show">
    {{# if(d.list.length>0){ }}
        {{# if(d.type == 1){ }}
            <div class="close-cutHtml">收起答案</div>
        {{#}else{}}
            <div class="close-cutHtml">收起解析</div>
        {{#}}}

        {{# for(var i=0,l=d.list.length;i
        <l;i++){}}
        <div id="c_{{d.list[i].Id}}" class='sliceMainClass cuthtml'>
            <div class='selectedContent htmlload' data-htmlurl="{{d.list[i].HtmlUrl}}"></div>
            <div class='sliceMaskLayer'></div>
            <div class="cutset waitset">
                {{# if(d.type == 1){ }}
                <a class="setclass setanswer">设为答案</a>
                {{#}else{}}
                <a class="setclass setanalysis">设为解析</a>
                {{#}}}
            </div>
        </div>
        {{#}}}
        {{#}else{}}
        <div class='sliceMainClass cuthtml'>
            <div class='selectedContent nodata'>
                <a>暂无相关内容，可将答案解析添加到word中再上传~</a>
            </div>
            <div class='sliceMaskLayer'></div>
        </div>
        {{#}}}
    </div></script><script id="answerAnalysisHtml" type="text/html">{{# if(d.type=="1"){}}
        <div id="answer_{{d.qId}}" class='sliceMainClass question-answer'>
            <div class='selectedContent htmlload' data-htmlurl="{{d.cutHtml}}"></div>
            <div class='sliceMaskLayer'></div>
            <div class="cutset showset">
                <a class="setclass setcompleted">答案</a>
            </div>
        </div>
        {{#}else{}}
        <div id="analysis_{{d.qId}}" class='sliceMainClass question-analysis'>
            <div class='selectedContent htmlload' data-htmlurl="{{d.cutHtml}}"></div>
            <div class='sliceMaskLayer'></div>
            <div class="cutset showset">
                <a class="setclass setcompleted">解析</a>
            </div>
        </div>
        {{#}}}</script></div><script defer="defer" type="text/javascript" src="https://fs.iclass30.com/aliba/plug/mathjax/MathJax.js"></script><script type="text/x-mathjax-config">MathJax.Hub.Register.StartupHook("TeX Jax Ready", function () {
            var TEX = MathJax.InputJax.TeX;
            // 注册预处理钩子，在处理 TeX 公式前执行
            TEX.prefilterHooks.Add(function (data) {
                // 判断是否是行内公式（非 display 模式）
                if (!data.display) {
                    // 给行内公式前面添加 \displaystyle 
                    data.math = '\\displaystyle ' + data.math;
                }
                return data;
            });
        });
        MathJax.Hub.Config({
            showProcessingMessages: false,
            messageStyle: "none",
            menuSettings: {
              context: "Browser"
            },
            "HTML-CSS": {
              webFont: "STIX-Web",
              availableFonts: ["STIX-Web"],
              preferredFont: "STIX-Web",
              styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
              undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
              scale:110
            },
            "CommonHTML": {
              webFont: "STIX-Web",
              availableFonts: ["STIX-Web"],
              preferredFont: "STIX-Web",
              styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
              undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
              scale:110
            },
            tex2jax: {
              inlineMath: [['$$','$$'],["\\(","\\)"],["\\[", "\\]"]],
              processEscapes: true,
              skipTags: ['script', 'style',  'pre', 'code']
            },
            MMLorHTML: {prefer: "HTML"},
            jax: ["input/TeX","output/CommonHTML"],
            extensions: ["tex2jax.js","mml2jax.js","MathMenu.js","MathZoom.js", "fast-preview.js", "AssistiveMML.js"],
            TeX: {
                extensions: ["extpfeil.js","AMSmath.js","AMSsymbols.js","noErrors.js","noUndefined.js","cancel.js","mhchem.js","autoload-all.js"]
            },
            font:"STIX-Web",
      
            SVG: {
              scale: 110,
              linebreaks: {
                   automatic: true
              },
              addMMLclasses: false
            }
          });</script><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script src="js/util/common.js"></script><script src="js/util/domHelp.js"></script><script src="js/util/ajax.js"></script><script src="js/thirdLib/layui/layui.js"></script><script src="js/thirdLib/layui/layui-config.js"></script><script src="js/thirdLib/avalon/avalon.js"></script><script src="js/thirdLib/avalon/component.js"></script><script src="js/testbank/matchAnsStyle.js"></script><script src="js/testbank/matchAns.js"></script><script src="js/thirdLib/disable-devtool.js"></script></body><script>DisableDevtool({md5:"27365a67be2217aa30a5e690d6937273",tkName:"mk"})</script>