"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang('image', 'de', {
  alt: 'Alternativer Text',
  border: 'Rahmen',
  btnUpload: 'Zum Server senden',
  button2Img: 'Möchten Sie die ausgewählte Bildschaltfläche in ein einfaches Bild umwandeln?',
  hSpace: 'Horizontal-Abstand',
  img2Button: 'Möchten Sie das ausgewählte Bild in eine Bildschaltfläche umwandeln?',
  infoTab: 'Bildinfo',
  linkTab: 'Link',
  lockRatio: 'Größenverhältnis beibehalten',
  menu: 'Bildeigenschaften',
  resetSize: 'Größe zurücksetzen',
  title: 'Bildeigenschaften',
  titleButton: 'Bildschaltflächeneigenschaften',
  upload: 'Hochladen',
  urlMissing: 'Bildquellen-URL fehlt.',
  vSpace: 'Vertikal-Abstand',
  validateBorder: 'Rahm<PERSON> muss eine ganze Zahl sein.',
  validateHSpace: 'Horizontal-Abstand muss eine ganze Zahl sein.',
  validateVSpace: 'Vertikal-Abstand muss eine ganze Zahl sein.'
});
