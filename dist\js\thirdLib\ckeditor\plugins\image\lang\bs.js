/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'bs', {
	alt: 'Tekst na slici',
	border: '<PERSON>vir',
	btnUpload: 'Š<PERSON><PERSON> na server',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'HSpace',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Info slike',
	linkTab: 'Link',
	lockRatio: 'Zaklju<PERSON>aj odnos',
	menu: 'Svojstva slike',
	resetSize: 'Resetuj dimenzije',
	title: 'Svojstva slike',
	titleButton: 'Image Button Properties', // MISSING
	upload: 'Š<PERSON>ji',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'VSpace',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );
