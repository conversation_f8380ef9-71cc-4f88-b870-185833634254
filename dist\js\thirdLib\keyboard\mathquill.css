@font-face{font-family:Symbola;src:url(font/Symbola.eot);src:local("Symbola Regular"),local("Symbola"),url(Symbola.woff) format("woff"),url(Symbola.woff) format("woff"),url(font/Symbola.ttf) format("truetype"),url(font/Symbola.otf) format("opentype"),url(font/Symbola.svg#Symbola) format("svg")}.mq-editable-field{display:-moz-inline-box;display:inline-block}.mq-editable-field .mq-cursor{border-left:1px solid #000;margin-left:-1px;position:relative;z-index:1;padding:0;display:-moz-inline-box;display:inline-block}.mq-editable-field .mq-cursor.mq-blink{visibility:hidden}.mq-math-mode .mq-editable-field{margin:1px}.mq-editable-field .mq-latex-command-input{color:inherit;font-family:"Courier New",monospace;border:1px solid gray;padding-right:1px;margin-right:1px;margin-left:2px}.mq-editable-field .mq-latex-command-input.mq-empty{background:0 0}.mq-editable-field .mq-latex-command-input.mq-hasCursor{border-color:ActiveBorder}.mq-editable-field.mq-empty:after,.mq-editable-field.mq-text-mode:after,.mq-math-mode .mq-empty:after{visibility:hidden;content:'c'}.mq-editable-field .mq-cursor:only-child:after,.mq-editable-field .mq-textarea+.mq-cursor:last-child:after{visibility:hidden;content:'c'}.mq-editable-field .mq-text-mode .mq-cursor:only-child:after{content:''}.mq-editable-field.mq-text-mode{overflow-x:auto;overflow-y:hidden}.mq-math-mode .mq-root-block,.mq-root-block{display:-moz-inline-box;display:inline-block;padding:8px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;white-space:nowrap;overflow:hidden;vertical-align:middle}.mq-math-mode{font-variant:normal;font-weight:400;font-style:normal;font-size:115%;line-height:1;display:-moz-inline-box;display:inline-block;min-height:20px}.mq-math-mode .mq-non-leaf,.mq-math-mode .mq-scaled{display:-moz-inline-box;display:inline-block}.mq-math-mode .mq-nonSymbola,.mq-math-mode .mq-text-mode,.mq-math-mode var{font-family:"Times New Roman",Symbola,serif;line-height:.9}.mq-math-mode *{font-size:inherit;line-height:inherit;margin:0;padding:0;border-color:#000;-webkit-user-select:none;-moz-user-select:none;user-select:none;box-sizing:border-box}.mq-math-mode .mq-empty{background:#ccc}.mq-math-mode .mq-empty.mq-root-block{background:0 0}.mq-math-mode.mq-empty{background:0 0}.mq-math-mode .mq-text-mode{display:inline-block}.mq-math-mode .mq-text-mode.mq-hasCursor{box-shadow:inset #a9a9a9 0 .1em .2em;padding:0 .1em;margin:0 -.1em;min-width:1ex}.mq-math-mode .mq-font{font:1em "Times New Roman",Symbola,serif}.mq-math-mode .mq-font *{font-family:inherit;font-style:inherit}.mq-math-mode b,.mq-math-mode b.mq-font{font-weight:bolder}.mq-math-mode i,.mq-math-mode i.mq-font,.mq-math-mode var{font-style:italic}.mq-math-mode var.mq-f{margin-right:.2em;margin-left:.1em}.mq-math-mode .mq-roman var.mq-f{margin:0}.mq-math-mode big{font-size:125%}.mq-math-mode .mq-roman{font-style:normal}.mq-math-mode .mq-sans-serif{font-family:sans-serif,Symbola,serif}.mq-math-mode .mq-monospace{font-family:monospace,Symbola,serif}.mq-math-mode .mq-overline{border-top:1px solid #000;margin-top:1px}.mq-math-mode .mq-underline{border-bottom:1px solid #000;margin-bottom:1px}.mq-math-mode .mq-binary-operator{padding:0 .2em;display:-moz-inline-box;display:inline-block}.mq-math-mode .mq-supsub{font-size:90%;vertical-align:-.5em}.mq-math-mode .mq-supsub.mq-limit{font-size:80%;vertical-align:-.4em}.mq-math-mode .mq-supsub.mq-sup-only{vertical-align:.5em}.mq-math-mode .mq-supsub.mq-sup-only .mq-sup{display:inline-block;vertical-align:text-bottom}.mq-math-mode .mq-supsub .mq-sup{display:block}.mq-math-mode .mq-supsub .mq-sub{display:block;float:left}.mq-math-mode .mq-supsub.mq-limit .mq-sub{margin-left:-.25em}.mq-math-mode .mq-supsub .mq-binary-operator{padding:0 .1em}.mq-math-mode .mq-supsub .mq-fraction{font-size:70%}.mq-math-mode sup.mq-nthroot{font-size:80%;vertical-align:.8em;margin-right:-.6em;margin-left:.2em;min-width:.5em}.mq-math-mode .mq-paren{padding:0 .1em;vertical-align:top;-webkit-transform-origin:center .06em;-moz-transform-origin:center .06em;-ms-transform-origin:center .06em;-o-transform-origin:center .06em;transform-origin:center .06em}.mq-math-mode .mq-paren.mq-ghost{color:silver}.mq-math-mode .mq-paren+span{margin-top:.1em;margin-bottom:.1em}.mq-math-mode .mq-array{vertical-align:middle;text-align:center}.mq-math-mode .mq-array>span{display:block}.mq-math-mode .mq-operator-name{line-height:.9;font-style:normal}.mq-math-mode var.mq-operator-name.mq-first{padding-left:.2em}.mq-math-mode var.mq-operator-name.mq-last{padding-right:.2em}.mq-math-mode .mq-fraction{font-size:90%;text-align:center;vertical-align:-.4em;padding:0 .2em}.mq-math-mode .mq-fraction,.mq-math-mode .mq-large-operator,.mq-math-mode x:-moz-any-link{display:-moz-groupbox}.mq-math-mode .mq-fraction,.mq-math-mode .mq-large-operator,.mq-math-mode x:-moz-any-link,.mq-math-mode x:default{display:inline-block}.mq-math-mode .mq-denominator,.mq-math-mode .mq-numerator{display:block}.mq-math-mode .mq-numerator{padding:0 .1em}.mq-math-mode .mq-denominator{border-top:1px solid;float:right;width:100%;padding:.1em}.mq-math-mode .mq-sqrt-prefix{padding-top:0;position:relative;top:.1em;vertical-align:top;-webkit-transform-origin:top;-moz-transform-origin:top;-ms-transform-origin:top;-o-transform-origin:top;transform-origin:top}.mq-math-mode .mq-sqrt-stem{border-top:1px solid;margin-top:1px;padding-left:.15em;padding-right:.2em;margin-right:.1em;padding-top:1px}.mq-math-mode .mq-vector-prefix{display:block;text-align:center;line-height:.25em;margin-bottom:-.1em;font-size:.75em}.mq-math-mode .mq-vector-stem{display:block}.mq-math-mode .mq-large-operator{text-align:center}.mq-math-mode .mq-large-operator .mq-from,.mq-math-mode .mq-large-operator .mq-to,.mq-math-mode .mq-large-operator big{display:block}.mq-math-mode .mq-large-operator .mq-from,.mq-math-mode .mq-large-operator .mq-to{font-size:80%}.mq-math-mode .mq-large-operator .mq-from{float:right;width:100%}.mq-math-mode,.mq-math-mode .mq-editable-field{cursor:text}.mq-math-mode .mq-overarrow{border-top:1px solid #000;margin-top:1px;padding-top:.2em}.mq-math-mode .mq-overarrow:before{display:block;position:relative;top:-.34em;font-size:.5em;line-height:0;content:'\27A4';text-align:right}.mq-math-mode .mq-overarrow.mq-arrow-left:before{-moz-transform:scaleX(-1);-o-transform:scaleX(-1);-webkit-transform:scaleX(-1);transform:scaleX(-1);filter:FlipH;-ms-filter:"FlipH"}.mq-editable-field .mq-selection,.mq-editable-field .mq-selection .mq-non-leaf,.mq-editable-field .mq-selection .mq-scaled,.mq-math-mode .mq-selection,.mq-math-mode .mq-selection .mq-non-leaf,.mq-math-mode .mq-selection .mq-scaled{background:#b4d5fe!important;background:Highlight!important;color:HighlightText;border-color:HighlightText}.mq-editable-field .mq-selection .mq-matrixed,.mq-math-mode .mq-selection .mq-matrixed{background:#39f!important}.mq-editable-field .mq-selection.mq-blur,.mq-editable-field .mq-selection.mq-blur .mq-matrixed,.mq-editable-field .mq-selection.mq-blur .mq-non-leaf,.mq-editable-field .mq-selection.mq-blur .mq-scaled,.mq-math-mode .mq-selection.mq-blur,.mq-math-mode .mq-selection.mq-blur .mq-matrixed,.mq-math-mode .mq-selection.mq-blur .mq-non-leaf,.mq-math-mode .mq-selection.mq-blur .mq-scaled{background:#d4d4d4!important;color:#000;border-color:#000}.mq-editable-field .mq-textarea,.mq-math-mode .mq-textarea{position:relative;-webkit-user-select:text;-moz-user-select:text;user-select:text}.mq-editable-field .mq-selectable,.mq-editable-field .mq-textarea *,.mq-math-mode .mq-selectable,.mq-math-mode .mq-textarea *{-webkit-user-select:text;-moz-user-select:text;user-select:text;position:absolute;clip:rect(1em 1em 1em 1em);-webkit-transform:scale(0);-moz-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0);resize:none;width:1px;height:1px}.mq-math-mode .mq-matrixed{background:#fff;display:-moz-inline-box;display:inline-block}.mq-math-mode .mq-matrixed-container{margin-top:-.1em}