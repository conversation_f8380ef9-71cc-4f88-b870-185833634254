!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.MathMLToLaTeX=t():e.MathMLToLaTeX=t()}(this,()=>{return a={2167:(e,t)=>{"use strict";function r(e,t){return(t=void 0===t?Object:t)&&"function"==typeof t.freeze?t.freeze(e):e}var a=r({HTML:"text/html",isHTML:function(e){return e===a.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),n=r({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===n.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});t.assign=function(e,t){if(null===e||"object"!=typeof e)throw new TypeError("target is not an object");for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},t.find=function(e,t,r){if(void 0===r&&(r=Array.prototype),e&&"function"==typeof r.find)return r.find.call(e,t);for(var a=0;a<e.length;a++)if(Object.prototype.hasOwnProperty.call(e,a)){var n=e[a];if(t.call(void 0,n,a,e))return n}},t.freeze=r,t.MIME_TYPE=a,t.NAMESPACE=n},6129:(e,t,r)=>{var a=r(2167),n=r(1146),c=r(1045),r=r(6925),o=n.DOMImplementation,u=a.NAMESPACE,i=r.ParseError,h=r.XMLReader;function d(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function l(e){this.options=e||{locator:{}}}function m(){this.cdata=!1}function p(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function x(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function s(e,t,r){return"string"==typeof e?e.substr(t,r):e.length>=t+r||t?new java.lang.String(e,t,r)+"":e}function f(e,t){(e.currentElement||e.doc).appendChild(t)}l.prototype.parseFromString=function(e,t){var r=this.options,a=new h,i=r.domBuilder||new m,n=r.errorHandler,o=r.locator,l=r.xmlns||{},t=/\/x?html?$/.test(t),s=t?c.HTML_ENTITIES:c.XML_ENTITIES,n=(o&&i.setDocumentLocator(o),a.errorHandler=function(a,n){if(!a){if(i instanceof m)return i;a=i}var e={},o=a instanceof Function;function t(t){var r=a[t];!r&&o&&(r=2==a.length?function(e){a(t,e)}:a),e[t]=r?function(e){r("[xmldom "+t+"]\t"+e+x(n))}:function(){}}return n=n||{},t("warning"),t("error"),t("fatalError"),e}(n,o),a.domBuilder=r.domBuilder||i,t&&(l[""]=u.HTML),l.xml=l.xml||u.XML,r.normalizeLineEndings||d);return e&&"string"==typeof e?a.parse(n(e),l,s):a.errorHandler.error("invalid doc source"),i.doc},m.prototype={startDocument:function(){this.doc=(new o).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,r,a){var n=this.doc,o=n.createElementNS(e,r||t),i=a.length;f(this,o),this.currentElement=o,this.locator&&p(this.locator,o);for(var l=0;l<i;l++){e=a.getURI(l);var s=a.getValue(l),c=(r=a.getQName(l),n.createAttributeNS(e,r));this.locator&&p(a.getLocator(l),c),c.value=c.nodeValue=s,o.setAttributeNode(c)}},endElement:function(e,t,r){var a=this.currentElement;a.tagName,this.currentElement=a.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){e=this.doc.createProcessingInstruction(e,t);this.locator&&p(this.locator,e),f(this,e)},ignorableWhitespace:function(e,t,r){},characters:function(e,t,r){var a;(e=s.apply(this,arguments))&&(a=this.cdata?this.doc.createCDATASection(e):this.doc.createTextNode(e),this.currentElement?this.currentElement.appendChild(a):/^\s*$/.test(e)&&this.doc.appendChild(a),this.locator)&&p(this.locator,a)},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,r){e=s.apply(this,arguments);var a=this.doc.createComment(e);this.locator&&p(this.locator,a),f(this,a)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,r){var a=this.doc.implementation;a&&a.createDocumentType&&(a=a.createDocumentType(e,t,r),this.locator&&p(this.locator,a),f(this,a),this.doc.doctype=a)},warning:function(e){console.warn("[xmldom warning]\t"+e,x(this.locator))},error:function(e){console.error("[xmldom error]\t"+e,x(this.locator))},fatalError:function(e){throw new i(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){m.prototype[e]=function(){return null}}),t.DOMParser=l},1146:(k,e,t)=>{var t=t(2167),i=t.find,v=t.NAMESPACE;function P(e){return""!==e}function R(e,t){return e.hasOwnProperty(t)||(e[t]=!0),e}function s(e){return e?(e=e?e.split(/[\t\n\f\r ]+/).filter(P):[],Object.keys(e.reduce(R,{}))):[]}function n(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function r(e,t){var r=e.prototype;function a(){}r instanceof t||(a.prototype=t.prototype,n(r,a=new a),e.prototype=r=a),r.constructor!=e&&("function"!=typeof e&&console.error("unknown Class:"+e),r.constructor=e)}var t={},a=(t.ELEMENT_NODE=1,t.ATTRIBUTE_NODE=2,t.TEXT_NODE=3,t.CDATA_SECTION_NODE=4,t.ENTITY_REFERENCE_NODE=5,t.ENTITY_NODE=6,t.PROCESSING_INSTRUCTION_NODE=7,t.COMMENT_NODE=8,t.DOCUMENT_NODE=9,t.DOCUMENT_TYPE_NODE=10,t.DOCUMENT_FRAGMENT_NODE=11,t.NOTATION_NODE=12,{}),o={},c=(a.INDEX_SIZE_ERR=(o[1]="Index size error",1),a.DOMSTRING_SIZE_ERR=(o[2]="DOMString size error",2),a.HIERARCHY_REQUEST_ERR=(o[3]="Hierarchy request error",3));a.WRONG_DOCUMENT_ERR=(o[4]="Wrong document",4),a.INVALID_CHARACTER_ERR=(o[5]="Invalid character",5),a.NO_DATA_ALLOWED_ERR=(o[6]="No data allowed",6),a.NO_MODIFICATION_ALLOWED_ERR=(o[7]="No modification allowed",7),a.NOT_FOUND_ERR=(o[8]="Not found",8),a.NOT_SUPPORTED_ERR=(o[9]="Not supported",9),a.INUSE_ATTRIBUTE_ERR=(o[10]="Attribute in use",10);function u(e,t){var r;return t instanceof Error?r=t:(r=this,Error.call(this,o[e]),this.message=o[e],Error.captureStackTrace&&Error.captureStackTrace(this,u)),r.code=e,t&&(this.message=this.message+": "+t),r}function d(){}function h(e,t){this._node=e,this._refresh=t,l(this)}function l(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!==t){var r=e._refresh(e._node);if(ee(e,"length",r.length),!e.$$length||r.length<e.$$length)for(var a=r.length;a in e;a++)Object.prototype.hasOwnProperty.call(e,a)&&delete e[a];n(r,e),e._inc=t}}function m(){}function p(e,t){for(var r=e.length;r--;)if(e[r]===t)return r}function I(e,t,r,a){a?t[p(t,a)]=r:t[t.length++]=r,e&&(t=(r.ownerElement=e).ownerDocument)&&(a&&V(t,e,a),a=e,e=r,t&&t._inc++,e.namespaceURI===v.XMLNS)&&(a._nsMap[e.prefix?e.localName:""]=e.value)}function U(e,t,r){var a=p(t,r);if(!(0<=a))throw new u(8,new Error(e.tagName+"@"+r));for(var n,o=t.length-1;a<o;)t[a]=t[++a];t.length=o,e&&(n=e.ownerDocument)&&(V(n,e,r),r.ownerElement=null)}function j(){}function x(){}function G(e){return("<"==e?"&lt;":">"==e&&"&gt;")||("&"==e?"&amp;":'"'==e&&"&quot;")||"&#"+e.charCodeAt()+";"}function f(e,t){if(t(e))return 1;if(e=e.firstChild)do{if(f(e,t))return 1}while(e=e.nextSibling)}function g(){this.ownerDocument=this}function V(e,t,r){e&&e._inc++,r.namespaceURI===v.XMLNS&&delete t._nsMap[r.prefix?r.localName:""]}function w(e,t,r){if(e&&e._inc){e._inc++;var a=t.childNodes;if(r)a[a.length++]=r;else{for(var n=t.firstChild,o=0;n;)n=(a[o++]=n).nextSibling;a.length=o,delete a[a.length]}}}function X(e,t){var r=t.previousSibling,a=t.nextSibling;return r?r.nextSibling=a:e.firstChild=a,a?a.previousSibling=r:e.lastChild=r,t.parentNode=null,t.previousSibling=null,t.nextSibling=null,w(e.ownerDocument,e),t}function b(e){return e&&e.nodeType===x.DOCUMENT_TYPE_NODE}function C(e){return e&&e.nodeType===x.ELEMENT_NODE}function A(e){return e&&e.nodeType===x.TEXT_NODE}function $(e,t){var r,e=e.childNodes||[];if(!i(e,C)&&!b(t))return r=i(e,b),!(t&&r&&e.indexOf(r)>e.indexOf(t))}function H(e,t){var r,e=e.childNodes||[];if(!i(e,function(e){return C(e)&&e!==t}))return r=i(e,b),!(t&&r&&e.indexOf(r)>e.indexOf(t))}function W(e,t,r){var a=e.childNodes||[],n=t.childNodes||[];if(t.nodeType===x.DOCUMENT_FRAGMENT_NODE){var o=n.filter(C);if(1<o.length||i(n,A))throw new u(c,"More than one element or text in fragment");if(1===o.length&&!$(e,r))throw new u(c,"Element in fragment can not be inserted before doctype")}if(C(t)&&!$(e,r))throw new u(c,"Only one element can be added and only after doctype");if(b(t)){if(i(a,b))throw new u(c,"Only one doctype is allowed");n=i(a,C);if(r&&a.indexOf(n)<a.indexOf(r))throw new u(c,"Doctype can only be inserted before an element");if(!r&&n)throw new u(c,"Doctype can not be appended since element is present")}}function z(e,t,r){var a=e.childNodes||[],n=t.childNodes||[];if(t.nodeType===x.DOCUMENT_FRAGMENT_NODE){var o=n.filter(C);if(1<o.length||i(n,A))throw new u(c,"More than one element or text in fragment");if(1===o.length&&!H(e,r))throw new u(c,"Element in fragment can not be inserted before doctype")}if(C(t)&&!H(e,r))throw new u(c,"Only one element can be added and only after doctype");if(b(t)){if(i(a,function(e){return b(e)&&e!==r}))throw new u(c,"Only one doctype is allowed");n=i(a,C);if(r&&a.indexOf(n)<a.indexOf(r))throw new u(c,"Doctype can only be inserted before an element")}}function E(e,t,r,a){var n=e,o=t,i=r;if(!n||n.nodeType!==x.DOCUMENT_NODE&&n.nodeType!==x.DOCUMENT_FRAGMENT_NODE&&n.nodeType!==x.ELEMENT_NODE)throw new u(c,"Unexpected parent node type "+n.nodeType);if(i&&i.parentNode!==n)throw new u(8,"child not in parent");if(!o||!(C(o)||A(o)||b(o)||o.nodeType===x.DOCUMENT_FRAGMENT_NODE||o.nodeType===x.COMMENT_NODE||o.nodeType===x.PROCESSING_INSTRUCTION_NODE)||b(o)&&n.nodeType!==x.DOCUMENT_NODE)throw new u(c,"Unexpected node type "+o.nodeType+" for parent node type "+n.nodeType);e.nodeType===x.DOCUMENT_NODE&&(a||W)(e,t,r);i=t.parentNode;if(i&&i.removeChild(t),11===t.nodeType){var l=t.firstChild;if(null==l)return t;var s=t.lastChild}else l=s=t;o=r?r.previousSibling:e.lastChild;for(l.previousSibling=o,s.nextSibling=r,o?o.nextSibling=l:e.firstChild=l,null==r?e.lastChild=s:r.previousSibling=s;l.parentNode=e,l!==s&&(l=l.nextSibling););return w(e.ownerDocument||e,e),11==t.nodeType&&(t.firstChild=t.lastChild=null),t}function y(){this._nsMap={}}function _(){}function q(){}function D(){}function T(){}function N(){}function M(){}function Y(){}function J(){}function O(){}function L(){}function B(){}function Z(){}function Q(e,t){var r,a=[],n=9==this.nodeType&&this.documentElement||this,o=n.prefix,i=n.namespaceURI;return F(this,a,e,t,r=i&&null==o&&null==n.lookupPrefix(i)?[{namespace:i,prefix:null}]:r),a.join("")}function K(e,t,r){var a=e.prefix||"",n=e.namespaceURI;if(n&&("xml"!==a||n!==v.XML)&&n!==v.XMLNS){for(var o=r.length;o--;){var i=r[o];if(i.prefix===a)return i.namespace!==n}return 1}}function S(e,t,r){e.push(" ",t,'="',r.replace(/[<>&"\t\n\r]/g,G),'"')}function F(e,t,r,a,n){if(n=n||[],a){if(!(e=a(e)))return;if("string"==typeof e)return t.push(e)}switch(e.nodeType){case 1:var o=e.attributes,i=o.length,l=e.firstChild,s=e.tagName,c=s;if(!(r=v.isHTML(e.namespaceURI)||r)&&!e.prefix&&e.namespaceURI){for(var u,h,d=0;d<o.length;d++)if("xmlns"===o.item(d).name){u=o.item(d).value;break}if(!u)for(var m=n.length-1;0<=m;m--)if(""===(h=n[m]).prefix&&h.namespace===e.namespaceURI){u=h.namespace;break}if(u!==e.namespaceURI)for(m=n.length-1;0<=m;m--)if((h=n[m]).namespace===e.namespaceURI){h.prefix&&(c=h.prefix+":"+s);break}}t.push("<",c);for(var p,x,f,g=0;g<i;g++)"xmlns"==(p=o.item(g)).prefix?n.push({prefix:p.localName,namespace:p.value}):"xmlns"==p.nodeName&&n.push({prefix:"",namespace:p.value});for(g=0;g<i;g++)K(p=o.item(g),0,n)&&(S(t,(x=p.prefix||"")?"xmlns:"+x:"xmlns",f=p.namespaceURI),n.push({prefix:x,namespace:f})),F(p,t,r,a,n);if(s===c&&K(e,0,n)&&(S(t,(x=e.prefix||"")?"xmlns:"+x:"xmlns",f=e.namespaceURI),n.push({prefix:x,namespace:f})),l||r&&!/^(?:meta|link|img|br|hr|input)$/i.test(s)){if(t.push(">"),r&&/^script$/i.test(s))for(;l;)l.data?t.push(l.data):F(l,t,r,a,n.slice()),l=l.nextSibling;else for(;l;)F(l,t,r,a,n.slice()),l=l.nextSibling;t.push("</",c,">")}else t.push("/>");return;case 9:case 11:for(l=e.firstChild;l;)F(l,t,r,a,n.slice()),l=l.nextSibling;return;case 2:return S(t,e.name,e.value);case 3:return t.push(e.data.replace(/[<&>]/g,G));case 4:return t.push("<![CDATA[",e.data,"]]>");case 8:return t.push("\x3c!--",e.data,"--\x3e");case 10:var w=e.publicId,b=e.systemId;return t.push("<!DOCTYPE ",e.name),void(w?(t.push(" PUBLIC ",w),b&&"."!=b&&t.push(" ",b),t.push(">")):b&&"."!=b?t.push(" SYSTEM ",b,">"):((w=e.internalSubset)&&t.push(" [",w,"]"),t.push(">")));case 7:return t.push("<?",e.target," ",e.data,"?>");case 5:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function ee(e,t,r){e[t]=r}a.INVALID_STATE_ERR=(o[11]="Invalid state",11),a.SYNTAX_ERR=(o[12]="Syntax error",12),a.INVALID_MODIFICATION_ERR=(o[13]="Invalid modification",13),a.NAMESPACE_ERR=(o[14]="Invalid namespace",14),a.INVALID_ACCESS_ERR=(o[15]="Invalid access",15),u.prototype=Error.prototype,n(a,u),d.prototype={length:0,item:function(e){return 0<=e&&e<this.length?this[e]:null},toString:function(e,t){for(var r=[],a=0;a<this.length;a++)F(this[a],r,e,t);return r.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}},h.prototype.item=function(e){return l(this),this[e]||null},r(h,d),m.prototype={length:0,item:d.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var r=this[t];if(r.nodeName==e)return r}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new u(10);t=this.getNamedItem(e.nodeName);return I(this._ownerElement,this,e,t),t},setNamedItemNS:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new u(10);return t=this.getNamedItemNS(e.namespaceURI,e.localName),I(this._ownerElement,this,e,t),t},removeNamedItem:function(e){e=this.getNamedItem(e);return U(this._ownerElement,this,e),e},removeNamedItemNS:function(e,t){e=this.getNamedItemNS(e,t);return U(this._ownerElement,this,e),e},getNamedItemNS:function(e,t){for(var r=this.length;r--;){var a=this[r];if(a.localName==t&&a.namespaceURI==e)return a}return null}},j.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,r){var a=new g;return a.implementation=this,a.childNodes=new d,a.doctype=r||null,r&&a.appendChild(r),t&&(r=a.createElementNS(e,t),a.appendChild(r)),a},createDocumentType:function(e,t,r){var a=new M;return a.name=e,a.nodeName=e,a.publicId=t||"",a.systemId=r||"",a}},x.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return E(this,e,t)},replaceChild:function(e,t){E(this,e,t,z),t&&this.removeChild(t)},removeChild:function(e){return X(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,r,a){var n,o,i=new r.constructor;for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&"object"!=typeof(o=r[n])&&o!=i[n]&&(i[n]=o);switch(r.childNodes&&(i.childNodes=new d),i.ownerDocument=t,i.nodeType){case 1:var l=r.attributes,s=i.attributes=new m,c=l.length;s._ownerElement=i;for(var u=0;u<c;u++)i.setAttributeNode(e(t,l.item(u),!0));break;case 2:a=!0}if(a)for(var h=r.firstChild;h;)i.appendChild(e(t,h,a)),h=h.nextSibling;return i}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&3==t.nodeType&&3==e.nodeType?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return 0<this.attributes.length},lookupPrefix:function(e){for(var t=this;t;){var r=t._nsMap;if(r)for(var a in r)if(Object.prototype.hasOwnProperty.call(r,a)&&r[a]===e)return a;t=2==t.nodeType?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var r=t._nsMap;if(r&&Object.prototype.hasOwnProperty.call(r,e))return r[e];t=2==t.nodeType?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},n(t,x),n(t,x.prototype),g.prototype={nodeName:"#document",nodeType:9,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(11==e.nodeType)for(var r=e.firstChild;r;){var a=r.nextSibling;this.insertBefore(r,t),r=a}else E(this,e,t),null===(e.ownerDocument=this).documentElement&&1===e.nodeType&&(this.documentElement=e);return e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),X(this,e)},replaceChild:function(e,t){E(this,e,t,z),e.ownerDocument=this,t&&this.removeChild(t),C(e)&&(this.documentElement=e)},importNode:function(e,t){return function e(t,r,a){var n;switch(r.nodeType){case 1:(n=r.cloneNode(!1)).ownerDocument=t;case 11:break;case 2:a=!0}if((n=n||r.cloneNode(!1)).ownerDocument=t,n.parentNode=null,a)for(var o=r.firstChild;o;)n.appendChild(e(t,o,a)),o=o.nextSibling;return n}(this,e,t)},getElementById:function(t){var r=null;return f(this.documentElement,function(e){if(1==e.nodeType&&e.getAttribute("id")==t)return r=e,!0}),r},getElementsByClassName:function(i){var l=s(i);return new h(this,function(n){var o=[];return 0<l.length&&f(n.documentElement,function(e){var t,r,a;e!==n&&1===e.nodeType&&(t=e.getAttribute("class"))&&((r=i===t)||(t=s(t),r=l.every((a=t,function(e){return a&&-1!==a.indexOf(e)}))),r)&&o.push(e)}),o})},createElement:function(e){var t=new y;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new d,(t.attributes=new m)._ownerElement=t},createDocumentFragment:function(){var e=new L;return e.ownerDocument=this,e.childNodes=new d,e},createTextNode:function(e){var t=new D;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new T;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new N;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var r=new B;return r.ownerDocument=this,r.tagName=r.nodeName=r.target=e,r.nodeValue=r.data=t,r},createAttribute:function(e){var t=new _;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new O;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var r=new y,a=t.split(":"),n=r.attributes=new m;return r.childNodes=new d,r.ownerDocument=this,r.nodeName=t,r.tagName=t,r.namespaceURI=e,2==a.length?(r.prefix=a[0],r.localName=a[1]):r.localName=t,n._ownerElement=r},createAttributeNS:function(e,t){var r=new _,a=t.split(":");return r.ownerDocument=this,r.nodeName=t,r.name=t,r.namespaceURI=e,r.specified=!0,2==a.length?(r.prefix=a[0],r.localName=a[1]):r.localName=t,r}},r(g,x),g.prototype.getElementsByTagName=(y.prototype={nodeType:1,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){e=this.getAttributeNode(e);return e&&e.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){e=this.ownerDocument.createAttribute(e);e.value=e.nodeValue=""+t,this.setAttributeNode(e)},removeAttribute:function(e){e=this.getAttributeNode(e);e&&this.removeAttributeNode(e)},appendChild:function(e){return 11===e.nodeType?this.insertBefore(e,null):(t=this,(e=e).parentNode&&e.parentNode.removeChild(e),e.parentNode=t,e.previousSibling=t.lastChild,e.nextSibling=null,e.previousSibling?e.previousSibling.nextSibling=e:t.firstChild=e,t.lastChild=e,w(t.ownerDocument,t,e),e);var t},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){e=this.getAttributeNodeNS(e,t);e&&this.removeAttributeNode(e)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){e=this.getAttributeNodeNS(e,t);return e&&e.value||""},setAttributeNS:function(e,t,r){e=this.ownerDocument.createAttributeNS(e,t);e.value=e.nodeValue=""+r,this.setAttributeNode(e)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(a){return new h(this,function(t){var r=[];return f(t,function(e){e===t||1!=e.nodeType||"*"!==a&&e.tagName!=a||r.push(e)}),r})},getElementsByTagNameNS:function(a,n){return new h(this,function(t){var r=[];return f(t,function(e){e===t||1!==e.nodeType||"*"!==a&&e.namespaceURI!==a||"*"!==n&&e.localName!=n||r.push(e)}),r})}}).getElementsByTagName,g.prototype.getElementsByTagNameNS=y.prototype.getElementsByTagNameNS,r(y,x),_.prototype.nodeType=2,r(_,x),q.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(o[c])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,r){r=this.data.substring(0,e)+r+this.data.substring(e+t),this.nodeValue=this.data=r,this.length=r.length}},r(q,x),D.prototype={nodeName:"#text",nodeType:3,splitText:function(e){var t=(r=this.data).substring(e),r=r.substring(0,e),e=(this.data=this.nodeValue=r,this.length=r.length,this.ownerDocument.createTextNode(t));return this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling),e}},r(D,q),T.prototype={nodeName:"#comment",nodeType:8},r(T,q),N.prototype={nodeName:"#cdata-section",nodeType:4},r(N,q),M.prototype.nodeType=10,r(M,x),Y.prototype.nodeType=12,r(Y,x),J.prototype.nodeType=6,r(J,x),O.prototype.nodeType=5,r(O,x),L.prototype.nodeName="#document-fragment",L.prototype.nodeType=11,r(L,x),B.prototype.nodeType=7,r(B,x),Z.prototype.serializeToString=function(e,t,r){return Q.call(e,t,r)},x.prototype.toString=Q;try{Object.defineProperty&&(Object.defineProperty(h.prototype,"length",{get:function(){return l(this),this.$$length}}),Object.defineProperty(x.prototype,"textContent",{get:function(){return function e(t){switch(t.nodeType){case 1:case 11:var r=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&r.push(e(t)),t=t.nextSibling;return r.join("");default:return t.nodeValue}}(this)},set:function(e){switch(this.nodeType){case 1:case 11:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),ee=function(e,t,r){e["$$"+t]=r})}catch(e){}e.DocumentType=M,e.DOMException=u,e.DOMImplementation=j,e.Element=y,e.Node=x,e.NodeList=d,e.XMLSerializer=Z},1045:(e,t,r)=>{"use strict";r=r(2167).freeze;t.XML_ENTITIES=r({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),t.HTML_ENTITIES=r({Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}),t.entityMap=t.HTML_ENTITIES},3969:(e,t,r)=>{var a=r(1146);t.DOMImplementation=a.DOMImplementation,t.XMLSerializer=a.XMLSerializer,t.DOMParser=r(6129).DOMParser},6925:(e,t,r)=>{var H=r(2167).NAMESPACE,r=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,a=new RegExp("[\\-\\.0-9"+r.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),n=new RegExp("^"+r.source+a.source+"*(?::"+r.source+a.source+"*)?$");function W(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,W)}function o(){}function z(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function Y(e,t,r){for(var a,n=e.tagName,o=null,i=e.length;i--;){var l,s=e[i],c=s.qName,u=s.value,c=0<(a=c.indexOf(":"))?(l=s.prefix=c.slice(0,a),h=c.slice(a+1),"xmlns"===l&&h):(l=null,"xmlns"===(h=c)&&"");s.localName=h,!1!==c&&(null==o&&(o={},m(r,r={})),r[c]=o[c]=u,s.uri=H.XMLNS,t.startPrefixMapping(c,u))}for(i=e.length;i--;)(l=(s=e[i]).prefix)&&("xml"===l&&(s.uri=H.XML),"xmlns"!==l)&&(s.uri=r[l||""]);var h=0<(a=n.indexOf(":"))?(l=e.prefix=n.slice(0,a),e.localName=n.slice(a+1)):(l=null,e.localName=n),d=e.uri=r[l||""];if(t.startElement(d,h,n,e),!e.closed)return e.currentNSMap=r,e.localNSMap=o,1;if(t.endElement(d,h,n),o)for(l in o)Object.prototype.hasOwnProperty.call(o,l)&&t.endPrefixMapping(l)}function m(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function J(){this.attributeNames={}}(W.prototype=new Error).name=W.name,o.prototype={parse:function(e,t,r){var a=this.domBuilder;a.startDocument(),m(t,t={}),function(r,k,a,n,o){function i(e){var t,r=e.slice(1,-1);return Object.hasOwnProperty.call(a,r)?a[r]:"#"===r.charAt(0)?65535<(r=parseInt(r.substr(1).replace("x","0x")))?(t=55296+((r-=65536)>>10),String.fromCharCode(t,56320+(1023&r))):String.fromCharCode(r):(o.error("entity not found:"+e),e)}function e(e){var t;q<e&&(t=r.substring(q,e).replace(/&#?\w+;/g,i),y&&l(q),n.characters(t,0,e-q),q=e)}function l(e,t){for(;P<=e&&(t=R.exec(r));)E=t.index,P=E+t[0].length,y.lineNumber++;y.columnNumber=e-E+1}for(var t,s,c,u,h,d,m,p,x,f,g,w,b,v,C,A,E=0,P=0,R=/.*(?:\r\n?|\n)|.*$/g,y=n.locator,_=[{currentNSMap:k}],I={},q=0;;){try{var U,D,T=r.indexOf("<",q);if(T<0)return r.substr(q).match(/^\s*$/)||(D=(U=n.doc).createTextNode(r.substr(q)),U.appendChild(D),n.currentElement=D);switch(q<T&&e(T),r.charAt(T+1)){case"/":var N=r.indexOf(">",T+3),M=r.substring(T+2,N).replace(/[ \t\n\r]+$/g,""),O=_.pop(),L=(N<0?(M=r.substring(T+2).replace(/[\s<].*/,""),o.error("end tag name: "+M+" is not complete:"+O.tagName),N=T+1+M.length):M.match(/\s</)&&(M=M.replace(/[\s<].*/,""),o.error("end tag name: "+M+" maybe not complete"),N=T+1+M.length),O.localNSMap),j=O.tagName==M;if(j||O.tagName&&O.tagName.toLowerCase()==M.toLowerCase()){if(n.endElement(O.uri,O.localName,M),L)for(var G in L)Object.prototype.hasOwnProperty.call(L,G)&&n.endPrefixMapping(G);j||o.fatalError("end tag name: "+M+" is not match the current start tagName:"+O.tagName)}else _.push(O);N++;break;case"?":y&&l(T),v=T,C=n,A=void 0,N=(A=(b=r).indexOf("?>",v))&&(b=b.substring(v,A).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/))?(b[0].length,C.processingInstruction(b[1],b[2]),A+2):-1;break;case"!":y&&l(T),m=T,p=n,x=o,w=g=f=void 0,N="-"===(d=r).charAt(m+2)?"-"===d.charAt(m+3)?(f=d.indexOf("--\x3e",m+4))>m?(p.comment(d,m+4,f-m-4),f+3):(x.error("Unclosed comment"),-1):-1:"CDATA["==d.substr(m+3,6)?(f=d.indexOf("]]>",m+9),p.startCDATA(),p.characters(d,m+9,f-m-9),p.endCDATA(),f+3):1<(f=(x=function(e){var t,r=[],a=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;for(a.lastIndex=m,a.exec(e);t=a.exec(e);)if(r.push(t),t[1])return r}(d)).length)&&/!doctype/i.test(x[0][0])?(d=x[1][0],w=g=!1,3<f&&(/^public$/i.test(x[2][0])?(g=x[3][0],w=4<f&&x[4][0]):/^system$/i.test(x[2][0])&&(w=x[3][0])),x=x[f-1],p.startDTD(d,g,w),p.endDTD(),x.index+x[0].length):-1;break;default:y&&l(T);var B=new J,S=_[_.length-1].currentNSMap,V=(N=function(e,t,a,r,n,o){function i(e,t,r){a.attributeNames.hasOwnProperty(e)&&o.fatalError("Attribute "+e+" redefined"),a.addValue(e,t.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,n),r)}for(var l,s=++t,c=0;;){var u=e.charAt(s);switch(u){case"=":if(1===c)l=e.slice(t,s);else if(2!==c)throw new Error("attribute equal must after attrName");c=3;break;case"'":case'"':if(3===c||1===c){if(1===c&&(o.warning('attribute value must after "="'),l=e.slice(t,s)),!(0<(s=e.indexOf(u,t=s+1))))throw new Error("attribute value no end '"+u+"' match");i(l,h=e.slice(t,s),t-1)}else{if(4!=c)throw new Error('attribute value must after "="');i(l,h=e.slice(t,s),t),o.warning('attribute "'+l+'" missed start quot('+u+")!!"),t=s+1}c=5;break;case"/":switch(c){case 0:a.setTagName(e.slice(t,s));case 5:case 6:case 7:c=7,a.closed=!0;case 4:case 1:break;case 2:a.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return o.error("unexpected end of input"),0==c&&a.setTagName(e.slice(t,s)),s;case">":switch(c){case 0:a.setTagName(e.slice(t,s));case 5:case 6:case 7:break;case 4:case 1:"/"===(h=e.slice(t,s)).slice(-1)&&(a.closed=!0,h=h.slice(0,-1));case 2:2===c&&(h=l),4==c?(o.warning('attribute "'+h+'" missed quot(")!'),i(l,h,t)):(H.isHTML(r[""])&&h.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+h+'" missed value!! "'+h+'" instead!!'),i(h,h,t));break;case 3:throw new Error("attribute value missed!!")}return s;case"":u=" ";default:if(u<=" ")switch(c){case 0:a.setTagName(e.slice(t,s)),c=6;break;case 1:l=e.slice(t,s),c=2;break;case 4:var h=e.slice(t,s);o.warning('attribute "'+h+'" missed quot(")!!'),i(l,h,t);case 5:c=6}else switch(c){case 2:a.tagName,H.isHTML(r[""])&&l.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+l+'" missed value!! "'+l+'" instead2!!'),i(l,l,t),t=s,c=1;break;case 5:o.warning('attribute space is required"'+l+'"!!');case 6:c=1,t=s;break;case 3:c=4,t=s;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}s++}}(r,T,B,S,i,o),B.length);if(!B.closed&&(t=r,s=N,c=B.tagName,h=void 0,null==(h=(u=I)[c])&&((h=t.lastIndexOf("</"+c+">"))<s&&(h=t.lastIndexOf("</"+c)),u[c]=h),h<s)&&(B.closed=!0,a.nbsp||o.warning("unclosed xml attribute")),y&&V){for(var X=z(y,{}),F=0;F<V;F++){var $=B[F];l($.offset),$.locator=z(y,{})}n.locator=X,Y(B,n,S)&&_.push(B),n.locator=y}else Y(B,n,S)&&_.push(B);H.isHTML(B.uri)&&!B.closed?N=function(e,t,r,a,n){if(/^(?:script|textarea)$/i.test(r)){var o=e.indexOf("</"+r+">",t),e=e.substring(t+1,o);if(/[&<]/.test(e))return/^script$/i.test(r)||(e=e.replace(/&#?\w+;/g,a)),n.characters(e,0,e.length),o}return t+1}(r,N,B.tagName,i,n):N++}}catch(r){if(r instanceof W)throw r;o.error("element parse error: "+r),N=-1}q<N?q=N:e(Math.max(T,q)+1)}}(e,t,r,a,this.errorHandler),a.endDocument()}},J.prototype={setTagName:function(e){if(!n.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,r){if(!n.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:r}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},t.XMLReader=o,t.ParseError=W},6036:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidNumberOfChildrenError=void 0;var a=r(6187);Object.defineProperty(t,"InvalidNumberOfChildrenError",{enumerable:!0,get:function(){return a.InvalidNumberOfChildrenError}})},6187:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidNumberOfChildrenError=void 0;class r extends Error{constructor(e,t,r,a="exactly"){super(e+` tag must have ${a} ${t} children. It's actually `+r),this.name="InvalidNumberOfChildrenError"}}t.InvalidNumberOfChildrenError=r},8398:function(e,t,r){"use strict";var a=this&&this.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(8208),t),n(r(7011),t),n(r(3487),t),n(r(5379),t)},7011:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JoinWithManySeparators=void 0;t.JoinWithManySeparators=class r{constructor(e){this._separators=e}static join(e,t){return new r(t)._join(e)}_join(e){return e.reduce((e,t,r,a)=>e+t+(r===a.length-1?"":this._get(r)),"")}_get(e){return this._separators[e]||(0<this._separators.length?this._separators[this._separators.length-1]:",")}}},3487:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mathMLElementToLaTeXConverter=void 0;const a=r(6483);t.mathMLElementToLaTeXConverter=e=>new a.MathMLElementToLatexConverterAdapter(e).toLatexConverter()},5379:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizeWhiteSpaces=void 0,t.normalizeWhiteSpaces=e=>e.replace(/\s+/g," ")},7274:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BracketWrapper=void 0;const a=r(836);t.BracketWrapper=class{constructor(){this._open="{",this._close="}"}wrap(e){return new a.Wrapper(this._open,this._close).wrap(e)}}},5667:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericWrapper=void 0;const a=r(836);t.GenericWrapper=class{constructor(e,t){this._open="\\left"+e,this._close="\\right"+t}wrap(e){return new a.Wrapper(this._open,this._close).wrap(e)}}},8208:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericWrapper=t.ParenthesisWrapper=t.BracketWrapper=void 0;var a=r(7274),n=(Object.defineProperty(t,"BracketWrapper",{enumerable:!0,get:function(){return a.BracketWrapper}}),r(3246)),o=(Object.defineProperty(t,"ParenthesisWrapper",{enumerable:!0,get:function(){return n.ParenthesisWrapper}}),r(5667));Object.defineProperty(t,"GenericWrapper",{enumerable:!0,get:function(){return o.GenericWrapper}})},3246:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ParenthesisWrapper=void 0;const a=r(836);t.ParenthesisWrapper=class{constructor(){this._open="\\left(",this._close="\\right)"}wrap(e){return new a.Wrapper(this._open,this._close).wrap(e)}wrapIfMoreThanOneChar(e){return e.length<=1?e:this.wrap(e)}}},836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Wrapper=void 0,t.Wrapper=class{constructor(e,t){this._open=e,this._close=t}wrap(e){return this._open+e+this._close}}},7510:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericSpacingWrapper=void 0;const a=r(8398);t.GenericSpacingWrapper=class{constructor(e){this._mathmlElement=e}convert(){return this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" ")}}},3589:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericUnderOver=void 0;const a=r(3487),n=r(6036),o=r(6626);t.GenericUnderOver=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(2!==r)throw new n.InvalidNumberOfChildrenError(e,2,r);e=(0,a.mathMLElementToLaTeXConverter)(t[0]).convert(),r=(0,a.mathMLElementToLaTeXConverter)(t[1]).convert();return this._applyCommand(e,r)}_applyCommand(e,t){var r=this._mathmlElement.name.match(/under/)?l.Under:l.Over;return new i(r).apply(e,t)}};class i{constructor(e){this._type=e}apply(e,t){return o.latexAccents.includes(t)?t+`{${e}}`:this._defaultCommand+`{${t}}{${e}}`}get _defaultCommand(){return this._type===l.Under?"\\underset":"\\overset"}}var l;(r=l=l||{})[r.Under=0]="Under",r[r.Over=1]="Over"},1733:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericUnderOver=t.GenericSpacingWrapper=t.MTr=t.MTable=t.MUnderover=t.MText=t.MMultiscripts=t.MSubsup=t.MSub=t.MSup=t.MPhantom=t.MError=t.MEnclose=t.MAction=t.MRoot=t.MFrac=t.MFenced=t.MSqrt=t.MN=t.MO=t.MI=t.Math=void 0;var a=r(2283),n=(Object.defineProperty(t,"Math",{enumerable:!0,get:function(){return a.Math}}),r(7523)),o=(Object.defineProperty(t,"MI",{enumerable:!0,get:function(){return n.MI}}),r(5258)),i=(Object.defineProperty(t,"MO",{enumerable:!0,get:function(){return o.MO}}),r(411)),l=(Object.defineProperty(t,"MN",{enumerable:!0,get:function(){return i.MN}}),r(8301)),s=(Object.defineProperty(t,"MSqrt",{enumerable:!0,get:function(){return l.MSqrt}}),r(5237)),c=(Object.defineProperty(t,"MFenced",{enumerable:!0,get:function(){return s.MFenced}}),r(4060)),u=(Object.defineProperty(t,"MFrac",{enumerable:!0,get:function(){return c.MFrac}}),r(5851)),h=(Object.defineProperty(t,"MRoot",{enumerable:!0,get:function(){return u.MRoot}}),r(916)),d=(Object.defineProperty(t,"MAction",{enumerable:!0,get:function(){return h.MAction}}),r(7018)),m=(Object.defineProperty(t,"MEnclose",{enumerable:!0,get:function(){return d.MEnclose}}),r(6752)),p=(Object.defineProperty(t,"MError",{enumerable:!0,get:function(){return m.MError}}),r(9897)),x=(Object.defineProperty(t,"MPhantom",{enumerable:!0,get:function(){return p.MPhantom}}),r(5806)),f=(Object.defineProperty(t,"MSup",{enumerable:!0,get:function(){return x.MSup}}),r(9)),g=(Object.defineProperty(t,"MSub",{enumerable:!0,get:function(){return f.MSub}}),r(2740)),w=(Object.defineProperty(t,"MSubsup",{enumerable:!0,get:function(){return g.MSubsup}}),r(4596)),b=(Object.defineProperty(t,"MMultiscripts",{enumerable:!0,get:function(){return w.MMultiscripts}}),r(3808)),v=(Object.defineProperty(t,"MText",{enumerable:!0,get:function(){return b.MText}}),r(2710)),C=(Object.defineProperty(t,"MUnderover",{enumerable:!0,get:function(){return v.MUnderover}}),r(4134)),A=(Object.defineProperty(t,"MTable",{enumerable:!0,get:function(){return C.MTable}}),r(7188)),E=(Object.defineProperty(t,"MTr",{enumerable:!0,get:function(){return A.MTr}}),r(7510)),y=(Object.defineProperty(t,"GenericSpacingWrapper",{enumerable:!0,get:function(){return E.GenericSpacingWrapper}}),r(3589));Object.defineProperty(t,"GenericUnderOver",{enumerable:!0,get:function(){return y.GenericUnderOver}})},916:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MAction=void 0;const a=r(3487);t.MAction=class{constructor(e){this._mathmlElement=e}convert(){var e=this._mathmlElement["children"];return this._isToggle()?e.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" \\Longrightarrow "):(0,a.mathMLElementToLaTeXConverter)(e[0]).convert()}_isToggle(){var e=this._mathmlElement.attributes["actiontype"];return"toggle"===e||!e}}},2283:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Math=void 0;const a=r(3487),n=r(5379);t.Math=class{constructor(e){this._mathmlElement=e}convert(){var e=this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" ");return(0,n.normalizeWhiteSpaces)(e)}}},7018:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MEnclose=void 0;const a=r(3487);t.MEnclose=class{constructor(e){this._mathmlElement=e}convert(){var e=this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" ");return"actuarial"===this._notation?`\\overline{\\left.${e}\\right|}`:"radical"===this._notation?`\\sqrt{${e}}`:["box","roundedbox","circle"].includes(this._notation)?`\\boxed{${e}}`:"left"===this._notation?"\\left|"+e:"right"===this._notation?e+"\\right|":"top"===this._notation?`\\overline{${e}}`:"bottom"===this._notation?`\\underline{${e}}`:"updiagonalstrike"===this._notation?`\\cancel{${e}}`:"downdiagonalstrike"===this._notation?`\\bcancel{${e}}`:"updiagonalarrow"===this._notation?`\\cancelto{}{${e}}`:["verticalstrike","horizontalstrike"].includes(this._notation)?`\\hcancel{${e}}`:"madruwb"===this._notation?`\\underline{${e}\\right|}`:"phasorangle"===this._notation?`{\\angle \\underline{${e}}}`:`\\overline{\\left.\\right)${e}}`}get _notation(){return this._mathmlElement.attributes.notation||"longdiv"}}},6752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MError=void 0;const a=r(3487);t.MError=class{constructor(e){this._mathmlElement=e}convert(){return`\\color{red}{${this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" ")}}`}}},5237:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MFenced=void 0;const a=r(3487),n=r(8398);t.MFenced=class{constructor(e){this._mathmlElement=e,this._open=this._mathmlElement.attributes.open||"",this._close=this._mathmlElement.attributes.close||"",this._separators=Array.from(this._mathmlElement.attributes.separators||"")}convert(){var e=this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert());return(this._isThereRelativeOfName(this._mathmlElement.children,"mtable")?new i(this._open,this._close):new o(this._open,this._close,this._separators)).apply(e)}_isThereRelativeOfName(e,t){return e.some(e=>e.name===t||this._isThereRelativeOfName(e.children,t))}};class o{constructor(e,t,r){this._open=e||"(",this._close=t||")",this._separators=r}apply(e){e=n.JoinWithManySeparators.join(e,this._separators);return new n.GenericWrapper(this._open,this._close).wrap(e)}}class i{constructor(e,t){this._genericCommand="matrix",this._separators=new l(e,t)}apply(e){var t=this._command,e=`\\begin{${t}}
${e.join("")}
\\end{${t}}`;return t===this._genericCommand?this._separators.wrap(e):e}get _command(){return this._separators.areParentheses()?"pmatrix":this._separators.areSquareBrackets()?"bmatrix":this._separators.areBrackets()?"Bmatrix":this._separators.areDivides()?"vmatrix":this._separators.areParallels()?"Vmatrix":this._separators.areNotEqual()?this._genericCommand:"bmatrix"}}class l{constructor(e,t){this._open=e,this._close=t}wrap(e){return new n.GenericWrapper(this._open,this._close).wrap(e)}areParentheses(){return this._compare("(",")")}areSquareBrackets(){return this._compare("[","]")}areBrackets(){return this._compare("{","}")}areDivides(){return this._compare("|","|")}areParallels(){return this._compare("||","||")}areNotEqual(){return this._open!==this._close}_compare(e,t){return this._open===e&&this._close===t}}},4060:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MFrac=void 0;const a=r(6036),n=r(8398);t.MFrac=class{constructor(e){this._mathmlElement=e}convert(){var{children:e,name:t}=this._mathmlElement,r=e.length;if(2!==r)throw new a.InvalidNumberOfChildrenError(t,2,r);t=(0,n.mathMLElementToLaTeXConverter)(e[0]).convert(),r=(0,n.mathMLElementToLaTeXConverter)(e[1]).convert();return this._isBevelled()?this._wrapIfMoreThanOneChar(t)+"/"+this._wrapIfMoreThanOneChar(r):`\\frac{${t}}{${r}}`}_wrapIfMoreThanOneChar(e){return(new n.ParenthesisWrapper).wrapIfMoreThanOneChar(e)}_isBevelled(){return!!this._mathmlElement.attributes.bevelled}}},7523:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MI=void 0;const a=r(8398),n=r(2408),o=r(4317);t.MI=class{constructor(e){this.utf8Converter=new o.HashUTF8ToLtXConverter,this._mathmlElement=e}convert(){var e,t=(0,a.normalizeWhiteSpaces)(this._mathmlElement.value);return" "===t?i.apply(t):(t=t.trim(),t=i.apply(t),(e=this.utf8Converter.convert(t))!==t?e:this.wrapInMathVariant(t,this.getMathVariant(this._mathmlElement.attributes)))}getMathVariant(e){if(e&&e.mathvariant)return e.mathvariant}wrapInMathVariant(e,t){switch(t){case"bold":return`\\mathbf{${e}}`;case"italic":return`\\mathit{${e}}`;case"bold-italic":return`\\mathbf{\\mathit{${e}}}`;case"double-struck":return`\\mathbb{${e}}`;case"bold-fraktur":return`\\mathbf{\\mathfrak{${e}}}`;case"script":return`\\mathcal{${e}}`;case"bold-script":return`\\mathbf{\\mathcal{${e}}}`;case"fraktur":return`\\mathfrak{${e}}`;case"sans-serif":return`\\mathsf{${e}}`;case"bold-sans-serif":return`\\mathbf{\\mathsf{${e}}}`;case"sans-serif-italic":return`\\mathsf{\\mathit{${e}}}`;case"sans-serif-bold-italic":return`\\mathbf{\\mathsf{\\mathit{${e}}}}`;case"monospace":return`\\mathtt{${e}}`;default:return e}}};class i{constructor(e){this._value=e}static apply(e){return new i(e)._apply()}_apply(){return this._findByCharacter()||this._findByGlyph()||this._findByNumber()||(new o.HashUTF8ToLtXConverter).convert(this._value)}_findByCharacter(){return n.allMathSymbolsByChar[this._value]}_findByGlyph(){return n.allMathSymbolsByGlyph[this._value]}_findByNumber(){return n.mathNumberByGlyph[this._value]}}},4596:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MMultiscripts=void 0;const a=r(8398),n=r(6036);t.MMultiscripts=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(r<3)throw new n.InvalidNumberOfChildrenError(e,3,r,"at least");e=(0,a.mathMLElementToLaTeXConverter)(t[0]).convert();return this._prescriptLatex()+this._wrapInParenthesisIfThereIsSpace(e)+this._postscriptLatex()}_prescriptLatex(){var e=this._mathmlElement["children"];let t,r;if(this._isPrescripts(e[1]))t=e[2],r=e[3];else{if(!this._isPrescripts(e[3]))return"";t=e[4],r=e[5]}return`\\_{${(0,a.mathMLElementToLaTeXConverter)(t).convert()}}^{${(0,a.mathMLElementToLaTeXConverter)(r).convert()}}`}_postscriptLatex(){var e,t=this._mathmlElement["children"];return this._isPrescripts(t[1])?"":(e=t[1],t=t[2],`_{${(0,a.mathMLElementToLaTeXConverter)(e).convert()}}^{${(0,a.mathMLElementToLaTeXConverter)(t).convert()}}`)}_wrapInParenthesisIfThereIsSpace(e){return e.match(/\s+/g)?(new a.ParenthesisWrapper).wrap(e):e}_isPrescripts(e){return"mprescripts"===(null==e?void 0:e.name)}}},411:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MN=void 0;const a=r(8398),n=r(2408);t.MN=class{constructor(e){this._mathmlElement=e}convert(){var e=(0,a.normalizeWhiteSpaces)(this._mathmlElement.value).trim();return n.mathNumberByGlyph[e]||e}}},5258:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MO=void 0;const a=r(8398),n=r(2408);t.MO=class{constructor(e){this._mathmlElement=e}convert(){var e=(0,a.normalizeWhiteSpaces)(this._mathmlElement.value).trim();return o.operate(e)}};class o{constructor(e){this._value=e}static operate(e){return new o(e)._operate()}_operate(){return this._findByCharacter()||this._findByGlyph()||this._findByNumber()||(new n.HashUTF8ToLtXConverter).convert(this._value)}_findByCharacter(){return n.allMathOperatorsByChar[this._value]}_findByGlyph(){return n.allMathOperatorsByGlyph[this._value]}_findByNumber(){return n.mathNumberByGlyph[this._value]}}},9897:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MPhantom=void 0,t.MPhantom=class{constructor(e){this._mathmlElement=e}convert(){return""}}},5851:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MRoot=void 0;const a=r(8398),n=r(6036);t.MRoot=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(2!==r)throw new n.InvalidNumberOfChildrenError(e,2,r);e=(0,a.mathMLElementToLaTeXConverter)(t[0]).convert();return`\\sqrt[${(0,a.mathMLElementToLaTeXConverter)(t[1]).convert()}]{${e}}`}}},8301:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MSqrt=void 0;const a=r(8398);t.MSqrt=class{constructor(e){this._mathmlElement=e}convert(){return`\\sqrt{${this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" ")}}`}}},9:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MSub=void 0;const a=r(8398),n=r(6036);t.MSub=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(2!==r)throw new n.InvalidNumberOfChildrenError(e,2,r);e=(0,a.mathMLElementToLaTeXConverter)(t[0]).convert(),r=(0,a.mathMLElementToLaTeXConverter)(t[1]).convert();return(new a.ParenthesisWrapper).wrapIfMoreThanOneChar(e)+"_"+(new a.BracketWrapper).wrap(r)}}},2740:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MSubsup=void 0;const a=r(8398),n=r(6036);t.MSubsup=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(3!==r)throw new n.InvalidNumberOfChildrenError(e,3,r);e=(0,a.mathMLElementToLaTeXConverter)(t[0]).convert(),r=(0,a.mathMLElementToLaTeXConverter)(t[1]).convert(),t=(0,a.mathMLElementToLaTeXConverter)(t[2]).convert(),r=(new a.BracketWrapper).wrap(r),t=(new a.BracketWrapper).wrap(t);return this._wrapInParenthesisIfThereIsSpace(e)+`_${r}^`+t}_wrapInParenthesisIfThereIsSpace(e){return e.match(/\s+/g)?(new a.ParenthesisWrapper).wrap(e):e}}},5806:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MSup=void 0;const a=r(8398),n=r(6036);t.MSup=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(2!==r)throw new n.InvalidNumberOfChildrenError(e,2,r);e=(0,a.mathMLElementToLaTeXConverter)(t[0]).convert(),r=(0,a.mathMLElementToLaTeXConverter)(t[1]).convert();return(new a.ParenthesisWrapper).wrapIfMoreThanOneChar(e)+"^"+(new a.BracketWrapper).wrap(r)}}},4134:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MTable=void 0;const a=r(8398);t.MTable=class{constructor(e){this._mathmlElement=e,this._addFlagRecursiveIfName(this._mathmlElement.children,"mtable","innerTable")}convert(){var e=this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" \\\\\n");return this._hasFlag("innerTable")?this._wrap(e):e}_wrap(e){return`\\begin{matrix}${e}\\end{matrix}`}_addFlagRecursiveIfName(e,t,r){e.forEach(e=>{e.name===t&&(e.attributes[r]=r),this._addFlagRecursiveIfName(e.children,t,r)})}_hasFlag(e){return!!this._mathmlElement.attributes[e]}}},3808:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MText=void 0;const a=r(7523);t.MText=class{constructor(e){this._mathmlElement=e}convert(){const{attributes:t,value:e}=this._mathmlElement;return[...e].map(e=>/^[a-zA-Z0-9]$/.test(e)||" "===e?{value:e,isAlphanumeric:!0}:{value:e,isAlphanumeric:!1}).reduce((e,t)=>{if(t.isAlphanumeric){var r=e[e.length-1];if(r&&r.isAlphanumeric)return r.value+=t.value,e}return[...e,t]},[]).map(e=>e.isAlphanumeric?new n(t.mathvariant).apply(e.value):new a.MI({name:"mi",attributes:{},children:[],value:e.value}).convert()).join("")}};class n{constructor(e){this._mathvariant=e||"normal"}apply(a){return this._commands.reduce((e,t,r)=>0===r?t+`{${a}}`:t+`{${e}}`,"")}get _commands(){switch(this._mathvariant){case"bold":return["\\textbf"];case"italic":return["\\textit"];case"bold-italic":return["\\textit","\\textbf"];case"double-struck":return["\\mathbb"];case"monospace":return["\\mathtt"];case"bold-fraktur":case"fraktur":return["\\mathfrak"];default:return["\\text"]}}}},7188:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MTr=void 0;const a=r(8398);t.MTr=class{constructor(e){this._mathmlElement=e}convert(){return this._mathmlElement.children.map(e=>(0,a.mathMLElementToLaTeXConverter)(e)).map(e=>e.convert()).join(" & ")}}},2710:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MUnderover=void 0;const a=r(8398),n=r(6036);t.MUnderover=class{constructor(e){this._mathmlElement=e}convert(){var{name:e,children:t}=this._mathmlElement,r=t.length;if(3!==r)throw new n.InvalidNumberOfChildrenError(e,3,r);return`${(0,a.mathMLElementToLaTeXConverter)(t[0]).convert()}_{${(0,a.mathMLElementToLaTeXConverter)(t[1]).convert()}}^{${(0,a.mathMLElementToLaTeXConverter)(t[2]).convert()}}`}}},6483:function(e,t,r){"use strict";var a=this&&this.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&a(t,e,r);return n(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.MathMLElementToLatexConverterAdapter=void 0;const i=o(r(1733)),l=(t.MathMLElementToLatexConverterAdapter=class{constructor(e){this._mathMLElement=e}toLatexConverter(){var e=this._mathMLElement["name"];return new(l[e]||i.GenericSpacingWrapper)(this._mathMLElement)}},{math:i.Math,mi:i.MI,mo:i.MO,mn:i.MN,msqrt:i.MSqrt,mfenced:i.MFenced,mfrac:i.MFrac,mroot:i.MRoot,maction:i.MAction,menclose:i.MEnclose,merror:i.MError,mphantom:i.MPhantom,msup:i.MSup,msub:i.MSub,msubsup:i.MSubsup,mmultiscripts:i.MMultiscripts,mtext:i.MText,munderover:i.MUnderover,mtable:i.MTable,mtr:i.MTr,mover:i.GenericUnderOver,munder:i.GenericUnderOver,mrow:i.GenericSpacingWrapper,mpadded:i.GenericSpacingWrapper})},965:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorHandler=void 0,t.ErrorHandler=class{constructor(){this._errors=[],this.errorLocator={}}fixError(e,t){return this._isMissingAttributeValueError(t)?(this._errors.push(t),this._fixMissingAttribute(t,e)):e}isThereAnyErrors(){return 0<this._errors.length}cleanErrors(){this._errors=[]}_fixMissingAttribute(e,t){e=e.split('"')[1];if(e)return t.replace(this._matchMissingValueForAttribute(e),"");for(;this._mathGenericMissingValue().exec(t);)t=t.replace(this._mathGenericMissingValue(),"$1$3");return t}_matchMissingValueForAttribute(e){return new RegExp(`(${e}=(?!("|')))|(${e}(?!("|')))`,"gm")}_mathGenericMissingValue(){return/(\<.* )(\w+=(?!\"|\'))(.*\>)/gm}_isMissingAttributeValueError(e){return!!e.includes("attribute")&&!!e.includes("missed")||e.includes("attribute value missed")}}},6177:function(e,t,r){"use strict";var a=this&&this.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(5242),t),n(r(965),t),n(r(1877),t)},1877:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ElementsToMathMLAdapter=void 0,t.ElementsToMathMLAdapter=class{convert(e){return e.filter(e=>void 0!==e.tagName).map(e=>this._convertElement(e))}_convertElement(e){return{name:e.tagName,attributes:this._convertElementAttributes(e.attributes),value:!this._hasElementChild(e)&&e.textContent||"",children:this._hasElementChild(e)?this.convert(Array.from(e.childNodes)):[]}}_convertElementAttributes(e){return Array.from(e).reduce((e,t)=>Object.assign({[t.nodeName]:t.nodeValue===t.nodeName?"":t.nodeValue},e),{})}_hasElementChild(e){e=e.childNodes;return!!e&&0!==e.length&&this._isThereAnyNoTextNode(e)}_isThereAnyNoTextNode(e){return Array.from(e).some(e=>"#text"!==e.nodeName)}}},5242:function(e,t,r){"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.XmlToMathMLAdapter=void 0;const n=a(r(3969));t.XmlToMathMLAdapter=class{constructor(e,t){this._xml="",this._elementsConvertor=e,this._errorHandler=t,this._xmlDOM=new n.default.DOMParser({locator:this._errorHandler.errorLocator,errorHandler:this._fixError.bind(this)})}convert(e){return this._xml=this._removeLineBreaks(e),this._elementsConvertor.convert(this._mathMLElements)}_fixError(e){this._xml=this._errorHandler.fixError(this._xml,e)}_removeLineBreaks(e){return e.replace(/\n|\r\n|\r/g,"")}get _mathMLElements(){let e=this._xmlDOM.parseFromString(this._xml).getElementsByTagName("math");return this._errorHandler.isThereAnyErrors()&&(this._errorHandler.cleanErrors(),e=this._xmlDOM.parseFromString(this._xml).getElementsByTagName("math")),Array.from(e)}}},4150:function(e,t,r){"use strict";var a=this&&this.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(488),t)},488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.makeToMathElementsConverter=void 0;const a=r(6177);t.makeToMathElementsConverter=()=>{var e=new a.ElementsToMathMLAdapter,t=new a.ErrorHandler;return new a.XmlToMathMLAdapter(e,t)}},8863:function(e,t,r){"use strict";var a=this&&this.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(862),t)},862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MathMLToLaTeX=void 0;const a=r(6483),n=r(4150);t.MathMLToLaTeX=class{static convert(e){return(0,n.makeToMathElementsConverter)().convert(e).map(e=>new a.MathMLElementToLatexConverterAdapter(e).toLatexConverter()).map(e=>e.convert()).join("").trim()}}},1868:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.allMathOperatorsByChar=void 0,t.allMathOperatorsByChar={_:"\\underline","&#x23E1;":"\\underbrace","&#x23E0;":"\\overbrace","&#x23DF;":"\\underbrace","&#x23DE;":"\\overbrace","&#x23DD;":"\\underbrace","&#x23DC;":"\\overbrace","&#x23B5;":"\\underbrace","&#x23B4;":"\\overbrace","&#x20DC;":"\\square","&#x20DB;":"\\square","&#x2064;":"","&#x2057;":"''''","&#x203E;":"\\bar","&#x2037;":"```","&#x2036;":"``","&#x2035;":"`","&#x2034;":"'''","&#x2033;":"''","&#x201F;":"``","&#x201E;":",,","&#x201B;":"`","&#x201A;":",","&#x302;":"\\hat","&#x2F7;":"\\sim","&#x2DD;":"\\sim","&#x2DC;":"\\sim","&#x2DA;":"\\circ","&#x2D9;":"\\cdot","&#x2D8;":"","&#x2CD;":"\\_","&#x2CB;":"ˋ","&#x2CA;":"ˊ","&#x2C9;":"ˉ","&#x2C7;":"","&#x2C6;":"\\hat","&#xBA;":"o","&#xB9;":"1","&#xB8;":"¸","&#xB4;":"´","&#xB3;":"3","&#xB2;":"2","&#xB0;":"\\circ","&#xAF;":"\\bar","&#xAA;":"a","&#xA8;":"\\cdot\\cdot","~":"\\sim","`":"`","^":"\\hat","--":"--","++":"++","&amp;":"\\&","&#x2061;":"","&#x221C;":"\\sqrt[4]{}","&#x221B;":"\\sqrt[3]{}","&#x221A;":"\\sqrt{}","&#x2146;":"d","&#x2145;":"\\mathbb{D}","?":"?","@":"@","//":"//","!!":"!!","!":"!","&#x266F;":"\\#","&#x266E;":"","&#x266D;":"","&#x2032;":"'","&lt;>":"<>","**":"\\star\\star","&#x2207;":"\\nabla","&#x2202;":"\\partial","&#x2299;":"\\bigodot","&#xAC;":"\\neg","&#x2222;":"\\measuredangle","&#x2221;":"\\measuredangle","&#x2220;":"\\angle","&#xF7;":"\\div","/":"/","&#x2216;":"\\backslash","\\":"\\backslash","%":"\\%","&#x2297;":"\\bigotimes","&#xB7;":"\\cdot","&#x2A3F;":"\\coprod","&#x2A2F;":"\\times","&#x22C5;":"\\cdot","&#x22A1;":"\\boxdot","&#x22A0;":"\\boxtimes","&#x2062;":"","&#x2043;":"-","&#x2022;":"\\cdot","&#xD7;":"\\times",".":".","*":"\\star","&#x222A;":"\\cup","&#x2229;":"\\cap","&#x2210;":"\\coprod","&#x220F;":"\\prod","&#x2240;":"","&#x2AFF;":"","&#x2AFC;":"\\mid\\mid\\mid","&#x2A09;":"\\times","&#x2A08;":"","&#x2A07;":"","&#x2A06;":"\\sqcup","&#x2A05;":"\\sqcap","&#x2A02;":"\\otimes","&#x2A00;":"\\odot","&#x22C2;":"\\cap","&#x22C1;":"\\vee","&#x22C0;":"\\wedge","&#x2A04;":"\\uplus","&#x2A03;":"\\cup","&#x22C3;":"\\cup","&#x2A1C;":"\\underline{\\int}","&#x2A1B;":"\\overline{\\int}","&#x2A1A;":"\\int","&#x2A19;":"\\int","&#x2A18;":"\\int","&#x2A17;":"\\int","&#x2A16;":"\\oint","&#x2A15;":"\\oint","&#x2A14;":"\\int","&#x2A13;":"\\int","&#x2A12;":"\\int","&#x2A11;":"\\int","&#x2A10;":"\\int","&#x2A0F;":"\\bcancel{\\int}","&#x2A0E;":"","&#x2A0D;":"\\hcancel{\\int}","&#x2A0C;":"\\iiiint","&#x2233;":"\\oint","&#x2232;":"\\oint","&#x2231;":"\\int","&#x2230;":"\\oiint","&#x222F;":"\\oiint","&#x222E;":"\\oint","&#x222B;":"\\int","&#x2A01;":"\\oplus","&#x2298;":"\\oslash","&#x2296;":"\\ominus","&#x2295;":"\\oplus","&#x222D;":"\\iiint","&#x222C;":"\\iint","&#x2A0B;":"","&#x2A0A;":"","&#x2211;":"\\sum","&#x229F;":"\\boxminus","&#x229E;":"\\boxplus","&#x2214;":"\\dot{+}","&#x2213;":"+-","&#x2212;":"-","&#xB1;":"\\pm","-":"-","+":"+","&#x2B46;":"\\Rrightarrow","&#x2B45;":"\\Lleftarrow","&#x29F4;":":\\rightarrow","&#x29EF;":"","&#x29DF;":"\\bullet-\\bullet","&#x299F;":"\\angle","&#x299E;":"\\measuredangle","&#x299D;":"\\measuredangle","&#x299C;":"\\perp","&#x299B;":"\\measuredangle","&#x299A;":"","&#x2999;":"\\vdots","&#x297F;":"","&#x297E;":"","&#x297D;":"\\prec","&#x297C;":"\\succ","&#x297B;":"\\underset{\\rightarrow}{\\supset}","&#x297A;":"","&#x2979;":"\\underset{\\rightarrow}{\\subset}","&#x2978;":"\\underset{\\rightarrow}{>}","&#x2977;":"","&#x2976;":"\\underset{\\leftarrow}{<}","&#x2975;":"\\underset{\\approx}{\\rightarrow}","&#x2974;":"\\underset{\\sim}{\\rightarrow}","&#x2973;":"\\underset{\\sim}{\\leftarrow}","&#x2972;":"\\overset{\\sim}{\\rightarrow}","&#x2971;":"\\overset{=}{\\rightarrow}","&#x2970;":"","&#x296F;":"","&#x296E;":"","&#x296D;":"\\overline{\\rightharpoondown}","&#x296C;":"\\underline{\\rightharpoonup}","&#x296B;":"\\overline{\\leftharpoondown}","&#x296A;":"\\underline{\\leftharpoonup}","&#x2969;":"\\rightleftharpoons","&#x2968;":"\\rightleftharpoons","&#x2967;":"\\rightleftharpoons","&#x2966;":"\\rightleftharpoons","&#x2965;":"\\Downarrow","&#x2964;":"\\Rightarrow","&#x2963;":"\\Uparrow","&#x2962;":"\\Leftarrow","&#x2961;":"\\downarrow","&#x2960;":"\\uparrow","&#x295F;":"\\rightarrow","&#x295E;":"\\leftarrow","&#x295D;":"\\downarrow","&#x295C;":"\\uparrow","&#x295B;":"\\rightarrow","&#x295A;":"\\leftarrow","&#x2959;":"\\downarrow","&#x2958;":"\\uparrow","&#x2957;":"\\rightarrow","&#x2956;":"\\leftarrow","&#x2955;":"\\downarrow","&#x2954;":"\\uparrow","&#x2953;":"\\rightarrow","&#x2952;":"\\leftarrow","&#x2951;":"\\updownarrow","&#x2950;":"\\leftrightarrow","&#x294F;":"\\updownarrow","&#x294E;":"\\leftrightarrow","&#x294D;":"\\updownarrow","&#x294C;":"\\updownarrow","&#x294B;":"\\leftrightarrow","&#x294A;":"\\leftrightarrow","&#x2949;":"","&#x2948;":"\\leftrightarrow","&#x2947;":"\\nrightarrow","&#x2946;":"","&#x2945;":"","&#x2944;":"\\rightleftarrows","&#x2943;":"\\leftrightarrows","&#x2942;":"\\rightleftarrows","&#x2941;":"\\circlearrowright","&#x2940;":"\\circlearrowleft","&#x293F;":"\\rightarrow","&#x293E;":"\\leftarrow","&#x293D;":"","&#x293C;":"","&#x293B;":"","&#x293A;":"","&#x2939;":"","&#x2938;":"","&#x2937;":"\\Rsh","&#x2936;":"\\Lsh","&#x2935;":"\\downarrow","&#x2934;":"\\uparrow","&#x2933;":"\\leadsto","&#x2932;":"","&#x2931;":"","&#x2930;":"","&#x292F;":"","&#x292E;":"","&#x292D;":"","&#x292C;":"\\times","&#x292B;":"\\times","&#x292A;":"","&#x2929;":"","&#x2928;":"","&#x2927;":"","&#x2926;":"","&#x2925;":"","&#x2924;":"","&#x2923;":"","&#x2922;":"","&#x2921;":"","&#x2920;":"\\mapsto\\cdot","&#x291F;":"\\cdot\\leftarrow","&#x291E;":"\\rightarrow\\cdot","&#x291D;":"\\leftarrow","&#x291C;":"\\rightarrow","&#x291B;":"\\leftarrow","&#x291A;":"\\rightarrow","&#x2919;":"\\leftarrow","&#x2918;":"\\rightarrow","&#x2917;":"\\rightarrow","&#x2916;":"\\rightarrow","&#x2915;":"\\rightarrow","&#x2914;":"\\rightarrow","&#x2913;":"\\downarrow","&#x2912;":"\\uparrow","&#x2911;":"\\rightarrow","&#x2910;":"\\rightarrow","&#x290F;":"\\rightarrow","&#x290E;":"\\leftarrow","&#x290D;":"\\rightarrow","&#x290C;":"\\leftarrow","&#x290B;":"\\Downarrow","&#x290A;":"\\Uparrow","&#x2909;":"\\uparrow","&#x2908;":"\\downarrow","&#x2907;":"\\Rightarrow","&#x2906;":"\\Leftarrow","&#x2905;":"\\mapsto","&#x2904;":"\\nLeftrightarrow","&#x2903;":"\\nRightarrow","&#x2902;":"\\nLeftarrow","&#x2901;":"\\rightsquigarrow","&#x2900;":"\\rightsquigarrow","&#x27FF;":"\\rightsquigarrow","&#x27FE;":"\\Rightarrow","&#x27FD;":"\\Leftarrow","&#x27FC;":"\\mapsto","&#x27FB;":"\\leftarrow","&#x27FA;":"\\Longleftrightarrow","&#x27F9;":"\\Longrightarrow","&#x27F8;":"\\Longleftarrow","&#x27F7;":"\\leftrightarrow","&#x27F6;":"\\rightarrow","&#x27F5;":"\\leftarrow","&#x27F1;":"\\Downarrow","&#x27F0;":"\\Uparrow","&#x22B8;":"\\rightarrow","&#x21FF;":"\\leftrightarrow","&#x21FE;":"\\rightarrow","&#x21FD;":"\\leftarrow","&#x21FC;":"\\nleftrightarrow","&#x21FB;":"\\nrightarrow","&#x21FA;":"\\nleftarrow","&#x21F9;":"\\nleftrightarrow","&#x21F8;":"\\nrightarrow","&#x21F7;":"\\nleftarrow","&#x21F6;":"\\Rrightarrow","&#x21F5;":"","&#x21F4;":"\\rightarrow","&#x21F3;":"\\Updownarrow","&#x21F2;":"\\searrow","&#x21F1;":"\\nwarrow","&#x21F0;":"\\Leftarrow","&#x21EF;":"\\Uparrow","&#x21EE;":"\\Uparrow","&#x21ED;":"\\Uparrow","&#x21EC;":"\\Uparrow","&#x21EB;":"\\Uparrow","&#x21EA;":"\\Uparrow","&#x21E9;":"\\Downarrow","&#x21E8;":"\\Rightarrow","&#x21E7;":"\\Uparrow","&#x21E6;":"\\Leftarrow","&#x21E5;":"\\rightarrow","&#x21E4;":"\\leftarrow","&#x21E3;":"\\downarrow","&#x21E2;":"\\rightarrow","&#x21E1;":"\\uparrow","&#x21E0;":"\\leftarrow","&#x21DF;":"\\downarrow","&#x21DE;":"\\uparrow","&#x21DD;":"\\rightsquigarrow","&#x21DC;":"\\leftarrow","&#x21DB;":"\\Rrightarrow","&#x21DA;":"\\Lleftarrow","&#x21D9;":"\\swarrow","&#x21D8;":"\\searrow","&#x21D7;":"\\nearrow","&#x21D6;":"\\nwarrow","&#x21D5;":"\\Updownarrow","&#x21D4;":"\\Leftrightarrow","&#x21D3;":"\\Downarrow","&#x21D2;":"\\Rightarrow","&#x21D1;":"\\Uparrow","&#x21D0;":"\\Leftarrow","&#x21CF;":"\\nRightarrow","&#x21CE;":"\\nLeftrightarrow","&#x21CD;":"\\nLeftarrow","&#x21CC;":"\\rightleftharpoons","&#x21CB;":"\\leftrightharpoons","&#x21CA;":"\\downdownarrows","&#x21C9;":"\\rightrightarrows","&#x21C8;":"\\upuparrows","&#x21C7;":"\\leftleftarrows","&#x21C6;":"\\leftrightarrows","&#x21C5;":"","&#x21C4;":"\\rightleftarrows","&#x21C3;":"\\downharpoonleft","&#x21C2;":"\\downharpoonright","&#x21C1;":"\\rightharpoondown","&#x21C0;":"\\rightharpoonup","&#x21BF;":"\\upharpoonleft","&#x21BE;":"\\upharpoonright","&#x21BD;":"\\leftharpoondown","&#x21BC;":"\\leftharpoonup","&#x21BB;":"\\circlearrowright","&#x21BA;":"\\circlearrowleft","&#x21B9;":"\\leftrightarrows","&#x21B8;":"\\overline{\\nwarrow}","&#x21B7;":"\\curvearrowright","&#x21B6;":"\\curvearrowleft","&#x21B5;":"\\swarrow","&#x21B4;":"\\searrow","&#x21B3;":"\\Rsh","&#x21B2;":"\\Lsh","&#x21B1;":"\\Rsh","&#x21B0;":"\\Lsh","&#x21AF;":"\\swarrow","&#x21AE;":"","&#x21AD;":"\\leftrightsquigarrow","&#x21AC;":"\\looparrowright","&#x21AB;":"\\looparrowleft","&#x21AA;":"\\hookrightarrow","&#x21A9;":"\\hookleftarrow","&#x21A8;":"\\underline{\\updownarrow}","&#x21A7;":"\\downarrow","&#x21A6;":"\\rightarrowtail","&#x21A5;":"\\uparrow","&#x21A4;":"\\leftarrowtail","&#x21A3;":"\\rightarrowtail","&#x21A2;":"\\leftarrowtail","&#x21A1;":"\\downarrow","&#x21A0;":"\\twoheadrightarrow","&#x219F;":"\\uparrow","&#x219E;":"\\twoheadleftarrow","&#x219D;":"\\nearrow","&#x219C;":"\\nwarrow","&#x219B;":"","&#x219A;":"","&#x2199;":"\\swarrow","&#x2198;":"\\searrow","&#x2197;":"\\nearrow","&#x2196;":"\\nwarrow","&#x2195;":"\\updownarrow","&#x2194;":"\\leftrightarrow","&#x2193;":"\\downarrow","&#x2192;":"\\rightarrow","&#x2191;":"\\uparrow","&#x2190;":"\\leftarrow","|||":"\\left|||\\right.","||":"\\left||\\right.","|":"\\left|\\right.","&#x2AFE;":"","&#x2AFD;":"//","&#x2AFB;":"///","&#x2AFA;":"","&#x2AF9;":"","&#x2AF8;":"","&#x2AF7;":"","&#x2AF6;":"\\vdots","&#x2AF5;":"","&#x2AF4;":"","&#x2AF3;":"","&#x2AF2;":"\\nparallel","&#x2AF1;":"","&#x2AF0;":"","&#x2AEF;":"","&#x2AEE;":"\\bcancel{\\mid}","&#x2AED;":"","&#x2AEC;":"","&#x2AEB;":"","&#x2AEA;":"","&#x2AE9;":"","&#x2AE8;":"\\underline{\\perp}","&#x2AE7;":"\\overline{\\top}","&#x2AE6;":"","&#x2AE5;":"","&#x2AE4;":"","&#x2AE3;":"","&#x2AE2;":"","&#x2AE1;":"","&#x2AE0;":"\\perp","&#x2ADF;":"\\top","&#x2ADE;":"\\dashv","&#x2ADD;&#x338;":"","&#x2ADD;":"","&#x2ADB;":"\\pitchfork","&#x2ADA;":"","&#x2AD9;":"","&#x2AD8;":"","&#x2AD7;":"","&#x2AD6;":"","&#x2AD5;":"","&#x2AD4;":"","&#x2AD3;":"","&#x2AD2;":"","&#x2AD1;":"","&#x2AD0;":"","&#x2ACF;":"","&#x2ACE;":"","&#x2ACD;":"","&#x2ACC;":"\\underset{\\neq}{\\supset}","&#x2ACB;":"\\underset{\\neq}{\\subset}","&#x2ACA;":"\\underset{\\approx}{\\supset}","&#x2AC9;":"\\underset{\\approx}{\\subset}","&#x2AC8;":"\\underset{\\sim}{\\supset}","&#x2AC7;":"\\underset{\\sim}{\\subset}","&#x2AC6;":"\\supseteqq","&#x2AC5;":"\\subseteqq","&#x2AC4;":"\\dot{\\supseteq}","&#x2AC3;":"\\dot{\\subseteq}","&#x2AC2;":"\\underset{\\times}{\\supset}","&#x2AC1;":"\\underset{\\times}{\\subset}","&#x2AC0;":"\\underset{+}{\\supset}","&#x2ABF;":"\\underset{+}{\\subset}","&#x2ABE;":"","&#x2ABD;":"","&#x2ABC;":"\\gg ","&#x2ABB;":"\\ll","&#x2ABA;":"\\underset{\\cancel{\\approx}}{\\succ}","&#x2AB9;":"\\underset{\\cancel{\\approx}}{\\prec}","&#x2AB8;":"\\underset{\\approx}{\\succ}","&#x2AB7;":"\\underset{\\approx}{\\prec}","&#x2AB6;":"\\underset{\\cancel{=}}{\\succ}","&#x2AB5;":"\\underset{\\cancel{=}}{\\prec}","&#x2AB4;":"\\underset{=}{\\succ}","&#x2AB3;":"\\underset{=}{\\prec}","&#x2AB2;":"","&#x2AB1;":"","&#x2AAE;":"","&#x2AAD;":"\\underline{\\hcancel{>}}","&#x2AAC;":"\\underline{\\hcancel{>}}","&#x2AAB;":"\\hcancel{>}","&#x2AAA;":"\\hcancel{<}","&#x2AA9;":"","&#x2AA8;":"","&#x2AA7;":"\\vartriangleright","&#x2AA6;":"\\vartriangleleft","&#x2AA5;":"><","&#x2AA4;":"><","&#x2AA3;":"\\underline{\\ll}","&#x2AA2;&#x338;":"\\cancel{\\gg}","&#x2AA2;":"\\gg","&#x2AA1;&#x338;":"\\cancel{\\ll}","&#x2AA1;":"\\ll","&#x2AA0;":"\\overset{\\sim}{\\geqq}","&#x2A9F;":"\\overset{\\sim}{\\leqq}","&#x2A9E;":"\\overset{\\sim}{>}","&#x2A9D;":"\\overset{\\sim}{<}","&#x2A9C;":"","&#x2A9B;":"","&#x2A9A;":"\\overset{=}{>}","&#x2A99;":"\\overset{=}{<}","&#x2A98;":"","&#x2A97;":"","&#x2A96;":"","&#x2A95;":"","&#x2A94;":"","&#x2A93;":"","&#x2A92;":"\\underset{=}{\\gtrless}","&#x2A91;":"\\underset{=}{\\lessgtr}","&#x2A90;":"\\underset{<}{\\gtrsim}","&#x2A8F;":"\\underset{>}{\\lesssim}","&#x2A8E;":"\\underset{\\simeq}{>}","&#x2A8D;":"\\underset{\\simeq}{<}","&#x2A8C;":"\\gtreqqless","&#x2A8B;":"\\lesseqqgtr","&#x2A8A;":"\\underset{\\cancel{\\approx}}{>}","&#x2A89;":"\\underset{\\approx}{<}","&#x2A86;":"\\underset{\\approx}{>}","&#x2A85;":"\\underset{\\approx}{<}","&#x2A84;":"","&#x2A83;":"","&#x2A82;":"","&#x2A81;":"","&#x2A80;":"","&#x2A7F;":"","&#x2A7E;&#x338;":"\\bcancel{\\geq}","&#x2A7E;":"\\geq","&#x2A7D;&#x338;":"\\bcancel{\\leq}","&#x2A7D;":"\\leq","&#x2A7C;":"","&#x2A7B;":"","&#x2A7A;":"","&#x2A79;":"","&#x2A78;":"\\overset{\\dots}{\\equiv}","&#x2A77;":"","&#x2A76;":"===","&#x2A75;":"==","&#x2A74;":"::=","&#x2A73;":"","&#x2A72;":"\\underset{=}{+}","&#x2A71;":"\\overset{=}{+}","&#x2A70;":"\\overset{\\approx}{=}","&#x2A6F;":"\\overset{\\wedge}{=}","&#x2A6E;":"\\overset{*}{=}","&#x2A6D;":"\\dot{\\approx}","&#x2A6C;":"","&#x2A6B;":"","&#x2A6A;":"\\dot{\\sim}","&#x2A69;":"","&#x2A68;":"","&#x2A67;":"\\dot{\\equiv}","&#x2A66;":"\\underset{\\cdot}{=}","&#x2A65;":"","&#x2A64;":"","&#x2A63;":"\\underset{=}{\\vee}","&#x2A62;":"\\overset{=}{\\vee}","&#x2A61;":"ul(vv)","&#x2A60;":"\\underset{=}{\\wedge}","&#x2A5F;":"\\underline{\\wedge}","&#x2A5E;":"\\overset{=}{\\wedge}","&#x2A5D;":"\\hcancel{\\vee}","&#x2A5C;":"\\hcancel{\\wedge}","&#x2A5B;":"","&#x2A5A;":"","&#x2A59;":"","&#x2A58;":"\\vee","&#x2A57;":"\\wedge","&#x2A56;":"","&#x2A55;":"","&#x2A54;":"","&#x2A53;":"","&#x2A52;":"\\dot{\\vee}","&#x2A51;":"\\dot{\\wedge}","&#x2A50;":"","&#x2A4F;":"","&#x2A4E;":"","&#x2A4D;":"\\overline{\\cap}","&#x2A4C;":"\\overline{\\cup}","&#x2A4B;":"","&#x2A4A;":"","&#x2A49;":"","&#x2A48;":"","&#x2A47;":"","&#x2A46;":"","&#x2A45;":"","&#x2A44;":"","&#x2A43;":"\\overline{\\cap}","&#x2A42;":"\\overline{\\cup}","&#x2A41;":"","&#x2A40;":"","&#x2A3E;":"","&#x2A3D;":"\\llcorner","&#x2A3C;":"\\lrcorner","&#x2A3B;":"","&#x2A3A;":"","&#x2A39;":"","&#x2A38;":"","&#x2A37;":"","&#x2A36;":"\\hat{\\otimes}","&#x2A35;":"","&#x2A34;":"","&#x2A33;":"","&#x2A32;":"\\underline{\\times}","&#x2A31;":"\\underline{\\times}","&#x2A30;":"\\dot{\\times}","&#x2A2E;":"","&#x2A2D;":"","&#x2A2C;":"","&#x2A2B;":"","&#x2A2A;":"","&#x2A29;":"","&#x2A28;":"","&#x2A27;":"","&#x2A26;":"\\underset{\\sim}{+}","&#x2A25;":"\\underset{\\circ}{+}","&#x2A24;":"\\overset{\\sim}{+}","&#x2A23;":"\\hat{+}","&#x2A22;":"\\dot{+}","&#x2A21;":"\\upharpoonright","&#x2A20;":">>","&#x2A1F;":"","&#x2A1E;":"\\triangleleft","&#x2A1D;":"\\bowtie","&#x29FF;":"","&#x29FE;":"+","&#x29FB;":"\\hcancel{|||}","&#x29FA;":"\\hcancel{||}","&#x29F9;":"\\backslash","&#x29F8;":"/","&#x29F7;":"hcancel{\backslash}","&#x29F6;":"","&#x29F5;":"\\backslash","&#x29F2;":"\\Phi","&#x29F1;":"","&#x29F0;":"","&#x29EE;":"","&#x29ED;":"","&#x29EC;":"","&#x29EB;":"\\lozenge","&#x29EA;":"","&#x29E9;":"","&#x29E8;":"","&#x29E7;":"\\ddagger","&#x29E2;":"\\sqcup\\sqcup","&#x29E1;":"","&#x29E0;":"\\square","&#x29DE;":"","&#x29DD;":"","&#x29DC;":"","&#x29DB;":"\\{\\{","&#x29D9;":"\\{","&#x29D8;":"\\}","&#x29D7;":"","&#x29D6;":"","&#x29D5;":"\\bowtie","&#x29D4;":"\\bowtie","&#x29D3;":"\\bowtie","&#x29D2;":"\\bowtie","&#x29D1;":"\\bowtie","&#x29D0;&#x338;":"| \\not\\triangleright","&#x29D0;":"| \\triangleright","&#x29CF;&#x338;":"\\not\\triangleleft |","&#x29CF;":"\\triangleleft |","&#x29CE;":"","&#x29CD;":"\\triangle","&#x29CC;":"","&#x29CB;":"\\underline{\\triangle}","&#x29CA;":"\\dot{\\triangle}","&#x29C9;":"","&#x29C8;":"\\boxed{\\circ}","&#x29C7;":"\\boxed{\\circ}","&#x29C6;":"\\boxed{\\rightarrow}","&#x29C5;":"\\bcancel{\\square}","&#x29C4;":"\\cancel{\\square}","&#x29C3;":"\\odot","&#x29C2;":"\\odot","&#x29BF;":"\\odot","&#x29BE;":"\\odot","&#x29BD;":"\\varnothing","&#x29BC;":"\\oplus","&#x29BB;":"\\otimes","&#x29BA;":"","&#x29B9;":"\\varnothing","&#x29B8;":"\\varnothing","&#x29B7;":"\\ominus","&#x29B6;":"\\ominus","&#x29B5;":"\\ominus","&#x29B4;":"\\vec{\\varnothing}","&#x29B3;":"\\vec{\\varnothing}","&#x29B2;":"\\dot{\\varnothing}","&#x29B1;":"\\overline{\\varnothing}","&#x29B0;":"\\varnothing","&#x29AF;":"","&#x29AE;":"","&#x29AD;":"","&#x29AC;":"","&#x29AB;":"","&#x29AA;":"","&#x29A9;":"","&#x29A8;":"","&#x29A7;":"","&#x29A6;":"","&#x29A5;":"","&#x29A4;":"","&#x29A3;":"","&#x29A2;":"","&#x29A1;":"\\not\\lor","&#x29A0;":"\\bcancel{>}","&#x2982;":":","&#x2981;":"\\circ","&#x2758;":"|","&#x25B2;":"\\bigtriangleup","&#x22FF;":"\\Epsilon","&#x22FE;":"\\overline{\\ni}","&#x22FD;":"\\overline{\\ni}","&#x22FC;":"\\in","&#x22FB;":"\\in","&#x22FA;":"\\in","&#x22F9;":"\\underline{\\in}","&#x22F8;":"\\underline{\\in}","&#x22F7;":"\\overline{\\in}","&#x22F6;":"\\overline{\\in}","&#x22F5;":"\\dot{\\in}","&#x22F4;":"\\in","&#x22F3;":"\\in","&#x22F2;":"\\in","&#x22F0;":"\\ddots","&#x22E9;":"\\underset{\\sim}{\\succ}","&#x22E8;":"\\underset{\\sim}{\\prec}","&#x22E7;":"\\underset{\\not\\sim}{>}","&#x22E6;":"\\underset{\\not\\sim}{<}","&#x22E5;":"\\not\\sqsupseteq","&#x22E4;":"\\not\\sqsubseteq","&#x22E3;":"\\not\\sqsupseteq","&#x22E2;":"\\not\\sqsubseteq","&#x22E1;":"\\nsucc","&#x22E0;":"\\nprec","&#x22DF;":"\\succ","&#x22DE;":"\\prec","&#x22DD;":"\\overline{>}","&#x22DC;":"\\overline{<}","&#x22DB;":"\\underset{>}{\\leq}","&#x22DA;":"\\underset{<}{\\geq}","&#x22D5;":"\\#","&#x22D3;":"\\cup","&#x22D2;":"\\cap","&#x22D1;":"\\supset","&#x22D0;":"\\subset","&#x22CF;":"\\wedge","&#x22CE;":"\\vee","&#x22CD;":"\\simeq","&#x22C8;":"\\bowtie","&#x22C7;":"\\ast","&#x22C6;":"\\star","&#x22C4;":"\\diamond","&#x22BF;":"\\triangle","&#x22BE;":"\\measuredangle","&#x22BD;":"\\overline{\\lor}","&#x22BC;":"\\overline{\\land}","&#x22BB;":"\\underline{\\lor}","&#x22BA;":"\\top","&#x22B9;":"","&#x22B7;":"\\circ\\multimap","&#x22B6;":"\\circ\\multimap","&#x22B3;":"\\triangleright","&#x22B2;":"\\triangleleft","&#x22B1;":"\\succ","&#x22B0;":"\\prec","&#x22AB;":"|\\models","&#x22AA;":"|\\models","&#x22A7;":"\\models","&#x22A6;":"\\vdash","&#x229D;":"\\ominus","&#x229C;":"\\ominus","&#x229B;":"\\odot","&#x229A;":"\\odot","&#x2294;":"\\sqcup","&#x2293;":"\\sqcap","&#x2292;":"\\sqsupseteq","&#x2291;":"\\sqsubseteq","&#x2290;&#x338;":"\\not\\sqsupset","&#x2290;":"\\sqsupset","&#x228F;&#x338;":"\\not\\sqsubset","&#x228F;":"\\sqsubset","&#x228E;":"\\cup","&#x228D;":"\\cup","&#x228C;":"\\cup","&#x227F;&#x338;":"\\not\\succsim","&#x227F;":"\\succsim","&#x227E;":"\\precsim","&#x2279;":"\\not\\overset{>}{<}","&#x2278;":"\\not\\overset{>}{<}","&#x2277;":"\\overset{>}{<}","&#x2276;":"\\overset{<}{>}","&#x2275;":"\\not\\geg","&#x2274;":"\\not\\leq","&#x2273;":"\\geg","&#x2272;":"\\leq","&#x226C;":"","&#x2267;":"\\geg","&#x2266;&#x338;":"\\not\\leq","&#x2266;":"\\leq","&#x2263;":"\\overset{=}{=} ","&#x225E;":"\\overset{m}{=} ","&#x225D;":"\\overset{def}{=}","&#x2258;":"=","&#x2256;":"=","&#x2255;":"=:","&#x2253;":"\\doteq","&#x2252;":"\\doteq","&#x2251;":"\\doteq","&#x2250;":"\\doteq","&#x224F;&#x338;":"","&#x224F;":"","&#x224E;&#x338;":"","&#x224E;":"","&#x224C;":"\\approx","&#x224B;":"\\approx","&#x224A;":"\\approx","&#x2242;&#x338;":"\\neq","&#x2242;":"=","&#x223F;":"\\sim","&#x223E;":"\\infty","&#x223D;&#x331;":"\\sim","&#x223D;":"\\sim","&#x223B;":"\\sim","&#x223A;":":-:","&#x2239;":"-:","&#x2238;":"\\bot","&#x2237;":"::","&#x2236;":":","&#x2223;":"|","&#x221F;":"\\llcorner","&#x2219;":"\\cdot","&#x2218;":"\\circ","&#x2217;":"*","&#x2215;":"/","&#x220E;":"\\square","&#x220D;":"\\ni","&#x220A;":"\\in","&#x2206;":"\\Delta","&#x2044;":"/","&#x2AB0;&#x338;":"\\nsucceq","&#x2AB0;":"\\succeq","&#x2AAF;&#x338;":"\\npreceq","&#x2AAF;":"\\preceq","&#x2A88;":"\\ngeqslant","&#x2A87;":"\\nleqslant","&#x29F3;":"\\Phi","&#x29E6;":"\\models","&#x29E5;":"\\not\\equiv","&#x29E4;":"\\approx\\neq","&#x29E3;":"\\neq","&#x29C1;":"\\circle","&#x29C0;":"\\circle","&#x25E6;":"\\circle","&#x25D7;":"\\circle","&#x25D6;":"\\circle","&#x25CF;":"\\circle","&#x25CE;":"\\circledcirc","&#x25CD;":"\\circledcirc","&#x25CC;":"\\circledcirc","&#x25C9;":"\\circledcirc","&#x25C8;":"\\diamond","&#x25C7;":"\\diamond","&#x25C6;":"\\diamond","&#x25C5;":"\\triangleleft","&#x25C4;":"\\triangleleft","&#x25C3;":"\\triangleleft","&#x25C2;":"\\triangleleft","&#x25C1;":"\\triangleleft","&#x25C0;":"\\triangleleft","&#x25BF;":"\\triangledown","&#x25BE;":"\\triangledown","&#x25BD;":"\\triangledown","&#x25BC;":"\\triangledown","&#x25B9;":"\\triangleright","&#x25B8;":"\\triangleright","&#x25B7;":"\\triangleright","&#x25B6;":"\\triangleright","&#x25B5;":"\\triangle","&#x25B4;":"\\triangle","&#x25B3;":"\\triangle","&#x25B1;":"\\square","&#x25B0;":"\\square","&#x25AF;":"\\square","&#x25AE;":"\\square","&#x25AD;":"\\square","&#x25AB;":"\\square","&#x25AA;":"\\square","&#x25A1;":"\\square","&#x25A0;":"\\square","&#x22ED;":"\\not\\triangleright","&#x22EC;":"\\not\\triangleleft","&#x22EB;":"\\not\\triangleright","&#x22EA;":"\\not\\triangleleft","&#x22D9;":"\\ggg","&#x22D8;":"\\lll","&#x22D7;":"*>","&#x22D6;":"<*","&#x22D4;":"\\pitchfork","&#x22CC;":"","&#x22CB;":"","&#x22CA;":"\\rtimes","&#x22C9;":"\\ltimes","&#x22B5;":"\\triangleright","&#x22B4;":"","&#x22A5;":"\\bot","&#x2281;":"\\nsucc","&#x2280;":"\\preceq","&#x227D;":"\\succeq","&#x227C;":"\\preceq","&#x227B;":"\\succ","&#x227A;":"\\prec","&#x2271;":"\\geq/","&#x2270;":"\\leq/","&#x226D;":"\\neq","&#x226B;&#x338;":"\\not\\gg","&#x226B;":"\\gg","&#x226A;&#x338;":"\\not\\ll","&#x226A;":"\\ll","&#x2269;":"\\ngeqslant","&#x2268;":"\\nleqslant","&#x2261;":"\\equiv","&#x225F;":"\\doteq","&#x225C;":"\\triangleq","&#x225B;":"\\doteq","&#x225A;":"\\triangleq","&#x2259;":"\\triangleq","&#x2257;":"\\doteq","&#x2254;":":=","&#x224D;":"\\asymp","&#x2247;":"\\ncong","&#x2246;":"\\ncong","&#x2245;":"\\cong","&#x2244;":"\\not\\simeq","&#x2243;":"\\simeq","&#x2241;":"\\not\\sim","&#x2226;":"\\not\\parallel","&#x2225;":"\\parallel","&#x2224;":"\\not|","&#x221D;":"\\propto","==":"==","=":"=",":=":":=","/=":"=","-=":"-=","+=":"+=","*=":"*=","!=":"!=","&#x2260;":"\\neq","&#x2262;":"\\equiv /","&#x2249;":"\\approx /","&#x223C;":"sim","&#x2248;":"\\approx","&#x226E;":"</","&lt;":"<","&#x226F;":">/",">=":">=",">":">","&#x2265;":"\\geq","&#x2264;":"\\leq","&lt;=":"<=","&#x228B;":"\\supsetneq","&#x228A;":"\\subsetneq","&#x2289;":"\\nsupseteq","&#x2288;":"\\nsubseteq","&#x2287;":"\\supseteq","&#x2286;":"\\subseteq","&#x2285;":"\\not\\supset","&#x2284;":"\\not\\subset","&#x2283;&#x20D2;":"\\supset |","&#x2283;":"\\supset","&#x2282;&#x20D2;":"\\subset |","&#x2282;":"\\subset","&#x220C;":"\\not\\in","&#x2209;":"\\notin","&#x2208;":"\\in","&#x2201;":"C","&#x2204;":"\\nexists","&#x2203;":"\\exists","&#x2200;":"\\forall","&#x2227;":"\\land","&amp;&amp;":"\\&\\&","&#x2228;":"\\lor","&#x22AF;":"\\cancel{\\vDash}","&#x22AE;":"\\cancel{\\Vdash}","&#x22AD;":"\\nvDash","&#x22AC;":"\\nvDash","&#x22A9;":"\\Vdash","&#x22A8;":"\\vDash","&#x22A4;":"\\top","&#x22A3;":"\\dashv","&#x22A2;":"\\vdash","&#x220B;":"\\ni","&#x22F1;":"\\ddots","&#x22EF;":"\\hdots","&#x22EE;":"\\vdots","&#x2026;":"\\hdots","&#x3F6;":"\\ni",":":":","...":"\\cdots","..":"..","->":"->","&#x2235;":"\\because","&#x2234;":"\\therefore ","&#x2063;":"",",":",",";":";","&#x29FD;":"\\}","&#x29FC;":"\\{","&#x2998;":"\\]","&#x2997;":"\\[","&#x2996;":"\\ll","&#x2995;":"\\gg","&#x2994;":"\\gg","&#x2993;":"\\ll","&#x2992;":"\\gg","&#x2991;":"\\ll","&#x2990;":"\\]","&#x298F;":"\\]","&#x298E;":"\\]","&#x298D;":"\\[","&#x298C;":"\\[","&#x298B;":"\\]","&#x298A;":"\\triangleright","&#x2989;":"\\triangleleft","&#x2988;":"|\\)","&#x2987;":"\\(|","&#x2986;":"|\\)","&#x2985;":"\\(\\(","&#x2984;":"|\\}","&#x2983;":"\\{|","&#x2980;":"\\||","&#x27EF;":"\\left. \\right]","&#x27EE;":"\\left[ \\right.","&#x27ED;":"\\left. \\right]]","&#x27EC;":"\\left[[ \\right.","&#x27EB;":"\\gg","&#x27EA;":"\\ll","&#x27E9;":"\\rangle","&#x27E8;":"\\langle","&#x27E7;":"\\left. \\right]]","&#x27E6;":"\\left[[ \\right.","&#x2773;":"\\left.\\right)","&#x2772;":"\\left(\\right.","&#x232A;":"\\rangle","&#x2329;":"\\langle","&#x230B;":"\\rfloor","&#x230A;":"\\lfloor","&#x2309;":"\\rceil","&#x2308;":"\\lceil","&#x2016;":"\\parallel","}":"\\left.\\right\\}","{":"\\left\\{\\right.","]":"\\left]\\right.","[":"\\left[\\right.",")":"\\left.\\right)","(":"\\left(\\right.","&#x201D;":'"',"&#x201C;":"``","&#x2019;":"'","&#x2018;":"`","%CE%B1":"\\alpha","%CE%B2":"\\beta","%CE%B3":"\\gamma","%CE%93":"\\Gamma","%CE%B4":"\\delta","%CE%94":"\\Delta","%CF%B5":"\\epsilon","%CE%B6":"\\zeta","%CE%B7":"\\eta","%CE%B8":"\\theta","%CE%98":"\\Theta","%CE%B9":"\\iota","%CE%BA":"\\kappa","%CE%BB":"\\lambda","%CE%BC":"\\mu","%CE%BD":"\\nu","%CE%BF":"\\omicron","%CF%80":"\\pi","%CE%A0":"\\Pi","%CF%81":"\\pho","%CF%83":"\\sigma","%CE%A3":"\\Sigma","%CF%84":"\\tau","%CF%85":"\\upsilon","%CE%A5":"\\Upsilon","%CF%95":"\\phi","%CE%A6":"\\Phi","%CF%87":"\\chi","%CF%88":"\\psi","%CE%A8":"\\Psi","%CF%89":"\\omega","%CE%A9":"\\Omega"}},598:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.allMathOperatorsByGlyph=void 0,t.allMathOperatorsByGlyph={_:"\\underline","⏡":"\\underbrace","⏠":"\\overbrace","⏟":"\\underbrace","⏞":"\\overbrace","⏝":"\\underbrace","⏜":"\\overbrace","⎵":"\\underbrace","⎴":"\\overbrace","⃜":"\\square","⃛":"\\square","⁤":"","⁗":"''''","‾":"\\overline","‷":"```","‶":"``","‵":"`","‴":"'''","″":"''","‟":"``","„":",,","‛":"`","‚":",","^":"\\hat","˷":"\\sim","˝":"\\sim","˜":"\\sim","˚":"\\circ","˙":"\\cdot","˘":" ","ˍ":"\\_","ˋ":"ˋ","ˊ":"ˊ","ˉ":"ˉ","ˇ":"","ˆ":"\\hat","º":"o","¹":"1","¸":",","´":"´","³":"3","²":"2","°":"\\circ","¯":"\\bar","ª":"a","↛":"\\nrightarrow","¨":"\\cdot\\cdot","~":"\\sim","`":"`","--":"--","++":"++","&":"\\&","∜":"\\sqrt[4]{}","∛":"\\sqrt[3]{}","√":"\\sqrt{}","ⅆ":"d","ⅅ":"\\mathbb{D}","?":"?","@":"@","//":"//","!!":"!!","!":"!","♯":"\\#","♮":"","♭":"","′":"'","<>":"<>","**":"\\star\\star","∇":"\\nabla","∂":"\\partial","⊙":"\\bigodot","¬":"\\neg","∢":"\\measuredangle","∡":"\\measuredangle","∠":"\\angle","÷":"\\div","/":"/","∖":"\\backslash","\\":"\\backslash","%":"\\%","⊗":"\\bigotimes","·":"\\cdot","⨿":"\\coprod","⨯":"\\times","⋅":"\\cdot","⊡":"\\boxdot","⊠":"\\boxtimes","⁢":"","⁃":"-","•":"\\cdot",".":".","*":"\\star","∪":"\\cup","∩":"\\cap","∐":"\\coprod","∏":"\\prod","≀":"","⫿":"","⫼":"\\mid\\mid\\mid","⨉":"\\times","⨈":"","⨇":"","⨆":"\\sqcup","⨅":"\\sqcap","⨂":"\\otimes","⨀":"\\odot","⋂":"\\cap","⋁":"\\vee","⋀":"\\wedge","⨄":"\\uplus","⨃":"\\cup","⋃":"\\cup","⨜":"\\underline{\\int}","⨛":"\\overline{\\int}","⨚":"\\int","⨙":"\\int","⨘":"\\int","⨗":"\\int","⨖":"\\oint","⨕":"\\oint","⨔":"\\int","⨓":"\\int","⨒":"\\int","⨑":"\\int","⨐":"\\int","⨏":"\\bcancel{\\int}","⨎":"","⨍":"\\hcancel{\\int}","⨌":"\\iiiint","∳":"\\oint","∲":"\\oint","∱":"\\int","∰":"\\oiint","∯":"\\oiint","∮":"\\oint","∫":"\\int","⨁":"\\oplus","⊘":"\\oslash","⊖":"\\ominus","⊕":"\\oplus","∭":"\\iiint","∬":"\\iint","⨋":"","⨊":"","∑":"\\sum","⊟":"\\boxminus","⊞":"\\boxplus","∔":"\\dot{+}","∓":"+-","−":"-","±":"\\pm","-":"-","+":"+","⭆":"\\Rrightarrow","⭅":"\\Lleftarrow","⧴":":\\rightarrow","⧯":"","⧟":"\\bullet-\\bullet","⦟":"\\angle","⦞":"\\measuredangle","⦝":"\\measuredangle","⦜":"\\perp","⦛":"\\measuredangle","⦚":"","⦙":"\\vdots","⥿":"","⥾":"","⥽":"\\prec","⥼":"\\succ","⥻":"\\underset{\\rightarrow}{\\supset}","⥺":"","⥹":"\\underset{\\rightarrow}{\\subset}","⥸":"\\underset{\\rightarrow}{>}","⥷":"","⥶":"\\underset{\\leftarrow}{<}","⥵":"\\underset{\\approx}{\\rightarrow}","⥴":"\\underset{\\sim}{\\rightarrow}","⥳":"\\underset{\\sim}{\\leftarrow}","⥲":"\\overset{\\sim}{\\rightarrow}","⥱":"\\overset{=}{\\rightarrow}","⥰":"","⥯":"","⥮":"","⥭":"\\overline{\\rightharpoondown}","⥬":"\\underline{\\rightharpoonup}","⥫":"\\overline{\\leftharpoondown}","⥪":"\\underline{\\leftharpoonup}","⥩":"\\rightleftharpoons","⥨":"\\rightleftharpoons","⥧":"\\rightleftharpoons","⥦":"\\rightleftharpoons","⥥":"\\Downarrow","⥤":"\\Rightarrow","⥣":"\\Uparrow","⥢":"\\Leftarrow","⥡":"\\downarrow","⥠":"\\uparrow","⥟":"\\rightarrow","⥞":"\\leftarrow","⥝":"\\downarrow","⥜":"\\uparrow","⥛":"\\rightarrow","⥚":"\\leftarrow","⥙":"\\downarrow","⥘":"\\uparrow","⥗":"\\rightarrow","⥖":"\\leftarrow","⥕":"\\downarrow","⥔":"\\uparrow","⥓":"\\rightarrow","⥒":"\\leftarrow","⥑":"\\updownarrow","⥐":"\\leftrightarrow","⥏":"\\updownarrow","⥎":"\\leftrightarrow","⥍":"\\updownarrow","⥌":"\\updownarrow","⥋":"\\leftrightarrow","⥊":"\\leftrightarrow","⥉":"","⥈":"\\leftrightarrow","⥇":"\\nrightarrow","⥆":"","⥅":"","⥄":"\\rightleftarrows","⥃":"\\leftrightarrows","⥂":"\\rightleftarrows","⥁":"\\circlearrowright","⥀":"\\circlearrowleft","⤿":"\\rightarrow","⤾":"\\leftarrow","⤽":"\\leftarrow","⤼":"\\rightarrow","⤻":"\\rightarrow","⤺":"\\leftarrow","⤹":"\\downarrow","⤸":"\\downarrow","⤷":"\\Rsh","⤶":"\\Lsh","⤵":"\\downarrow","⤴":"\\uparrow","⤳":"\\rightarrow","⤲":"\\leftarrow","⤱":" ","⤰":" ","⤯":" ","⤮":" ","⤭":" ","⤬":"\\times","⤫":"\\times","⤪":" ","⤩":" ","⤨":" ","⤧":" ","⤦":" ","⤥":" ","⤤":" ","⤣":" ","⤢":" ","⤡":" ","⤠":"\\mapsto\\cdot","⤟":"\\cdot\\leftarrow","⤞":"\\rightarrow\\cdot","⤝":"\\leftarrow","⤜":"\\rightarrow","⤛":"\\leftarrow","⤚":"\\rightarrow","⤙":"\\leftarrow","⤘":"\\rightarrow","⤗":"\\rightarrow","⤖":"\\rightarrow","⤕":"\\rightarrow","⤔":"\\rightarrow","⤓":"\\downarrow","⤒":"\\uparrow","⤑":"\\rightarrow","⤐":"\\rightarrow","⤏":"\\rightarrow","⤎":"\\leftarrow","⤍":"\\rightarrow","⤌":"\\leftarrow","⤋":"\\Downarrow","⤊":"\\Uparrow","⤉":"\\uparrow","⤈":"\\downarrow","⤇":"\\Rightarrow","⤆":"\\Leftarrow","⤅":"\\mapsto","⤄":"\\nLeftrightarrow","⤃":"\\nRightarrow","⤂":"\\nLeftarrow","⤁":"\\rightsquigarrow","⤀":"\\rightsquigarrow","⟿":"\\rightsquigarrow","⟾":"\\Rightarrow","⟽":"\\Leftarrow","⟼":"\\mapsto","⟻":"\\leftarrow","⟺":"\\Longleftrightarrow","⟹":"\\Longrightarrow","⟸":"\\Longleftarrow","⟷":"\\leftrightarrow","⟶":"\\rightarrow","⟵":"\\leftarrow","⟱":"\\Downarrow","⟰":"\\Uparrow","⊸":"\\rightarrow","⇿":"\\leftrightarrow","⇾":"\\rightarrow","⇽":"\\leftarrow","⇼":"\\nleftrightarrow","⇻":"\\nrightarrow","⇺":"\\nleftarrow","⇹":"\\nleftrightarrow","⇸":"\\nrightarrow","⇷":"\\nleftarrow","⇶":"\\Rrightarrow","⇵":"","⇴":"\\rightarrow","⇳":"\\Updownarrow","⇲":"\\searrow","⇱":"\\nwarrow","⇰":"\\Leftarrow","⇯":"\\Uparrow","⇮":"\\Uparrow","⇭":"\\Uparrow","⇬":"\\Uparrow","⇫":"\\Uparrow","⇪":"\\Uparrow","⇩":"\\Downarrow","⇨":"\\Rightarrow","⇧":"\\Uparrow","⇦":"\\Leftarrow","⇥":"\\rightarrow","⇤":"\\leftarrow","⇣":"\\downarrow","⇢":"\\rightarrow","⇡":"\\uparrow","⇠":"\\leftarrow","⇟":"\\downarrow","⇞":"\\uparrow","⇝":"\\rightsquigarrow","⇜":"\\leftarrow","⇛":"\\Rrightarrow","⇚":"\\Lleftarrow","⇙":"\\swarrow","⇘":"\\searrow","⇗":"\\nearrow","⇖":"\\nwarrow","⇕":"\\Updownarrow","⇔":"\\Leftrightarrow","⇓":"\\Downarrow","⇒":"\\Rightarrow","⇑":"\\Uparrow","⇐":"\\Leftarrow","⇏":"\\nRightarrow","⇎":"\\nLeftrightarrow","⇍":"\\nLeftarrow","⇌":"\\rightleftharpoons","⇋":"\\leftrightharpoons","⇊":"\\downdownarrows","⇉":"\\rightrightarrows","⇈":"\\upuparrows","⇇":"\\leftleftarrows","⇆":"\\leftrightarrows","⇅":"","⇄":"\\rightleftarrows","⇃":"\\downharpoonleft","⇂":"\\downharpoonright","⇁":"\\rightharpoondown","⇀":"\\rightharpoonup","↿":"\\upharpoonleft","↾":"\\upharpoonright","↽":"\\leftharpoondown","↼":"\\leftharpoonup","↻":"\\circlearrowright","↺":"\\circlearrowleft","↹":"\\leftrightarrows","↸":"\\overline{\\nwarrow}","↷":"\\curvearrowright","↶":"\\curvearrowleft","↵":"\\swarrow","↴":"\\searrow","↳":"\\Rsh","↲":"\\Lsh","↱":"\\Rsh","↰":"\\Lsh","↯":"\\swarrow","↮":"","↭":"\\leftrightsquigarrow","↬":"\\looparrowright","↫":"\\looparrowleft","↪":"\\hookrightarrow","↩":"\\hookleftarrow","↨":"\\underline{\\updownarrow}","↧":"\\downarrow","↦":"\\rightarrowtail","↥":"\\uparrow","↤":"\\leftarrowtail","↣":"\\rightarrowtail","↢":"\\leftarrowtail","↡":"\\downarrow","↠":"\\twoheadrightarrow","↟":"\\uparrow","↞":"\\twoheadleftarrow","↝":"\\nearrow","↜":"\\nwarrow","↚":"","↙":"\\swarrow","↘":"\\searrow","↗":"\\nearrow","↖":"\\nwarrow","↕":"\\updownarrow","↔":"\\leftrightarrow","↓":"\\downarrow","→":"\\rightarrow","↑":"\\uparrow","←":"\\leftarrow","|||":"\\left|||\\right.","||":"\\left||\\right.","|":"\\mid","⫾":"","⫽":"//","⫻":"///","⫺":"","⫹":"","⫸":"","⫷":"","⫶":"\\vdots","⫵":"","⫴":"","⫳":"","⫲":"\\nparallel","⫱":"","⫰":"","⫯":"","⫮":"\\bcancel{\\mid}","⫭":"","⫬":"","⫫":"","⫪":"","⫩":"","⫨":"\\underline{\\perp}","⫧":"\\overline{\\top}","⫦":"","⫥":"","⫤":"","⫣":"","⫢":"","⫡":"","⫠":"\\perp","⫟":"\\top","⫞":"\\dashv","⫝̸":"","⫝":"","⫛":"\\pitchfork","⫚":"","⫙":"","⫘":"","⫗":"","⫖":"","⫕":"","⫔":"","⫓":"","⫒":"","⫑":"","⫐":"","⫏":"","⫎":"","⫍":"","⫌":"\\underset{\\neq}{\\supset}","⫋":"\\underset{\\neq}{\\subset}","⫊":"\\underset{\\approx}{\\supset}","⫉":"\\underset{\\approx}{\\subset}","⫈":"\\underset{\\sim}{\\supset}","⫇":"\\underset{\\sim}{\\subset}","⫆":"\\supseteqq","⫅":"\\subseteqq","⫄":"\\dot{\\supseteq}","⫃":"\\dot{\\subseteq}","⫂":"\\underset{\\times}{\\supset}","⫁":"\\underset{\\times}{\\subset}","⫀":"\\underset{+}{\\supset}","⪿":"\\underset{+}{\\subset}","⪾":"","⪽":"","⪼":"\\gg ","⪻":"\\ll","⪺":"\\underset{\\cancel{\\approx}}{\\succ}","⪹":"\\underset{\\cancel{\\approx}}{\\prec}","⪸":"\\underset{\\approx}{\\succ}","⪷":"\\underset{\\approx}{\\prec}","⪶":"\\underset{\\cancel{=}}{\\succ}","⪵":"\\underset{\\cancel{=}}{\\prec}","⪴":"\\underset{=}{\\succ}","⪳":"\\underset{=}{\\prec}","⪲":"","⪱":"","⪮":"","⪭":"\\underline{\\hcancel{>}}","⪬":"\\underline{\\hcancel{>}}","⪫":"\\hcancel{>}","⪪":"\\hcancel{<}","⪩":"","⪨":"","⪧":"\\vartriangleright","⪦":"\\vartriangleleft","⪥":"><","⪤":"><","⪣":"\\underline{\\ll}","⪢̸":"\\cancel{\\gg}","⪢":"\\gg","⪡̸":"\\cancel{\\ll}","⪡":"\\ll","⪠":"\\overset{\\sim}{\\geqq}","⪟":"\\overset{\\sim}{\\leqq}","⪞":"\\overset{\\sim}{>}","⪝":"\\overset{\\sim}{<}","⪜":"","⪛":"","⪚":"\\overset{=}{>}","⪙":"\\overset{=}{<}","⪘":"","⪗":"","⪖":"","⪕":"","⪔":"","⪓":"","⪒":"\\underset{=}{\\gtrless}","⪑":"\\underset{=}{\\lessgtr}","⪐":"\\underset{<}{\\gtrsim}","⪏":"\\underset{>}{\\lesssim}","⪎":"\\underset{\\simeq}{>}","⪍":"\\underset{\\simeq}{<}","⪌":"\\gtreqqless","⪋":"\\lesseqqgtr","⪊":"\\underset{\\cancel{\\approx}}{>}","⪉":"\\underset{\\approx}{<}","⪆":"\\underset{\\approx}{>}","⪅":"\\underset{\\approx}{<}","⪄":"","⪃":"","⪂":"","⪁":"","⪀":"","⩿":"","⩾̸":"\\bcancel{\\geq}","⩾":"\\geq","⩽̸":"\\bcancel{\\leq}","⩽":"\\leq","⩼":"","⩻":"","⩺":"","⩹":"","⩸":"\\overset{\\dots}{\\equiv}","⩷":"","⩶":"===","⩵":"==","⩴":"::=","⩳":"","⩲":"\\underset{=}{+}","⩱":"\\overset{=}{+}","⩰":"\\overset{\\approx}{=}","⩯":"\\overset{\\wedge}{=}","⩮":"\\overset{*}{=}","⩭":"\\dot{\\approx}","⩬":"","⩫":"","⩪":"\\dot{\\sim}","⩩":"","⩨":"","⩧":"\\dot{\\equiv}","⩦":"\\underset{\\cdot}{=}","⩥":"","⩤":"","⩣":"\\underset{=}{\\vee}","⩢":"\\overset{=}{\\vee}","⩡":"ul(vv)","⩠":"\\underset{=}{\\wedge}","⩟":"\\underline{\\wedge}","⩞":"\\overset{=}{\\wedge}","⩝":"\\hcancel{\\vee}","⩜":"\\hcancel{\\wedge}","⩛":"","⩚":"","⩙":"","⩘":"\\vee","⩗":"\\wedge","⩖":"","⩕":"","⩔":"","⩓":"","⩒":"\\dot{\\vee}","⩑":"\\dot{\\wedge}","⩐":"","⩏":"","⩎":"","⩍":"\\overline{\\cap}","⩌":"\\overline{\\cup}","⩋":"","⩊":"","⩉":"","⩈":"","⩇":"","⩆":"","⩅":"","⩄":"","⩃":"\\overline{\\cap}","⩂":"\\overline{\\cup}","⩁":"","⩀":"","⨾":"","⨽":"\\llcorner","⨼":"\\lrcorner","⨻":"","⨺":"","⨹":"","⨸":"","⨷":"","⨶":"\\hat{\\otimes}","⨵":"","⨴":"","⨳":"","⨲":"\\underline{\\times}","⨱":"\\underline{\\times}","⨰":"\\dot{\\times}","⨮":"\\bigodot","⨭":"\\bigodot","⨬":"","⨫":"","⨪":"","⨩":"","⨨":"","⨧":"","◻":"\\Box","⨦":"\\underset{\\sim}{+}","⨥":"\\underset{\\circ}{+}","⨤":"\\overset{\\sim}{+}","⨣":"\\hat{+}","⨢":"\\dot{+}","⨡":"\\upharpoonright","⨠":">>","⨟":"","⨞":"\\triangleleft","⨝":"\\bowtie","⧿":"","⧾":"+","⧻":"\\hcancel{|||}","⧺":"\\hcancel{||}","⧹":"\\backslash","⧸":"/","⧷":"hcancel{\backslash}","⧶":"","⧵":"\\backslash","⧲":"\\Phi","⧱":"","⧰":"","⧮":"","⧭":"","⧬":"","⧫":"\\lozenge","⧪":"","⧩":"","⧨":"","⧧":"\\ddagger","⧢":"\\sqcup\\sqcup","⧡":"","⧠":"\\square","⧞":"","⧝":"","⧜":"","⧛":"\\{\\{","⧙":"\\{","⧘":"\\}","⧗":"","⧖":"","⧕":"\\bowtie","⧔":"\\bowtie","⧓":"\\bowtie","⧒":"\\bowtie","⧑":"\\bowtie","⧐̸":"| \\not\\triangleright","⧐":"| \\triangleright","⧏̸":"\\not\\triangleleft |","⧏":"\\triangleleft |","⧎":"","⧍":"\\triangle","⧌":"","⧋":"\\underline{\\triangle}","⧊":"\\dot{\\triangle}","⧉":"","⧈":"\\boxed{\\circ}","⧇":"\\boxed{\\circ}","⧆":"\\boxed{\\rightarrow}","⧅":"\\bcancel{\\square}","⧄":"\\cancel{\\square}","⧃":"\\odot","⧂":"\\odot","⦿":"\\odot","⦾":"\\odot","⦽":"\\varnothing","⦼":"\\oplus","⦻":"\\otimes","⦺":"","⦹":"\\varnothing","⦸":"\\varnothing","⦷":"\\ominus","⦶":"\\ominus","⦵":"\\ominus","⦴":"\\vec{\\varnothing}","⦳":"\\vec{\\varnothing}","⦲":"\\dot{\\varnothing}","⦱":"\\overline{\\varnothing}","⦰":"\\varnothing","⦯":"\\measuredangle","⦮":"\\measuredangle","⦭":"\\measuredangle","⦬":"\\measuredangle","⦫":"\\measuredangle","⦪":"\\measuredangle","⦩":"\\measuredangle","⦨":"\\measuredangle","⦧":"","⦦":"","⦥":"","⦤":"","⦣":"\\ulcorner","⦢":"\\measuredangle","⦡":"\\not\\lor","⦠":"\\bcancel{>}","⦂":":","⦁":"\\cdot","❘":"\\mid","▲":"\\bigtriangleup","⋿":"\\Epsilon","⋾":"\\overline{\\ni}","⋽":"\\overline{\\ni}","⋼":"\\in","⋻":"\\in","⋺":"\\in","⋹":"\\underline{\\in}","⋸":"\\underline{\\in}","⋷":"\\overline{\\in}","⋶":"\\overline{\\in}","⋵":"\\dot{\\in}","⋴":"\\in","⋳":"\\in","⋲":"\\in","⋰":"\\ddots","։":":","⋩":"\\underset{\\sim}{\\succ}","⋨":"\\underset{\\sim}{\\prec}","⋧":"\\underset{\\not\\sim}{>}","⋦":"\\underset{\\not\\sim}{<}","⋥":"\\not\\sqsupseteq","⋤":"\\not\\sqsubseteq","⋣":"\\not\\sqsupseteq","⋢":"\\not\\sqsubseteq","⋡":"\\nsucc","⋠":"\\nprec","⋟":"\\succ","⋞":"\\prec","⋝":"\\overline{>}","⋜":"\\overline{<}","⋛":"\\underset{>}{\\leq}","⋚":"\\underset{<}{\\geq}","⋕":"\\#","⋓":"\\cup","⋒":"\\cap","⋑":"\\supset","⋐":"\\subset","⋏":"\\wedge","⋎":"\\vee","⋍":"\\simeq","⋈":"\\Join","⋇":"\\ast","⋆":"\\star","⋄":"\\diamond","⊿":"\\triangle","⊾":"\\measuredangle","⊽":"\\overline{\\lor}","⊼":"\\overline{\\land}","⊻":"\\underline{\\lor}","⊺":"\\top","土":"\\pm","十":"+","⊹":"","⊷":"\\circ\\multimap","⊶":"\\circ\\multimap","⊳":"\\triangleright","⊲":"\\triangleleft","⊱":"\\succ","⊰":"\\prec","⊫":"|\\models","⊪":"|\\models","⊧":"\\models","⊦":"\\vdash","⊝":"\\ominus","⊜":"\\ominus","⊛":"\\odot","⊚":"\\odot","⊔":"\\sqcup","⊓":"\\sqcap","⊒":"\\sqsupseteq","⊑":"\\sqsubseteq","⊐̸":"\\not\\sqsupset","⊐":"\\sqsupset","⊏̸":"\\not\\sqsubset","⊏":"\\sqsubset","⊎":"\\cup","⊍":"\\cup","⊌":"\\cup","≿̸":"\\not\\succsim","≿":"\\succsim","≾":"\\precsim","≹":"\\not\\overset{>}{<}","≸":"\\not\\overset{>}{<}","≷":"\\overset{>}{<}","≶":"\\overset{<}{>}","≵":"\\not\\geg","≴":"\\not\\leq","≳":"\\geg","≲":"\\leq","≬":"","≧":"\\geg","≦̸":"\\not\\leq","≦":"\\leq","≣":"\\overset{=}{=} ","≞":"\\overset{m}{=} ","≝":"\\overset{def}{=}","≘":"=","≖":"=","≕":"=:","≓":"\\doteq","≒":"\\doteq","≑":"\\doteq","≐":"\\doteq","≏̸":"","≏":"","≎̸":"","≎":"","≌":"\\approx","≋":"\\approx","≊":"\\approx","≂̸":"\\neq","≂":"=","∿":"\\sim","∾":"\\infty","∽̱":"\\sim","∽":"\\sim","∻":"\\sim","∺":":-:","∹":"-:","∸":"\\bot","∷":"::","∶":":","∣":"\\mid","∟":"\\llcorner","∘":"\\circ","∗":"*","∕":"/","∎":"\\square","∍":"\\ni","∊":"\\in","∆":"\\Delta","⁄":"/","⪰̸":"\\nsucceq","⪰":"\\succeq","⪯̸":"\\npreceq","⪯":"\\preceq","⪈":"\\ngeqslant","⪇":"\\nleqslant","⧳":"\\Phi","⧦":"\\models","⧥":"\\not\\equiv","⧤":"\\approx\\neq","⧣":"\\neq","⧁":"\\circle","⧀":"\\circle","◦":"\\circle","◗":"\\circle","◖":"\\circle","●":"\\circle","◎":"\\circledcirc","◍":"\\circledcirc","◌":"\\circledcirc","◉":"\\circledcirc","◈":"\\diamond","◇":"\\diamond","◆":"\\diamond","◅":"\\triangleleft","◄":"\\triangleleft","◃":"\\triangleleft","◂":"\\triangleleft","◁":"\\triangleleft","◀":"\\triangleleft","▿":"\\triangledown","▾":"\\triangledown","▽":"\\triangledown","▼":"\\triangledown","▹":"\\triangleright","▸":"\\triangleright","▷":"\\triangleright","▶":"\\triangleright","▵":"\\triangle","▴":"\\triangle","△":"\\triangle","▱":"\\square","▰":"\\blacksquare","▯":"\\square","▮":"\\blacksquare","▭":"\\square","▫":"\\square","▪":"\\square","□":"\\square","■":"\\blacksquare","⋭":"\\not\\triangleright","⋬":"\\not\\triangleleft","⋫":"\\not\\triangleright","⋪":"\\not\\triangleleft","⋙":"\\ggg","⋘":"\\lll","⋗":"*>","⋖":"<*","⋔":"\\pitchfork","⋌":"","⋋":"\\bowtie","⋊":"\\ltimes","⋉":"\\rtimes","⊵":"\\triangleright","\\triangleleft":"","⊥":"\\bot","⊁":"\\nsucc","⊀":"\\preceq","≽":"\\succeq","≼":"\\preceq","≻":"\\succ","≺":"\\prec","≱":"\\geq/","≰":"\\leq/","≭":"\\neq","≫̸":"\\not\\gg","≫":"\\gg","≪̸":"\\not\\ll","≪":"\\ll","≩":"\\ngeqslant","≨":"\\nleqslant","≡":"\\equiv","≟":"\\doteq","≜":"\\triangleq","≛":"\\doteq","≚":"\\triangleq","≙":"\\triangleq","≗":"\\doteq","≔":":=","≍":"\\asymp","≇":"\\ncong","≆":"\\ncong","≅":"\\cong","≄":"\\not\\simeq","≃":"\\simeq","≁":"\\not\\sim","∦":"\\not\\parallel","∥":"\\parallel","∤":"\\not|","∝":"\\propto","==":"==","=":"=",":=":":=","/=":"=","-=":"-=","+=":"+=","*=":"*=","!=":"!=","≠":"\\neq","≢":"\\equiv /","≉":"\\approx /","∼":"sim","≈":"\\approx","≮":"</","<":"<","≯":">/",">=":">=",">":">","≥":"\\geq","≤":"\\leq","<=":"<=","⊋":"\\supsetneq","⊊":"\\subsetneq","⊉":"\\nsupseteq","⊈":"\\nsubseteq","⊇":"\\supseteq","⊆":"\\subseteq","⊅":"\\not\\supset","⊄":"\\not\\subset","⊃⃒":"\\supset |","⊃":"\\supset","⊂⃒":"\\subset |","⊂":"\\subset","∌":"\\not\\in","∉":"\\notin","∈":"\\in","∁":"C","∄":"\\nexists","∃":"\\exists","∀":"\\forall","∧":"\\land","&&":"\\&\\&","∨":"\\lor","⊯":"\\cancel{\\vDash}","⊮":"\\cancel{\\Vdash}","⊭":"\\nvDash","⊬":"\\nvDash","⊩":"\\Vdash","⊨":"\\vDash","⊤":"\\top","⊣":"\\dashv","⊢":"\\vdash","∋":"\\ni","⋱":"\\ddots","⋯":"\\hdots","⋮":"\\vdots","϶":"\\ni",":":":","...":"\\cdots","..":"..","->":"->","∵":"\\because","∴":"\\therefore ","⁣":"\\llbracket",",":",",";":";","⧽":"\\}","⧼":"\\{","⦘":"\\]","⦗":"\\[","⦖":"\\ll","⦕":"\\gg","⦔":"\\gg","⦓":"\\ll","⦒":"\\gg","⦑":"\\ll","⦐":"\\]","⦏":"\\]","⦎":"\\]","⦍":"\\[","⦌":"\\[","⦋":"\\]","⦊":"\\triangleright","⦉":"\\triangleleft","⦈":"|\\)","⦇":"\\(|","⦆":"|\\)","⦅":"\\(\\(","⦄":"|\\}","⦃":"\\{|","⦀":"\\||","⟯":"\\left. \\right]","⟮":"\\left[ \\right.","⟭":"\\left. \\right]]","⟬":"\\left[[ \\right.","⟫":"\\gg","⟪":"\\ll","⟧":"\\)|","⟦":"\\(|","❳":"\\left.\\right)","❲":"\\left(\\right.","〉":"\\rangle","〈":"\\langle","⌋":"\\rfloor","⌊":"\\lfloor","⌉":"\\rceil","⌈":"\\lceil","‖":"\\parallel","}":"\\left.\\right\\}","{":"\\left\\{\\right.","]":"\\left]\\right.","[":"\\left[\\right.",")":"\\left.\\right)","(":"\\left(\\right.","”":'\\"',"“":"\\text{``}","’":"'","‘":"`","α":"\\alpha","β":"\\beta","γ":"\\gamma","Γ":"\\Gamma","δ":"\\delta","Δ":"\\Delta","ϵ":"\\epsilon","ζ":"\\zeta","η":"\\eta","θ":"\\theta","Θ":"\\Theta","ι":"\\iota","κ":"\\kappa","λ":"\\lambda","ν":"\\nu","ο":"\\omicron","π":"\\pi","Π":"\\Pi","ρ":"\\rho","σ":"\\sigma","Σ":"\\Sigma","τ":"\\tau","υ":"\\upsilon","Υ":"\\Upsilon","ϕ":"\\phi","Φ":"\\Phi","χ":"\\chi","ψ":"\\psi","Ψ":"\\Psi","ω":"\\omega","Ω":"\\Omega","Ω":"\\Omega","∅":"\\emptyset","⟲":"\\circlearrowleft","⟳":"\\circlearrowright","×":"\\times","½":"\\dfrac{1}{2}","μ":"\\mu","Ө":"\\theta","✓":"\\checkmark","⟩":"\\rangle","⟨":"\\langle","¼":"\\dfrac{1}{4}","…":"\\ldots","ℏ":"\\hbar","ℜ":"\\mathfrak{R}","Ѳ":"\\theta","Ø":"\\emptyset","ϱ":"\\varrho","ф":"\\phi","ℇ":"\\varepsilon",T:"T","∙":"\\cdot","Ρ":"P","∞":"\\infty","ᐁ":"\\nabla","ƞ":"\\eta","⁺":"^{+}","⁻":"^{-}","⁼":"^{=}","⁽":"^{(}","⁾":"^{)}","〗":"\\)|","〖":"\\langle",";":";","൦":"\\circ","┴":"\\perp","✕":"\\times","⎻":"-","»":"\\gg","⬆":"\\uparrow","⬇":"\\downarrow","⬅":"\\leftarrow","➡":"\\rightarrow","⎼":"-","⎜":"\\mid","⎥":"\\mid","ħ":"\\hbar","⮕":"\\rightarrow","・":"\\cdot","¦":"\\mid","£":"\\pounds","¥":"\\yen","✗":"\\times","✔":"\\checkmark","ⁿ":"^{n}","«":"\\ll","เ":"\\prime","†":"\\dagger","│":"\\mid",$:"\\$","#":"\\#","℃":"\\text{\\textdegree C}","℉":"\\text{\\textdegree F}","█":"\\blacksquare","℧":"\\mho","ⅇ":"\\text{e}","ɼ":"r","‡":"\\ddagger","ἱ":"i","ϒ":"\\Upsilon","𝛿":"\\delta","˳":"\\cdot","ѳ":"\\theta","𝜙":"\\phi","П":"\\prod","о":"o","ђ":"\\hbar","Ʌ":"\\Lambda","।":"\\mid","€":"\\euro","ῡ":"\\bar{u}","φ":"\\varphi","ȼ":"c","𝞮":"\\epsilon","Χ":"\\mathsf{X}","ₙ":"_{n}"}},2335:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.allMathSymbolsByChar=void 0,t.allMathSymbolsByChar={"&#xA0;":"\\textrm{ }","&#x2203;":"\\exists","&#x2200;":"\\forall","&#x21D4;":"\\iff","&#x21D2;":"=>","&#xAC;":"\\neg","&#x2124;":"\\mathbb{Z}","&#x211D;":"\\mathbb{R}","&#x211A;":"\\mathbb{Q}","&#x2115;":"\\mathbb{N}","&#x2102;":"CC","&#x25A1;":"\\square","&#x22C4;":"\\diamond","&#x25B3;":"\\triangle","&#x2322;":"\\frown","&#x2220;":"\\angle","&#x22F1;":"\\ddots","&#x22EE;":"\\vdots","&#x2235;":"\\because","&#x2234;":"\\therefore","&#x2135;":"\\aleph","&#x2205;":"\\oslash","&#xB1;":"\\pm","&#x2207;":"\\nabla","&#x2202;":"\\partial","&#x222E;":"\\oint","&#x222B;":"\\int","&#x22C3;":"\\cup","&#x222A;":"\\cup","&#x22C2;":"\\cap","&#x2229;":"\\cap","&#x22C1;":"\\vee","&#x2228;":"\\vee","&#x22C0;":"\\wedge","&#x2227;":"\\wedge","&#x220F;":"\\prod","&#x2211;":"\\sum","&#x2299;":"\\bigodot","&#x2297;":"\\bigoplus","&#x2295;":"o+","&#x2218;":"@","&#x22C8;":"\\bowtie","&#x22CA;":"\\rtimes","&#x22C9;":"\\ltimes","&#xF7;":"\\div","&#xD7;":"\\times","\\":"\\backslash","&#x22C6;":"\\star","&#x2217;":"\\star","&#x22C5;":"\\cdot","&#x3A9;":"\\Omega","&#x3C9;":"\\omega","&#x3A8;":"\\Psi","&#x3C8;":"\\psi","&#x3C7;":"\\chi","&#x3C6;":"\\varphi","&#x3A6;":"\\Phi","&#x3D5;":"\\phi","&#x3C5;":"\\upsilon","&#x3C4;":"\\tau","&#x3A3;":"\\Sigma","&#x3C3;":"\\sigma","&#x3C1;":"\\rho","&#x3A0;":"\\Pi","&#x3C0;":"\\pi","&#x39E;":"\\Xi","&#x3BE;":"\\xi","&#x3BD;":"\\nu","&#x3BC;":"\\mu","&#x39B;":"\\Lambda","&#x3BB;":"\\lambda","&#x3BA;":"\\kappa","&#x3B9;":"\\iota","&#x3D1;":"\\vartheta","&#x398;":"\\Theta","&#x3B8;":"\\theta","&#x3B7;":"\\eta","&#x3B6;":"\\zeta","&#x25B;":"\\varepsilon","&#x3B5;":"\\epsilon","&#x394;":"\\Delta","&#x3B4;":"\\delta","&#x393;":"\\Gamma","&#x3B3;":"\\gamma","&#x3B2;":"\\beta","&#x3B1;":"\\alpha","&#x221E;":"\\infty","‬":"\\text{\\textdir TRT}","‎":"\\text{\\textdir LTR}"}},4104:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.allMathSymbolsByGlyph=void 0,t.allMathSymbolsByGlyph={" ":"\\textrm{ }","∃":"\\exists","∀":"\\forall","⇔":"\\iff","⇒":"\\Rightarrow","¬":"\\neg","□":"\\square","⋄":"\\diamond","△":"\\triangle","⌢":"\\frown","∠":"\\angle","⋱":"\\ddots","⋮":"\\vdots","∵":"\\because","∴":"\\therefore","ℵ":"\\aleph","∅":"\\emptyset","±":"\\pm","∇":"\\nabla","∂":"\\partial","∮":"\\oint","∫":"\\int","⋃":"\\cup","∪":"\\cup","⋂":"\\cap","∩":"\\cap","⋁":"\\vee","∨":"\\vee","⋀":"\\wedge","∧":"\\wedge","∏":"\\prod","∑":"\\sum","⊙":"\\bigodot","⊗":"\\bigoplus","⊕":"o+","∘":"@","⋈":"\\bowtie","⋊":"\\rtimes","⋉":"\\ltimes","÷":"\\div","×":"\\times","\\":"\\backslash","⋆":"\\star","∗":"\\star","⋅":"\\cdot","Ω":"\\Omega","ω":"\\omega","Ψ":"\\Psi","ψ":"\\psi","χ":"\\chi","φ":"\\varphi","Φ":"\\Phi","ϕ":"\\phi","υ":"\\upsilon","τ":"\\tau","Σ":"\\Sigma","σ":"\\sigma","ρ":"\\rho","Π":"\\Pi","π":"\\pi","Ξ":"\\Xi","ξ":"\\xi","ν":"\\nu","μ":"\\mu","Λ":"\\Lambda","λ":"\\lambda","κ":"\\kappa","ι":"\\iota","ϑ":"\\vartheta","Θ":"\\Theta","θ":"\\theta","η":"\\eta","ζ":"\\zeta","ɛ":"\\varepsilon","ε":"\\epsilon","Δ":"\\Delta","δ":"\\delta","Γ":"\\Gamma","γ":"\\gamma","β":"\\beta","α":"\\alpha","∞":"\\infty","ϵ":"\\epsilon","µ":"\\mu","²":"^{2}","ı":"\\imath","∎":"\\blacksquare","ม":"\\mathbf{m}","Ω":"\\Omega","⟲":"\\circlearrowleft","⟳":"\\circlearrowright","त":" ","¥":"\\yen","⁽":"^{(}","⁾":"^{)}","ß":"\\ss","Ћ":"\\hbar","⦵":"\\ominus","⊿":"\\bigtriangleup","↛'":"\\nrightarrow","†":"\\dagger","เ":"\\prime","白":" ","⿱":" ","ℸ":"\\wp","퓰":" ","ⁿ":"^{n}","✔":"\\checkmark","✗":"\\times","½":"\\dfrac{1}{2}","Ө":"\\theta","✓":"\\checkmark","⟩":"\\rangle","⟨":"\\langle","〈":"\\langle","¼":"\\dfrac{1}{4}","…":"\\ldots","ℏ":"\\hbar","ℜ":"\\mathfrak{R}","Ѳ":"\\theta","Ø":"\\emptyset","ϱ":"\\varrho","ф":"\\phi",T:"T","∙":"\\cdot","Ρ":"P","ᐁ":"\\nabla","ƞ":"\\eta","ɣ":"\\gamma","ћ":"\\hbar","Ɛ":"\\varepsilon","ⅅ":"\\_{D}","𝜆":"\\lambda","〗":"\\rangle","〖":"\\langle",";":";","𝑥":"x","𝑦":"y","𝑧":"z","𝑖":"i","𝑗":"j","𝑘":"k","𝑚":"m","𝑒":"e","𝑟":"r","ɳ":"\\eta","𝛽":"\\beta","⍵":"\\omega","℘":"\\wp","𝜋":"\\pi","Є":"\\epsilon","є":"\\epsilon","𝜀":"\\epsilon","п":"\\pi","Ν":"\\nu","ɵ":"\\theta","𝜓":"\\psi","ϴ":"\\theta","ɸ":"\\phi","Ӷ":"\\Gamma","ɭ":"\\ell","ʋ":"\\upsilon","𝛟":"\\varphi","⍬":"\\theta","Ф":"\\Phi","𝜑":"\\varphi","ⅈ":"i","ο":"o","ơ":"o","ƒ":"f","⍴":"\\rho","🇽":"x","𝑝":"p","𝑞":"q","𝑠":"s","𝑡":"t","𝑢":"u","𝑣":"v","𝑤":"w","𝑎":"a","𝑏":"b","𝑐":"c","𝑑":"d","𝑓":"f","𝑔":"g","𝑙":"l","𝑛":"n","𝑜":"o","𝔀":"w","𝚟":"v","ṁ":"m","൦":"\\circ","┴":"\\perp","✕":"\\times","∣":"\\mid","Փ":"\\Phi","⎜":"\\mid","ħ":"\\hbar","ፈ":" ","⦨":"\\llbracket","ế":"\\hat{e}","¢":"\\cent","⤹":"\\downarrow","⤸":"\\downarrow","⤷":"\\Rsh","⤶":"\\Lsh","⤵":"\\downarrow","⤴":"\\uparrow","⤳":"\\rightarrow","|":"\\mid","⎥":"\\mid","♥":"\\heartsuit","О":"0","Υ":"Y","х":"x","𝓏":"z","𝓎":"y","𝓍":"x","р":"p","а":"a","£":"\\pounds",m:"m","𝚵":"\\Xi","⓪":"\\textcircled{0}","①":"\\textcircled{1}","②":"\\textcircled{2}","③":"\\textcircled{3}","④":"\\textcircled{4}","⑤":"\\textcircled{5}","⑥":"\\textcircled{6}","⑦":"\\textcircled{7}","⑧":"\\textcircled{8}","⑨":"\\textcircled{9}","⑩":"\\textcircled{10}","⑪":"\\textcircled{11}","⑫":"\\textcircled{12}","⑬":"\\textcircled{13}","⑭":"\\textcircled{14}","⑮":"\\textcircled{15}","⑯":"\\textcircled{16}","⑰":"\\textcircled{17}","⑱":"\\textcircled{18}","⑲":"\\textcircled{19}","⑳":"\\textcircled{20}","㉑":"\\textcircled{21}","㉒":"\\textcircled{22}","㉓":"\\textcircled{23}","㉔":"\\textcircled{24}","㉕":"\\textcircled{25}","㉖":"\\textcircled{26}","㉗":"\\textcircled{27}","㉘":"\\textcircled{28}","㉙":"\\textcircled{29}","㉚":"\\textcircled{30}","㉛":"\\textcircled{31}","㉜":"\\textcircled{32}","㉝":"\\textcircled{33}","㉞":"\\textcircled{34}","㉟":"\\textcircled{35}","㊱":"\\textcircled{36}","㊲":"\\textcircled{37}","㊳":"\\textcircled{38}","㊴":"\\textcircled{39}","㊵":"\\textcircled{40}","㊶":"\\textcircled{41}","㊷":"\\textcircled{42}","㊸":"\\textcircled{43}","㊹":"\\textcircled{44}","㊺":"\\textcircled{45}","㊻":"\\textcircled{46}","㊼":"\\textcircled{47}","㊽":"\\textcircled{48}","㊾":"\\textcircled{49}","㊿":"\\textcircled{50}","&":"\\&","‖":"\\parallel","%":"\\%","“":"\\text{``}",$:"\\$","#":"\\#","℃":"\\text{\\textdegree C}","℉":"\\text{\\textdegree F}","█":"\\blacksquare","℧":"\\mho","⌋":"\\rfloor","⌊":"\\lfloor","⌉":"\\rceil","⌈":"\\lceil","ℇ":"\\varepsilon","ⅇ":"\\text{e}","ɼ":"r","↛":"\\nrightarrow","ˆ":"\\hat{}","‾":"\\overline","→":"\\rightarrow","‡":"\\ddagger","・":"\\cdot","▱":"\\square","∆":"\\Delta","ἱ":"i","∡":"\\angle","ϒ":"\\Upsilon","↓":"\\downarrow","↑":"\\uparrow","»":"\\gg","⊤":"\\top","⧸":"/","𝛿":"\\delta","˳":"\\cdot","։":":","⦪":"\\measuredangle","⦩":"\\measuredangle","⦫":"\\measuredangle","⦁":"\\cdot","ѳ":"\\theta","⦢":"\\measuredangle","¸":",","⎻":"\\overline","⟦":"\\llbracket","𝜙":"\\phi","П":"\\prod","о":"o","≈":"\\approx","≤":"\\leq","ђ":"\\hbar","Ʌ":"\\Lambda","土":"\\pm","⎼":"-","十":"+","≠":"\\neq","←":"\\leftarrow","।":"\\mid","€":"\\euro","˘":" ","ῡ":"\\bar{u}","∥":"\\parallel","↔":"\\leftrightarrow","√":"\\sqrt{}","ȼ":"c","𝞮":"\\epsilon","·":"\\cdot","⦬":"\\measuredangle","⦮":"\\measuredangle","⦭":"\\measuredangle","«":"\\ll","Χ":"\\mathsf{X}","│":"\\mid","〉":"\\rangle","ₙ":"_{n}","▫":"\\square","●":"\\circle","”":'\\"'}},2408:function(e,t,r){"use strict";var a=this&&this.__createBinding||(Object.create?function(e,t,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,a,n)}:function(e,t,r,a){e[a=void 0===a?r:a]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(1868),t),n(r(598),t),n(r(2335),t),n(r(4104),t),n(r(6626),t),n(r(2103),t),n(r(4317),t)},6626:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.latexAccents=void 0,t.latexAccents=["\\hat","\\bar","\\underbrace","\\overbrace"]},2103:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mathNumberByGlyph=void 0,t.mathNumberByGlyph={"₀":"_{0}","₁":"_{1}","₂":"_{2}","₃":"_{3}","₄":"_{4}","₅":"_{5}","₆":"_{6}","₇":"_{7}","₈":"_{8}","₉":"_{9}","⁰":"^{0}","¹":"^{1}","²":"^{2}","³":"^{3}","⁴":"^{4}","⁵":"^{5}","⁶":"^{6}","⁷":"^{7}","⁸":"^{8}","⁹":"^{9}","ⁿ":"^{n}","ₙ":"_{n}","⓪":"\\textcircled{0}","①":"\\textcircled{1}","②":"\\textcircled{2}","③":"\\textcircled{3}","④":"\\textcircled{4}","⑤":"\\textcircled{5}","⑥":"\\textcircled{6}","⑦":"\\textcircled{7}","⑧":"\\textcircled{8}","⑨":"\\textcircled{9}","⑩":"\\textcircled{10}","⑪":"\\textcircled{11}","⑫":"\\textcircled{12}","⑬":"\\textcircled{13}","⑭":"\\textcircled{14}","⑮":"\\textcircled{15}","⑯":"\\textcircled{16}","⑰":"\\textcircled{17}","⑱":"\\textcircled{18}","⑲":"\\textcircled{19}","⑳":"\\textcircled{20}","㉑":"\\textcircled{21}","㉒":"\\textcircled{22}","㉓":"\\textcircled{23}","㉔":"\\textcircled{24}","㉕":"\\textcircled{25}","㉖":"\\textcircled{26}","㉗":"\\textcircled{27}","㉘":"\\textcircled{28}","㉙":"\\textcircled{29}","㉚":"\\textcircled{30}","㉛":"\\textcircled{31}","㉜":"\\textcircled{32}","㉝":"\\textcircled{33}","㉞":"\\textcircled{34}","㉟":"\\textcircled{35}","㊱":"\\textcircled{36}","㊲":"\\textcircled{37}","㊳":"\\textcircled{38}","㊴":"\\textcircled{39}","㊵":"\\textcircled{40}","㊶":"\\textcircled{41}","㊷":"\\textcircled{42}","㊸":"\\textcircled{43}","㊹":"\\textcircled{44}","㊺":"\\textcircled{45}","㊻":"\\textcircled{46}","㊼":"\\textcircled{47}","㊽":"\\textcircled{48}","㊾":"\\textcircled{49}","㊿":"\\textcircled{50}","½":"\\dfrac{1}{2}","⅓":"\\dfrac{1}{3}","⅔":"\\dfrac{2}{3}","¼":"\\dfrac{1}{4}","¾":"\\dfrac{3}{4}","⅕":"\\dfrac{1}{5}","⅖":"\\dfrac{2}{5}","⅗":"\\dfrac{3}{5}","⅘":"\\dfrac{4}{5}","⅙":"\\dfrac{1}{6}","⅚":"\\dfrac{5}{6}","⅐":"\\dfrac{1}{7}","⅛":"\\dfrac{1}{8}","⅜":"\\dfrac{3}{8}","⅝":"\\dfrac{5}{8}","⅞":"\\dfrac{7}{8}","⅑":"\\dfrac{1}{9}","⅒":"\\dfrac{1}{10}"}},4317:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HashUTF8ToLtXConverter=void 0,t.HashUTF8ToLtXConverter=class{convert(e){var t;return e.match(/[a-z0-9]/i)?e:(t=r[e])?this.convertAccentCharToLtX(t)||e:this.convertSpecialCharToLtX(e)||e}convertAccentCharToLtX(e){var{char:e,accent:t}=e,t=a[t];return t?`\\${t}{${e}}`:null}convertSpecialCharToLtX(e){var t,e=n[e];return e?({letter:e,fontCmd:t}=e,`\\${t}{${e}}`):null}};const r={"á":{char:"a",accent:"´"},"à":{char:"a",accent:"`"},"â":{char:"a",accent:"^"},"ã":{char:"a",accent:"~"},"ä":{char:"a",accent:"¨"},"å":{char:"a",accent:"˚"},"ą":{char:"a",accent:"˙"},"ă":{char:"a",accent:"˘"},"ǎ":{char:"a",accent:"ˇ"},"ǟ":{char:"a",accent:"ˆ"},"ǻ":{char:"a",accent:"˙"},"ǡ":{char:"a",accent:"-"},"ā":{char:"a",accent:"-"},"é":{char:"e",accent:"´"},"è":{char:"e",accent:"`"},"ê":{char:"e",accent:"^"},"ë":{char:"e",accent:"¨"},"ę":{char:"e",accent:"˙"},"ě":{char:"e",accent:"ˇ"},"ȇ":{char:"i",accent:"^"},"ё":{char:"e",accent:"¨"},"ē":{char:"e",accent:"-"},"í":{char:"i",accent:"´"},"ì":{char:"i",accent:"`"},"î":{char:"i",accent:"^"},"ï":{char:"i",accent:"¨"},"į":{char:"i",accent:"˙"},"ǐ":{char:"i",accent:"ˇ"},"ȉ":{char:"i",accent:"`"},"ȋ":{char:"i",accent:"¨"},"ī":{char:"i",accent:"-"},"ó":{char:"o",accent:"´"},"ò":{char:"o",accent:"`"},"ô":{char:"o",accent:"^"},"õ":{char:"o",accent:"~"},"ö":{char:"o",accent:"¨"},"ő":{char:"o",accent:"˝"},"ǒ":{char:"o",accent:"ˇ"},"ȍ":{char:"o",accent:"`"},"ȏ":{char:"o",accent:"¨"},"ȫ":{char:"o",accent:"˘"},"ȭ":{char:"o",accent:"˝"},"ȯ":{char:"o",accent:"˙"},"ō":{char:"o",accent:"-"},"ú":{char:"u",accent:"´"},"ù":{char:"u",accent:"`"},"û":{char:"u",accent:"^"},"ü":{char:"u",accent:"¨"},"ű":{char:"u",accent:"˝"},"ǔ":{char:"u",accent:"ˇ"},"ǖ":{char:"u",accent:"¨"},"ǘ":{char:"u",accent:"¨"},"ǚ":{char:"u",accent:"¨"},"ǜ":{char:"u",accent:"¨"},"ȕ":{char:"u",accent:"`"},"ȗ":{char:"u",accent:"¨"},"ū":{char:"u",accent:"-"},"ý":{char:"y",accent:"´"},"ỳ":{char:"y",accent:"`"},"ŷ":{char:"y",accent:"^"},"ÿ":{char:"y",accent:"¨"},"ȳ":{char:"y",accent:"-"},"Á":{char:"A",accent:"´"},"À":{char:"A",accent:"`"},"Â":{char:"A",accent:"^"},"Ã":{char:"A",accent:"~"},"Ä":{char:"A",accent:"¨"},"Å":{char:"A",accent:"˚"},"Å":{char:"A",accent:"˚"},"Ȧ":{char:"A",accent:"˙"},"Ă":{char:"A",accent:"˘"},"Ǎ":{char:"A",accent:"ˇ"},"Ǟ":{char:"A",accent:"˝"},"Ǻ":{char:"A",accent:"˚"},"Ǡ":{char:"A",accent:"-"},"Ā":{char:"A",accent:"-"},"É":{char:"E",accent:"´"},"È":{char:"E",accent:"`"},"Ė":{char:"E",accent:"˙"},"Ê":{char:"E",accent:"^"},"Ë":{char:"E",accent:"¨"},"Ě":{char:"E",accent:"ˇ"},"Ȅ":{char:"E",accent:"`"},"Ȇ":{char:"E",accent:"¨"},"Ē":{char:"E",accent:"-"},"Í":{char:"I",accent:"´"},"Ì":{char:"I",accent:"`"},"Î":{char:"I",accent:"^"},"Ï":{char:"I",accent:"¨"},"Ĭ":{char:"I",accent:"˘"},"Ǐ":{char:"I",accent:"ˇ"},"Ȉ":{char:"I",accent:"`"},"Ȋ":{char:"I",accent:"¨"},"Ī":{char:"I",accent:"-"},"Ó":{char:"O",accent:"´"},"Ò":{char:"O",accent:"`"},"Ô":{char:"O",accent:"^"},"Õ":{char:"O",accent:"~"},"Ö":{char:"O",accent:"¨"},"Ő":{char:"O",accent:"˝"},"Ǒ":{char:"O",accent:"ˇ"},"Ȍ":{char:"O",accent:"`"},"Ȏ":{char:"O",accent:"¨"},"Ȫ":{char:"O",accent:"˘"},"Ȭ":{char:"O",accent:"˝"},"Ȯ":{char:"O",accent:"˙"},"Ō":{char:"O",accent:"-"},"Ú":{char:"U",accent:"´"},"Ù":{char:"U",accent:"`"},"Û":{char:"U",accent:"^"},"Ü":{char:"U",accent:"¨"},"Ű":{char:"U",accent:"˝"},"Ǔ":{char:"U",accent:"ˇ"},"Ǖ":{char:"U",accent:"¨"},"Ȕ":{char:"U",accent:"`"},"Ȗ":{char:"U",accent:"¨"},"Ū":{char:"U",accent:"-"},"Ý":{char:"Y",accent:"´"},"Ỳ":{char:"Y",accent:"`"},"Ŷ":{char:"Y",accent:"^"},"Ÿ":{char:"Y",accent:"¨"},"Ȳ":{char:"Y",accent:"-"},"ñ":{char:"n",accent:"~"},"Ñ":{char:"N",accent:"~"},"ç":{char:"c",accent:"˙"},"Ç":{char:"C",accent:"˙"},"ṽ":{char:"v",accent:"~"},"Ṽ":{char:"V",accent:"~"},"ĵ":{char:"j",accent:"^"},"Ĵ":{char:"J",accent:"^"},"ź":{char:"z",accent:"´"},"Ź":{char:"Z",accent:"´"},"Ż":{char:"Z",accent:"^"},"ż":{char:"z",accent:"^"},"Ž":{char:"Z",accent:"ˇ"},"ž":{char:"z",accent:"ˇ"},"ẑ":{char:"z",accent:"ˆ"}},a={"´":"grave","`":"acute","^":"hat","~":"tilde","¨":"ddot","˚":"mathring","˘":"breve","ˇ":"check","˝":"ddot","˙":"dot","-":"bar","ˆ":"hat","˜":"tilde"},n={"𝐀":{letter:"A",fontCmd:"mathbf"},"𝐁":{letter:"B",fontCmd:"mathbf"},"𝐂":{letter:"C",fontCmd:"mathbf"},"𝐃":{letter:"D",fontCmd:"mathbf"},"𝐄":{letter:"E",fontCmd:"mathbf"},"Ε":{letter:"E",fontCmd:"mathbf"},"𝐅":{letter:"F",fontCmd:"mathbf"},"𝐆":{letter:"G",fontCmd:"mathbf"},"𝐇":{letter:"H",fontCmd:"mathbf"},"𝐈":{letter:"I",fontCmd:"mathbf"},"𝐉":{letter:"J",fontCmd:"mathbf"},"𝐊":{letter:"K",fontCmd:"mathbf"},"𝐋":{letter:"L",fontCmd:"mathbf"},"𝐌":{letter:"M",fontCmd:"mathbf"},"𝐍":{letter:"N",fontCmd:"mathbf"},"𝐎":{letter:"O",fontCmd:"mathbf"},"𝐏":{letter:"P",fontCmd:"mathbf"},"𝐐":{letter:"Q",fontCmd:"mathbf"},"𝐑":{letter:"R",fontCmd:"mathbf"},"𝐒":{letter:"S",fontCmd:"mathbf"},"𝐓":{letter:"T",fontCmd:"mathbf"},"𝐔":{letter:"U",fontCmd:"mathbf"},"𝐕":{letter:"V",fontCmd:"mathbf"},"𝐖":{letter:"W",fontCmd:"mathbf"},"𝐗":{letter:"X",fontCmd:"mathbf"},"𝞆":{letter:"X",fontCmd:"mathbf"},"𝐘":{letter:"Y",fontCmd:"mathbf"},"𝐙":{letter:"Z",fontCmd:"mathbf"},"𝟎":{letter:"0",fontCmd:"mathbf"},"𝟏":{letter:"1",fontCmd:"mathbf"},"𝟐":{letter:"2",fontCmd:"mathbf"},"𝟑":{letter:"3",fontCmd:"mathbf"},"𝟒":{letter:"4",fontCmd:"mathbf"},"𝟓":{letter:"5",fontCmd:"mathbf"},"𝟔":{letter:"6",fontCmd:"mathbf"},"𝟕":{letter:"7",fontCmd:"mathbf"},"𝟖":{letter:"8",fontCmd:"mathbf"},"𝟗":{letter:"9",fontCmd:"mathbf"},"𝐴":{letter:"A",fontCmd:"mathit"},"𝐵":{letter:"B",fontCmd:"mathit"},"𝐶":{letter:"C",fontCmd:"mathit"},"𝐷":{letter:"D",fontCmd:"mathit"},"𝐸":{letter:"E",fontCmd:"mathit"},"𝐹":{letter:"F",fontCmd:"mathit"},"𝐺":{letter:"G",fontCmd:"mathit"},"𝐻":{letter:"H",fontCmd:"mathit"},"𝐼":{letter:"I",fontCmd:"mathit"},"Ι":{letter:"I",fontCmd:"mathit"},"𝐽":{letter:"J",fontCmd:"mathit"},"𝐾":{letter:"K",fontCmd:"mathit"},"𝐿":{letter:"L",fontCmd:"mathit"},"𝑀":{letter:"M",fontCmd:"mathit"},"𝑁":{letter:"N",fontCmd:"mathit"},"𝑂":{letter:"O",fontCmd:"mathit"},"𝑃":{letter:"P",fontCmd:"mathit"},"𝑄":{letter:"Q",fontCmd:"mathit"},"𝑅":{letter:"R",fontCmd:"mathit"},"𝑆":{letter:"S",fontCmd:"mathit"},"𝑇":{letter:"T",fontCmd:"mathit"},"𝑈":{letter:"U",fontCmd:"mathit"},"𝑉":{letter:"V",fontCmd:"mathit"},"𝑊":{letter:"W",fontCmd:"mathit"},"𝑋":{letter:"X",fontCmd:"mathit"},"𝑌":{letter:"Y",fontCmd:"mathit"},"𝑍":{letter:"Z",fontCmd:"mathit"},"𝔸":{letter:"A",fontCmd:"mathbb"},"𝔹":{letter:"B",fontCmd:"mathbb"},"ℂ":{letter:"C",fontCmd:"mathbb"},"𝔻":{letter:"D",fontCmd:"mathbb"},"𝔼":{letter:"E",fontCmd:"mathbb"},"𝔽":{letter:"F",fontCmd:"mathbb"},"𝔾":{letter:"G",fontCmd:"mathbb"},"ℍ":{letter:"H",fontCmd:"mathbb"},"𝕀":{letter:"I",fontCmd:"mathbb"},"𝕁":{letter:"J",fontCmd:"mathbb"},"𝕂":{letter:"K",fontCmd:"mathbb"},"𝕃":{letter:"L",fontCmd:"mathbb"},"𝕄":{letter:"M",fontCmd:"mathbb"},"ℕ":{letter:"N",fontCmd:"mathbb"},"𝕆":{letter:"O",fontCmd:"mathbb"},"ℙ":{letter:"P",fontCmd:"mathbb"},"ℚ":{letter:"Q",fontCmd:"mathbb"},"ℝ":{letter:"R",fontCmd:"mathbb"},"𝕊":{letter:"S",fontCmd:"mathbb"},"𝕋":{letter:"T",fontCmd:"mathbb"},"𝕌":{letter:"U",fontCmd:"mathbb"},"𝕍":{letter:"V",fontCmd:"mathbb"},"𝕎":{letter:"W",fontCmd:"mathbb"},"𝕏":{letter:"X",fontCmd:"mathbb"},"𝕐":{letter:"Y",fontCmd:"mathbb"},"ℤ":{letter:"Z",fontCmd:"mathbb"},"𝟘":{letter:"0",fontCmd:"mathbb"},"𝟙":{letter:"1",fontCmd:"mathbb"},"𝟚":{letter:"2",fontCmd:"mathbb"},"𝟛":{letter:"3",fontCmd:"mathbb"},"𝟜":{letter:"4",fontCmd:"mathbb"},"𝟝":{letter:"5",fontCmd:"mathbb"},"𝟞":{letter:"6",fontCmd:"mathbb"},"𝟟":{letter:"7",fontCmd:"mathbb"},"𝟠":{letter:"8",fontCmd:"mathbb"},"𝟡":{letter:"9",fontCmd:"mathbb"},"𝒜":{letter:"A",fontCmd:"mathcal"},"𝓐":{letter:"A",fontCmd:"mathcal"},"ℬ":{letter:"B",fontCmd:"mathcal"},"𝒞":{letter:"C",fontCmd:"mathcal"},"𝒟":{letter:"D",fontCmd:"mathcal"},"𝓓":{letter:"D",fontCmd:"mathcal"},"ℰ":{letter:"E",fontCmd:"mathcal"},"ℱ":{letter:"F",fontCmd:"mathcal"},"𝓕":{letter:"F",fontCmd:"mathcal"},"𝒢":{letter:"G",fontCmd:"mathcal"},"ℋ":{letter:"H",fontCmd:"mathcal"},"ℐ":{letter:"I",fontCmd:"mathcal"},"𝒥":{letter:"J",fontCmd:"mathcal"},"𝒦":{letter:"K",fontCmd:"mathcal"},"ℒ":{letter:"L",fontCmd:"mathcal"},"𝓛":{letter:"L",fontCmd:"mathcal"},"ℳ":{letter:"M",fontCmd:"mathcal"},"𝒩":{letter:"N",fontCmd:"mathcal"},"𝒪":{letter:"O",fontCmd:"mathcal"},"𝓞":{letter:"O",fontCmd:"mathcal"},"𝒫":{letter:"P",fontCmd:"mathcal"},"𝒬":{letter:"Q",fontCmd:"mathcal"},"ℛ":{letter:"R",fontCmd:"mathcal"},"𝕽":{letter:"R",fontCmd:"mathcal"},"℟":{letter:"R",fontCmd:"mathcal"},"𝒮":{letter:"S",fontCmd:"mathcal"},"𝒯":{letter:"T",fontCmd:"mathcal"},"𝒰":{letter:"U",fontCmd:"mathcal"},"𝒱":{letter:"V",fontCmd:"mathcal"},"𝒲":{letter:"W",fontCmd:"mathcal"},"𝒳":{letter:"X",fontCmd:"mathcal"},"𝒴":{letter:"Y",fontCmd:"mathcal"},"𝒵":{letter:"Z",fontCmd:"mathcal"},"𝔄":{letter:"A",fontCmd:"mathfrak"},"𝔅":{letter:"B",fontCmd:"mathfrak"},"ℭ":{letter:"C",fontCmd:"mathfrak"},"𝔇":{letter:"D",fontCmd:"mathfrak"},"𝔈":{letter:"E",fontCmd:"mathfrak"},"𝔉":{letter:"F",fontCmd:"mathfrak"},"𝔊":{letter:"G",fontCmd:"mathfrak"},"ℌ":{letter:"H",fontCmd:"mathfrak"},"ℑ":{letter:"I",fontCmd:"mathfrak"},"𝔍":{letter:"J",fontCmd:"mathfrak"},"𝔎":{letter:"K",fontCmd:"mathfrak"},"𝔏":{letter:"L",fontCmd:"mathfrak"},"𝔐":{letter:"M",fontCmd:"mathfrak"},"𝔑":{letter:"N",fontCmd:"mathfrak"},"𝔒":{letter:"O",fontCmd:"mathfrak"},"𝔓":{letter:"P",fontCmd:"mathfrak"},"𝔔":{letter:"Q",fontCmd:"mathfrak"},"ℜ":{letter:"R",fontCmd:"mathfrak"},"𝔖":{letter:"S",fontCmd:"mathfrak"},"𝔗":{letter:"T",fontCmd:"mathfrak"},"𝔘":{letter:"U",fontCmd:"mathfrak"},"𝔙":{letter:"V",fontCmd:"mathfrak"},"𝔚":{letter:"W",fontCmd:"mathfrak"},"𝔛":{letter:"X",fontCmd:"mathfrak"},"𝔜":{letter:"Y",fontCmd:"mathfrak"},"ℨ":{letter:"Z",fontCmd:"mathfrak"},"𝖠":{letter:"A",fontCmd:"mathsf"},"Α":{letter:"A",fontCmd:"mathsf"},"𝖡":{letter:"B",fontCmd:"mathsf"},"Β":{letter:"B",fontCmd:"mathsf"},"𝖢":{letter:"C",fontCmd:"mathsf"},"𝖣":{letter:"D",fontCmd:"mathsf"},"𝖤":{letter:"E",fontCmd:"mathsf"},"𝖥":{letter:"F",fontCmd:"mathsf"},"𝖦":{letter:"G",fontCmd:"mathsf"},"𝖧":{letter:"H",fontCmd:"mathsf"},"𝖨":{letter:"I",fontCmd:"mathsf"},"𝖩":{letter:"J",fontCmd:"mathsf"},"ȷ":{letter:"J",fontCmd:"mathsf"},"𝖪":{letter:"K",fontCmd:"mathsf"},"Κ":{letter:"K",fontCmd:"mathsf"},"𝖫":{letter:"L",fontCmd:"mathsf"},"𝖬":{letter:"M",fontCmd:"mathsf"},"𝖭":{letter:"N",fontCmd:"mathsf"},"𝖮":{letter:"O",fontCmd:"mathsf"},"𝖯":{letter:"P",fontCmd:"mathsf"},"𝖰":{letter:"Q",fontCmd:"mathsf"},"𝖱":{letter:"R",fontCmd:"mathsf"},"𝖲":{letter:"S",fontCmd:"mathsf"},"𝖳":{letter:"T",fontCmd:"mathsf"},"𝖴":{letter:"U",fontCmd:"mathsf"},"𝖵":{letter:"V",fontCmd:"mathsf"},"𝖶":{letter:"W",fontCmd:"mathsf"},"𝖷":{letter:"X",fontCmd:"mathsf"},"Χ":{letter:"X",fontCmd:"mathsf"},"𝖸":{letter:"Y",fontCmd:"mathsf"},"𝖹":{letter:"Z",fontCmd:"mathsf"},"𝚨":{letter:"A",fontCmd:"mathtt"},"𝚩":{letter:"B",fontCmd:"mathtt"},"𝚪":{letter:"\\Gamma",fontCmd:"mathtt"},"𝚫":{letter:"\\Delta",fontCmd:"mathtt"},"𝚬":{letter:"E",fontCmd:"mathtt"},"𝚭":{letter:"F",fontCmd:"mathtt"},"𝚮":{letter:"G",fontCmd:"mathtt"},"𝚯":{letter:"\\Theta",fontCmd:"mathtt"},"𝚰":{letter:"I",fontCmd:"mathtt"},"𝚱":{letter:"J",fontCmd:"mathtt"},"𝚲":{letter:"\\Lambda",fontCmd:"mathtt"},"𝚳":{letter:"L",fontCmd:"mathtt"},"𝚴":{letter:"M",fontCmd:"mathtt"},"𝚵":{letter:"\\Pi",fontCmd:"mathtt"},"𝚶":{letter:"O",fontCmd:"mathtt"},"𝚷":{letter:"\\Pi",fontCmd:"mathtt"},"𝚸":{letter:"Q",fontCmd:"mathtt"},"𝚹":{letter:"R",fontCmd:"mathtt"},"𝚺":{letter:"S",fontCmd:"mathtt"},"𝚻":{letter:"T",fontCmd:"mathtt"},"𝚼":{letter:"U",fontCmd:"mathtt"},"𝚽":{letter:"\\Phi",fontCmd:"mathtt"},"𝚾":{letter:"W",fontCmd:"mathtt"},"𝚿":{letter:"\\Psi",fontCmd:"mathtt"},"𝛀":{letter:"\\Omega",fontCmd:"mathtt"}}}},n={},o={},(()=>{"use strict";var e=o,t=(Object.defineProperty(e,"__esModule",{value:!0}),e.MathMLToLaTeX=void 0,r(8863));Object.defineProperty(e,"MathMLToLaTeX",{enumerable:!0,get:function(){return t.MathMLToLaTeX}})})(),o;function r(e){var t=n[e];return void 0!==t||(t=n[e]={exports:{}},a[e].call(t.exports,t,t.exports,r)),t.exports}var a,n,o});