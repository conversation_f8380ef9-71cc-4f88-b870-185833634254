"use strict";

layui.define('jquery', function (exports) {
  var $ = layui.jquery;
  var common = {
    //最小高度计算判断
    layout: function layout() {
      var overHeight = $(window).height() - $('.top').outerHeight() - $('.foot').outerHeight();
      $('.main-body').css("minHeight", overHeight);
      return overHeight;
    }
  };

  //最小高度赋值
  common.layout();

  //----------------------用户信息下拉------------------------
  //头部下拉菜单点击
  $('.user-title').click(function () {
    var open = $(this).parent('.user-info').is('.open');
    if (open) {
      $(this).parent('.user-info').removeClass('open');
      $(this).parent('.user-info').find('.user-list').slideUp();
    } else {
      $(this).parent('.user-info').addClass('open');
      $(this).parent('.user-info').find('.user-list').slideDown();
    }
  });
  //点击空白处收起头部菜单栏
  $(document).bind('click', function () {
    $('.user-info').removeClass('open');
    $('.user-list').slideUp();
  });
  //阻止冒泡
  $('.user-info').click(function (e) {
    e.stopPropagation(); //阻止冒泡
  });

  //----------------------input低版本placeholder兼容------------------------
  //输入框提示信息(placeholder)显示隐藏
  $(".placeholder-input").each(function () {
    var placehdr = $(this).next('input').val();
    $(this).val(placehdr);
    $(this).css('color', '#a9a9a9');
  });
  $(".placeholder-input").focusout(function () {
    var placehdr = $(this).next('input').val();
    if ($(this).val() == "") {
      $(this).val(placehdr);
      $(this).css('color', '#a9a9a9');
    }
  });
  $(".placeholder-input").focusin(function () {
    var placehdr = $(this).next('input').val();
    if ($(this).val() == placehdr) {
      $(this).val("");
      $(this).css('color', '#272822');
    }
  });

  //----------------------左导航------------------------
  //左导航初始展开
  $('.nav-ul li').removeClass('open').find('.nav-node').hide();
  $('.nav-ul li').eq(1).addClass('open').find('.nav-node').show();
  //左导航栏点击
  $('.nav-title').click(function () {
    var open = $(this).parent().is('.open');
    if (!open) {
      $('.nav-ul li').removeClass('open').find('.nav-node').slideUp();
      $(this).parent().addClass('open').find('.nav-node').slideDown();
      $('.nav-node-title').removeClass('cur');
    }
  });
  //左导航子节点点击
  $('.nav-node-title').click(function () {
    $('.nav-node-title').removeClass('cur');
    $(this).addClass('cur');
  });

  //----------------------下拉菜单通用------------------------
  //功能class:listdown/listdown-title/listdown-content/listdown-val/添加class为open
  //点击展开
  $('.listdown-title').click(function () {
    var open = $(this).closest('.listdown').is('.open');
    if (open) {
      $(this).closest('.listdown').removeClass('open').find('.listdown-content').slideUp();
    } else {
      $(this).closest('.listdown').addClass('open').find('.listdown-content').slideDown();
    }
  });
  //点击选择项
  $('.listdown-content ul li').click(function () {
    var text = $(this).find('span').html();
    $(this).closest('.listdown').removeClass('open');
    $(this).closest('.listdown').find('.listdown-val').html(text);
    $(this).closest('.listdown-content').slideUp();
  });
  //点击空白处收起下拉菜单
  $(document).bind('click', function () {
    $('.listdown').removeClass('open');
    $('.listdown-content').slideUp();
  });
  //阻止冒泡
  $('.listdown').click(function (e) {
    e.stopPropagation(); //阻止冒泡
  });

  //----------------------复选类型通用------------------------
  //功能class:check-sel/添加class为cur
  $('.check-sel').click(function () {
    var select = $(this).is('.cur');
    if (select) {
      $(this).removeClass('cur');
    } else {
      $(this).addClass('cur');
    }
  });

  //页面大小变动时执行函数
  $(window).resize(function () {
    layout();
  });

  //输出common接口
  exports('common', common);
});
