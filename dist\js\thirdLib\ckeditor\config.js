"use strict";

/**
 * @license Copyright (c) 2003-2018, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see https://ckeditor.com/legal/ckeditor-oss-license
 */
var _wrs_conf_defaultEditMode = "mathJax"; // 公式用mathjax表示
var _wrs_conf_imageClassName = 'Wirisformula'; // 公式的类名
window.CKEDITOR.editorConfig = function (config) {
  // Define changes to default configuration here.
  // For complete reference see:
  // http://docs.ckeditor.com/#!/api/CKEDITOR.config
  // config.language = 'zh-cn';
  // The toolbar groups arrangement, optimized for two toolbar rows.

  /*  config.toolbarGroups = [
     { name: 'styles' },
     { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
     // { name: 'basicstyles', groups: ['Bold', 'Italic', 'Underline', 'Subscript', 'Superscript', 'Heavy', 'CustomUnderline', 'CustomUnderlineSwitch', 'cleanup'] },
     // { name: 'editing', groups: ['find', 'selection'] },
     // { name: 'links' },
     '/',
     { name: 'insert' },
     // { name: 'forms' },
     // { name: 'tools' },
     { name: 'document', groups: ['mode', 'document', 'doctools'] },
     { name: 'others' }, // used for wiris
     { name: 'paragraph', groups: ['align', 'bidi'] },
     // { name: 'colors' },
     // { name: 'about' },
     { name: 'justify', groups: ['JustifyLeft', 'JustifyCenter', 'JustifyRight'] },
     { name: 'save', groups: ['undo'] },
     // { name: 'wiris', groups: ['ckeditor_wiris_formulaEditor', 'ckeditor_wiris_formulaEditorChemistry'] },
     { name: 'wiris', groups: ['ckeditor_wiris_formulaEditor'] },
   ];   */

  config.toolbar = [{
    name: 'fontline',
    items: ['FontSize', 'lineheight']
  },
  //,'Superscript','Subscript','RemoveFormat'
  {
    name: 'basicstyles',
    items: ['Bold', 'Italic', 'Underline', 'Subscript', 'Superscript', 'Heavy', 'CustomUnderline', 'CustomUnderlineSwitch', 'SolidUnderline', 'Dottedline', 'RemoveFormat', 'dotEmph', 'option', 'blank']
  }, {
    name: 'justify',
    items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock']
  }, {
    name: 'insert',
    items: ['uploadImg', 'Table', 'Smiley', 'SpecialChar']
  }, {
    name: 'wiris',
    items: ['ckeditor_wiris_formulaEditor']
  }, {
    name: 'save',
    items: ['Undo', 'Redo']
  }];

  // Remove some buttons provided by the standard plugins, which are
  // not needed in the Standard(s) toolbar.
  config.removeButtons = 'Strike,Format,HorizontalRule';
  // config.removeButtons = 'Underline,Scayt,Cut,Copy,Link,Unlink,Anchor,Outdent,Indent,Blockquote,Strike,Source,Maximize,HorizontalRule,NumberedList,BulletedList';
  // Set the most common block elements.
  // config.format_tags = 'p;h1;h2;h3;pre';
  // config.pasteFromWordRemoveStyles = true;
  // config.pasteFromWordRemoveFontStyles = true;
  // config.enterMode = window.CKEDITOR.ENTER_BR;
  // config.shiftEnterMode = window.CKEDITOR.ENTER_BR;

  config.enterMode = window.CKEDITOR.ENTER_P;
  config.shiftEnterMode = window.CKEDITOR.ENTER_P;
  config.disableObjectResizing = false;
  // config.floatSpacePreferRight = true;
  /*
  config.forcePasteAsPlainText = false;
  config.allowedContent = true;
  */
  config.fontSize_sizes = '小五/3.2mm;五号/3.5mm;小四/3.7mm;四号/4mm;小三/4.2mm;三号/4.4mm;小二/4.8mm;';
  config.font_names = '新罗马/Times New Roman ,Times;' + '宋体/SimSun, NSimSun;' + '微软雅黑/Microsoft YaHei,SimHei;' + '幼圆/YouYuan,Tahoma,Verdana;' + 'Arial/Arial, Helvetica, sans-serif;' + 'Georgia/Georgia, serif;';

  // Simplify the dialog windows.
  // config.removeDialogTabs = 'image:advanced;link:advanced';
  // config.extraAllowedContent = 'a(documentation);abbr[title];code;p[name;p[id]';
  // Add[MT]to the integration list
  // config.extraPlugins = 'sourcedialog';
  // config.extraPlugins += (config.extraPlugins.length === 0 ? '' : ',') + 'ckeditor_wiris';
  // remove plugin: imageresizerowandcolumn
  config.extraPlugins = 'font,ckeditor_wiris';
  // config.extraPlugins = 'CustomUnderline,CustomUnderlineSwitch,Drag,enterIcon';
  // config.extraPlugins += ',font';
  // config.extraPlugins += (config.extraPlugins ? ',ckeditor_wiris' : 'ckeditor_wiris');
  // config.removeButtons = 'Scayt,Cut,Copy,Link,Unlink,Anchor,Outdent,Indent,Blockquote,Strike,Source,Maximize,HorizontalRule,NumberedList,BulletedList';
  config.specialChars = ['&#9312;', '&#9313;', '&#9314;', '&#9315;', '&#9316;', '&#9317;', '&#9318;', '&#9319;', '&#9320;', '&#9321;', '&quot;', '#', '$', '%', '&amp;', "'", '*', '&lt;', '=', '&gt;', '@', '^', '_', '`', '{', '|', '}', '~', '&euro;', '&lsquo;', '&rsquo;', '&ldquo;', '&rdquo;', '&ndash;', '&mdash;', '&iexcl;', '&cent;', '&pound;', '&curren;', '&yen;', '&brvbar;', '&sect;', '&uml;', '&copy;', '&ordf;', '&laquo;', '&not;', '&reg;', '&macr;', '&deg;', '&sup2;', '&sup3;', '&acute;', '&micro;', '&para;', '&middot;', '&cedil;', '&sup1;', '&ordm;', '&raquo;', '&frac14;', '&frac12;', '&frac34;', '&iquest;', '&Agrave;', '&Aacute;', '&Acirc;', '&Atilde;', '&Auml;', '&Aring;', '&AElig;', '&Ccedil;', '&Egrave;', '&Eacute;', '&Ecirc;', '&Euml;', '&Igrave;', '&Iacute;', '&Icirc;', '&Iuml;', '&ETH;', '&Ntilde;', '&Ograve;', '&Oacute;', '&Ocirc;', '&Otilde;', '&Ouml;', '&times;', '&Oslash;', '&Ugrave;', '&Uacute;', '&Ucirc;', '&Uuml;', '&Yacute;', '&THORN;', '&szlig;', '&agrave;', '&aacute;', '&acirc;', '&atilde;', '&auml;', '&aring;', '&aelig;', '&ccedil;', '&egrave;', '&eacute;', '&ecirc;', '&euml;', '&igrave;', '&iacute;', '&icirc;', '&iuml;', '&eth;', '&ntilde;', '&ograve;', '&oacute;', '&ocirc;', '&otilde;', '&ouml;', '&divide;', '&oslash;', '&ugrave;', '&uacute;', '&ucirc;', '&uuml;', '&yacute;', '&thorn;', '&yuml;', '&OElig;', '&oelig;', '&#372;', '&#374', '&#373', '&#375;', '&sbquo;', '&#8219;', '&bdquo;', '&hellip;', '&trade;', '&#9658;', '&bull;', '&rarr;', '&rArr;', '&hArr;', '&diams;', '&asymp;'];
  config.allowedContent = true;
  // CKEDITOR.dtd.$removeEmpty.span = 0; // 防止过滤空span
  config.removePlugins = 'stylescombo, magicline';
  config.startupFocus = false;
  //   config.filebrowserBrowseUrl = 'https://cksource.com/weuy2g4ryt278ywiue/core/connector/php/connector.php?command=QuickUpload&type=Files&responseType=json';
  //   config.filebrowserUploadUrl = 'https://cksource.com/weuy2g4ryt278ywiue/core/connector/php/connector.php?command=QuickUpload&type=Files&responseType=json';
};
/*
window.CKEDITOR.editorConfig = function (config) {
  // Allow some non-standard markup that we used in the introduction.
  //   extraAllowedContent: 'a(documentation);abbr[title];code',
  //    removePlugins: 'stylescombo',

  config.extraAllowedContent = 'a(documentation);abbr[title];code';
  config.removePlugins = 'stylescombo';
  config.extraPlugins = 'sourcedialog';
  // Show toolbar on startup (optional).
  config.startupFocus = false;
};
*/
