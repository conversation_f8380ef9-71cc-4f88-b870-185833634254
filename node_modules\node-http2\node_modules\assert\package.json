{"_from": "assert@1.4.1", "_id": "assert@1.4.1", "_inBundle": false, "_integrity": "sha512-N+aAxov+CKVS3JuhDIQFr24XvZvwE96Wlhk9dytTg/GmwWoghdOvR8dspx8MVz71O+Y0pA3UPqHF68D6iy8UvQ==", "_location": "/node-http2/assert", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "assert@1.4.1", "name": "assert", "escapedName": "assert", "rawSpec": "1.4.1", "saveSpec": null, "fetchSpec": "1.4.1"}, "_requiredBy": ["/node-http2"], "_resolved": "https://registry.npmjs.org/assert/-/assert-1.4.1.tgz", "_shasum": "99912d591836b5a6f5b345c0f07eefc08fc65d91", "_spec": "assert@1.4.1", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\node-http2", "bugs": {"url": "https://github.com/defunctzombie/commonjs-assert/issues"}, "bundleDependencies": false, "dependencies": {"util": "0.10.3"}, "deprecated": false, "description": "commonjs assert - node.js api compatible", "devDependencies": {"mocha": "~1.21.4", "zuul": "~3.10.0", "zuul-ngrok": "^4.0.0"}, "homepage": "https://github.com/defunctzombie/commonjs-assert", "keywords": ["assert"], "license": "MIT", "main": "./assert.js", "name": "assert", "repository": {"type": "git", "url": "git://github.com/defunctzombie/commonjs-assert.git"}, "scripts": {"browser-local": "zuul --no-coverage --local 8000 -- test.js", "test": "npm run test-node && npm run test-browser", "test-browser": "zuul -- test.js", "test-native": "TEST_NATIVE=true mocha --ui qunit test.js", "test-node": "mocha --ui qunit test.js"}, "version": "1.4.1"}