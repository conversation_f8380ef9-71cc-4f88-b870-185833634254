/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'is', {
	alt: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> texti',
	border: '<PERSON><PERSON>',
	btnUpload: 'Hlaða upp',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: '<PERSON><PERSON><PERSON> bil',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Almennt',
	linkTab: '<PERSON>ikla',
	lockRatio: 'Festa stærðarhlutfall',
	menu: 'Eigindi myndar',
	resetSize: '<PERSON><PERSON>na stærð',
	title: 'Eigindi myndar',
	titleButton: 'Eigindi myndahnapps',
	upload: 'Hlaða upp',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: '<PERSON><PERSON><PERSON><PERSON> bil',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );
