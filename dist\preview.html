<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>预览</title><link rel="stylesheet" href="css/testbank/common.css"><style>::-webkit-scrollbar{width:6px}::-webkit-scrollbar-button{display:none}::-webkit-scrollbar-thumb{background:#ebedf5;border-radius:8px}body{user-select:none;background-color:#fff;padding-bottom:60px}.item{padding:12px}.item-text{padding-left:20px}.ques-body,.ques-opt{position:relative;padding:25px 5px 5px 5px;background-color:#ebf2ff;border-radius:3px}.ques-ans,.ques-exp{position:relative;padding:25px 5px 5px 5px;background-color:#fff5eb;margin:10px 0;border-radius:3px}.ques-lenge{position:absolute;top:.5px;left:.5px;text-align:center;line-height:24px;display:inline-block;width:56px;height:24px;background-color:#fff;border-radius:4px 0 0 0;color:#3b85fa}.ques-lenge.ans-lenge,.ques-lenge.exp-lenge{color:#ff8f1f}.footer{position:fixed;left:0;right:0;bottom:0;height:60px;margin:0;display:flex;align-items:center;justify-content:flex-end;background-color:#fff;box-shadow:0 -1px 6px 0 rgba(51,51,51,.08)}.footer li{width:120px;height:40px;line-height:40px;text-align:center;background:#fff;border:1px solid #e1e4f7;border-radius:3px;margin:0 20px;color:#4e5668;list-style:none;cursor:pointer}.footer li.switch-prev{display:none}.footer li.switch-next{color:#fff;background:linear-gradient(-36deg,#5181f7,#5c8afc)}</style></head><body><div id="question"></div><ul class="footer"><div style="position:absolute;left:20px"><span class="cur">1</span> / <span class="count">10</span></div><li class="switch-prev">上一题</li><li class="switch-next">下一题</li></ul></body></html><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script type="text/x-mathjax-config">MathJax.Hub.Register.StartupHook("TeX Jax Ready", function () {
        var TEX = MathJax.InputJax.TeX;
        // 注册预处理钩子，在处理 TeX 公式前执行
        TEX.prefilterHooks.Add(function (data) {
            // 判断是否是行内公式（非 display 模式）
            if (!data.display) {
                // 给行内公式前面添加 \displaystyle 
                data.math = '\\displaystyle ' + data.math;
            }
            return data;
        });
    });
    MathJax.Hub.Config({
    config: [ 'TeX-AMS_HTML-full.js'],
    TeX: {
        extensions: ["extpfeil.js","AMSmath.js","AMSsymbols.js","noErrors.js","noUndefined.js","cancel.js","mhchem.js","autoload-all.js"]
    }
});</script><script type="text/javascript" src="https://fs.iclass30.com/aliba/plug/mathjax/MathJax.js"></script><script type="text/javascript">$(function(){var s=[],t=0;function e(e){e=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),e=window.location.search.substr(1).match(e);return null!=e?decodeURIComponent(e[2]):null}var a,n=function(){d(),i()},i=function(){$(".footer .cur").text(t+1),$(".footer .count").text(s.length),0==t?$(".switch-prev").hide():$(".switch-prev").show(),t==s.length-1?$(".switch-next").hide():$(".switch-next").show(),0===s.length&&$(".footer").hide()},d=function(){var a="",n=s[t];n?(a+="<div class='item'>",""!=n.data.levelcode&&(a+='<div class="ques-body"><span class="ques-lenge">题干</span><div class="item-text">'+n.data.desc_html+"</div></div>"),n.data.qs.forEach(function(e,s){var t,s=""==n.data.levelcode?"题目":"小题"+(s+1);a+='<div class="ques-body"><span class="ques-lenge">'+s+'</span><div class="item-text">'+e.desc_html+"</div></div>",1!=n.data.type&&8!=n.data.type||!e.opts_htmls.length||(a+='<div class="ques-opt">',e.opts_htmls.forEach(function(e,s){a+='<div class="item-text">'+String.fromCharCode(s+65)+"、"+e+"</div>"}),a+="</div>"),7==n.data.type?(t=[],e.ans.forEach(function(e){/(\\\S)|({+)|(\^+)/.test(e)?t.push("\\("+e+"\\)"):t.push(e)}),a+='<div class="ques-ans"><span class="ques-lenge ans-lenge">答案</span><div class="item-text">'+t.join("，")+"</div></div>"):2==n.data.type?a+='<div class="ques-ans"><span class="ques-lenge ans-lenge">答案</span><div class="item-text">'+("A"==e.ans?"正确":"错误")+"</div></div>":a+='<div class="ques-ans"><span class="ques-lenge ans-lenge">答案</span><div class="item-text">'+e.ans.toString()+"</div></div>",a+='<div class="ques-exp"><span class="ques-lenge exp-lenge">解析</span><div class="item-text">'+e.exp+"</div></div>"}),a+="</div>",$("#question").empty().append(a),MathJax.Hub.Queue(["Typeset",MathJax.Hub,$("#question")[0]])):$("#question").text("暂无题目")};a={testBankId:e("testBankId"),token:e("token")},$.ajax({url:e("url")+"/testbank/TBQuestion/getTestBankQuestionInfo",data:a,type:"POST",datatype:"json",success:function(e){1==e.code&&(s=e.data,$(".switch-prev").click(()=>{0<t&&t--,n()}),$(".switch-next").click(()=>{t<s.length-1&&t++,n()}),n())},error:function(e,s,t){}})})</script>