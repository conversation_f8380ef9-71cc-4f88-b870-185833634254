<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/protocol/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> lib/protocol/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">90.29% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1441/1596</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">80.88% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>609/753</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">91.38% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>159/174</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">90.42% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1435/1587</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="compressor.js"><a href="compressor.js.html">compressor.js</a></td>
	<td data-value="92.61" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.61" class="pct high">92.61%</td>
	<td data-value="406" class="abs high">376/406</td>
	<td data-value="86.18" class="pct high">86.18%</td>
	<td data-value="152" class="abs high">131/152</td>
	<td data-value="87.8" class="pct high">87.8%</td>
	<td data-value="41" class="abs high">36/41</td>
	<td data-value="92.61" class="pct high">92.61%</td>
	<td data-value="406" class="abs high">376/406</td>
	</tr>

<tr>
	<td class="file high" data-value="connection.js"><a href="connection.js.html">connection.js</a></td>
	<td data-value="89.37" class="pic high"><div class="chart"><div class="cover-fill" style="width: 89%;"></div><div class="cover-empty" style="width:11%;"></div></div></td>
	<td data-value="89.37" class="pct high">89.37%</td>
	<td data-value="254" class="abs high">227/254</td>
	<td data-value="76.36" class="pct medium">76.36%</td>
	<td data-value="110" class="abs medium">84/110</td>
	<td data-value="96.97" class="pct high">96.97%</td>
	<td data-value="33" class="abs high">32/33</td>
	<td data-value="89.72" class="pct high">89.72%</td>
	<td data-value="253" class="abs high">227/253</td>
	</tr>

<tr>
	<td class="file high" data-value="endpoint.js"><a href="endpoint.js.html">endpoint.js</a></td>
	<td data-value="88.57" class="pic high"><div class="chart"><div class="cover-fill" style="width: 88%;"></div><div class="cover-empty" style="width:12%;"></div></div></td>
	<td data-value="88.57" class="pct high">88.57%</td>
	<td data-value="105" class="abs high">93/105</td>
	<td data-value="86.36" class="pct high">86.36%</td>
	<td data-value="22" class="abs high">19/22</td>
	<td data-value="81.25" class="pct high">81.25%</td>
	<td data-value="16" class="abs high">13/16</td>
	<td data-value="88.57" class="pct high">88.57%</td>
	<td data-value="105" class="abs high">93/105</td>
	</tr>

<tr>
	<td class="file high" data-value="flow.js"><a href="flow.js.html">flow.js</a></td>
	<td data-value="98.33" class="pic high"><div class="chart"><div class="cover-fill" style="width: 98%;"></div><div class="cover-empty" style="width:2%;"></div></div></td>
	<td data-value="98.33" class="pct high">98.33%</td>
	<td data-value="120" class="abs high">118/120</td>
	<td data-value="93.41" class="pct high">93.41%</td>
	<td data-value="91" class="abs high">85/91</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="15" class="abs high">15/15</td>
	<td data-value="98.33" class="pct high">98.33%</td>
	<td data-value="120" class="abs high">118/120</td>
	</tr>

<tr>
	<td class="file high" data-value="framer.js"><a href="framer.js.html">framer.js</a></td>
	<td data-value="88.56" class="pic high"><div class="chart"><div class="cover-fill" style="width: 88%;"></div><div class="cover-empty" style="width:12%;"></div></div></td>
	<td data-value="88.56" class="pct high">88.56%</td>
	<td data-value="472" class="abs high">418/472</td>
	<td data-value="71.43" class="pct medium">71.43%</td>
	<td data-value="196" class="abs medium">140/196</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="45" class="abs high">42/45</td>
	<td data-value="88.56" class="pct high">88.56%</td>
	<td data-value="472" class="abs high">418/472</td>
	</tr>

<tr>
	<td class="file high" data-value="index.js"><a href="index.js.html">index.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="stream.js"><a href="stream.js.html">stream.js</a></td>
	<td data-value="87.01" class="pic high"><div class="chart"><div class="cover-fill" style="width: 87%;"></div><div class="cover-empty" style="width:13%;"></div></div></td>
	<td data-value="87.01" class="pct high">87.01%</td>
	<td data-value="231" class="abs high">201/231</td>
	<td data-value="82.42" class="pct high">82.42%</td>
	<td data-value="182" class="abs high">150/182</td>
	<td data-value="86.96" class="pct high">86.96%</td>
	<td data-value="23" class="abs high">20/23</td>
	<td data-value="87.44" class="pct high">87.44%</td>
	<td data-value="223" class="abs high">195/223</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Aug 23 2017 13:12:39 GMT-0700 (PDT)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
