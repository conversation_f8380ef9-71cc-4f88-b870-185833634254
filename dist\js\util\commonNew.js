"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
!function (e) {
  var t = {
    data: {
      aliba: "127.0.0.1" == location.hostname ? "https://fs.iclass30.com" : ""
    },
    methods: {
      bubbleSort: function bubbleSort(e) {
        for (var t = e.length - 1; 0 < t; t--) for (var n, a = 0; a < t; a++) e[a] > e[a + 1] && (n = e[a], e.splice(a, 1, e[a + 1]), e.splice(a + 1, 1, n));
      },
      arrange: function arrange(e) {
        var t,
          n,
          a = [];
        return e.forEach(function (e) {
          t === e ? (n.push(t), t++) : (n = [e], t = e + 1, a.push(n));
        }), a;
      },
      getHtmlCont: function getHtmlCont(e) {
        $(e).each(function () {
          var n = $(this),
            e = (n.html("正在加载中..."), n.data("htmlurl"));
          "" != e && ajax(t.data.aliba + e, {}, !0, "get", "html").done(function (e) {
            var t = $(document.createElement("div")).append(e),
              a = e;
            $(".ck-math-tex", t).each(function (e, t) {
              a = a.replace(t.outerHTML, '<span tex="' + t.getAttribute("tex") + '" contenteditable="false" class="ck-math-tex">' + t.getAttribute("tex") + "</span>");
            }), $(".math-show", t).each(function (e, t) {
              0 < $(t).prev(".MathJax_Preview").length && (a = a.replace($(t).prev(".MathJax_Preview").get(0).outerHTML, ""));
              var n = "<span tex=\"\\(".concat($(t).find(".mathTex").text(), "\\)\" contenteditable=\"false\" class=\"ck-math-tex\">\\(").concat($(t).find(".mathTex").text(), "\\)</span>");
              a = a.replace($(t).get(0).outerHTML, n);
            }), n.html(a), MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
          }).fail(function () {
            n.html("加载失败...");
          });
        });
      }
    }
  };
  e.jcUtils = t.methods;
}(window);
var headerEvent = {
    pageJump: function pageJump(e, t) {
      if (-1 < window.location.pathname.indexOf("matchQuesNew.html")) {
        var n = !0;
        if (0 == $(".r-que").length) layer.msg("尚未制作题目!");else {
          for (var a, r = $(".select-ul,.judge-ul"), o = 0; o < r.length; o++) if (0 == $(r[o]).find(".selected").length) n = !1;else if (8 == $(r[o]).parent().data("type") && 1 < $(r[o]).find(".selected").length) return a = $(r[o]).parents(".r-que").find(".tit-show .tit").text(), void layer.msg(a + "为单选题，只能设置一个正确答案，请修正答案或题型");
          0 == (n = 0 < $(".fill-auto-li.set-answer").length ? !1 : n) ? layer.msg("客观题答案尚未设置完成!") : headerEvent.updateCutHtmlSort(e, t);
        }
      } else headerEvent.pageJumpEvent(e, t);
    },
    pageJumpEvent: function pageJumpEvent(e, t) {
      save17Data(t, function () {
        1 == e ? ($(".title-wrap li.qs-deline", window.parent.document).addClass("active").siblings().removeClass("active"), address = "../cutTestPaper/matchQuesNew.html" + window.location.search) : 2 == e ? ($(".title-wrap li.qs-answer", window.parent.document).addClass("active").siblings().removeClass("active"), address = "../cutTestPaper/matchAnsNew.html" + window.location.search) : 3 == e && ($(".title-wrap li.qs-mark", window.parent.document).addClass("active").siblings().removeClass("active"), address = "../cutTestPaper/matchInfo.html" + window.location.search), window.location.href = address;
      });
    },
    updateCutHtmlSort: function updateCutHtmlSort(e, t) {
      if (1 == CutHtml.v.manualState) {
        var n = $("#htmlDiv .sliceMainClass");
        if (0 < n.length) {
          for (var a = "", r = 0; r < n.length; r++) "" == a ? a = $(n[r]).attr("id") : a += "," + $(n[r]).attr("id");
          ajax("/testbank/testBank/batchUpdateCutHtmlSort", {
            cutlist: a
          }, !1).done(function (e) {});
        }
      }
      headerEvent.pageJumpEvent(e, t);
    }
  },
  CommonJs = {
    v: {
      network: "https://fs.iclass30.com",
      admin: ",1emcaa-oorvkzh7smvpifw||,-cpcaaanvz1cqqdmcgrlog,ac1caaanhahmdtecwfsvqq,fszcaaan2pvbuv-lmxudgq,jc5caaanxbrb0jvut6uxtg,ocpcaaanfa1jt0ewph8vsq,tytcaaan2ipbhxm-hs8qaw"
    },
    methods: {
      replaceALiUrl: function replaceALiUrl(e) {
        var t;
        return e ? (t = CommonJs.v.network, e.startsWith("http") ? e : e.startsWith("/") ? t + e : t + "/" + e) : "";
      },
      numberConvert: function numberConvert(e) {
        var t,
          n,
          a = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
        return 1 == e.length ? e = a[e] : 2 == e.length && (t = e.substring(0, 1), n = e.substring(1), e = "1" == t ? "十" + a[n] : a[e.substring(0, 1)] + "十" + a[e.substring(1)]), e.replace("零", "");
      },
      isObjective: function isObjective(e) {
        return 8 == e || 1 == e || 2 == e || 5 == e || 7 == e;
      }
    }
  };
function layout() {
  var e = $(window).height() - $(".top").outerHeight() - $(".foot").outerHeight();
  $(".main-body").css("minHeight", e);
}
function getQueryString(e) {
  e = new RegExp("(^|&)" + e + "=([^&]*)(&|$)", "i"), e = window.location.search.substr(1).match(e);
  return null != e ? decodeURI(e[2]) : null;
}
$(function () {
  layout(), $(".user-title").click(function () {
    $(this).parent(".user-info").is(".open") ? ($(this).parent(".user-info").removeClass("open"), $(this).parent(".user-info").find(".user-list").slideUp()) : ($(this).parent(".user-info").addClass("open"), $(this).parent(".user-info").find(".user-list").slideDown());
  }), $(document).bind("click", function () {
    $(".user-info").removeClass("open"), $(".user-list").slideUp();
  }), $(".user-info").click(function (e) {
    e.stopPropagation();
  }), window.location.host.substring(0, 5);
}), $(window).resize(function () {
  layout();
}), String.prototype.format = function (e) {
  if (0 < arguments.length) {
    var t = this;
    if (1 == arguments.length && "object" == _typeof(e)) for (var n in e) var a = new RegExp("\\{" + n + "\\}", "g"), t = t.replace(a, e[n]);else for (var r = 0; r < arguments.length; r++) {
      if (null == arguments[r]) return "";
      a = new RegExp("\\{" + r + "\\}", "g");
      t = t.replace(a, arguments[r]);
    }
    return t;
  }
  return this;
};
var ObjToMap = function ObjToMap(obj, keyName) {
    var strMap = new Map();
    return obj.forEach(function (value) {
      strMap.set(eval("value." + keyName), value);
    }), strMap;
  },
  MapToObj = function MapToObj(e) {
    var t = new Array();
    return e.forEach(function (e) {
      t.push(e);
    }), t;
  },
  helpCarousel = (String.prototype.replaceAll = function (e, t) {
    return regExp = new RegExp(e, "g"), this.replace(regExp, t);
  }, Array.prototype["delete"] = function (e) {
    for (var t = 0; t < this.length; t++) this[t] == e && this.splice(t, 1);
  }, Array.prototype.get = function (e) {
    for (var t = 0; t < this.length; t++) if (this[t].id == e) return this[t];
    return null;
  }, {
    n: 1,
    dir: 1,
    img_num: 0,
    init: function init() {
      helpCarousel.n = 1, helpCarousel.dir = 1, helpCarousel.img_num = $(".scrollobj img").length - 1, $(".btnLeft").click(function () {
        helpCarousel.runR();
      }), $(".btnRight").click(function () {
        helpCarousel.runL();
      }), $(".ctrl li").click(function () {
        n = $(this).index() + 1, $(".ctrl li").removeClass("current"), $(this).addClass("current"), $(".scrollobj").stop(!0, !0).animate({
          left: -700 * n
        }, 1e3);
      });
    },
    runL: function runL() {
      helpCarousel.dir = 1, helpCarousel.n < helpCarousel.img_num ? helpCarousel.n = helpCarousel.n + 1 : (helpCarousel.n = 2, $(".scrollobj").css({
        left: -700 * (helpCarousel.n - 1)
      })), $(".scrollobj").stop(!0, !0).animate({
        left: -700 * helpCarousel.n
      }, 1e3), $(".ctrl li").removeClass("current"), (helpCarousel.n == helpCarousel.img_num ? $(".ctrl li").eq(0) : $(".ctrl li").eq(helpCarousel.n - 1)).addClass("current");
    },
    runR: function runR() {
      (helpCarousel.dir = 0) < helpCarousel.n ? helpCarousel.n = helpCarousel.n - 1 : (helpCarousel.n = 2, $(".scrollobj").css({
        left: -700 * (helpCarousel.n + 1)
      })), $(".scrollobj").stop(!0, !0).animate({
        left: -700 * helpCarousel.n
      }, 1e3), $(".ctrl li").removeClass("current"), $(".ctrl li").eq(helpCarousel.n - 1).addClass("current");
    }
  });
function intToChinese(e) {
  var s = (e += "").length - 1,
    l = ["", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万", "十", "百", "千", "亿"],
    u = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  return e.replace(/([1-9]|0+)/g, function (e, t, n, a) {
    var r,
      o,
      i = 0;
    return "0" != t[0] ? (i = s - n, 0 == n && 1 == t[0] && "十" == l[s - n] ? l[s - n] : u[t[0]] + l[s - n]) : (o = (r = s - n) + t.length, (i = 0 < Math.floor(o / 4) - Math.floor(r / 4) ? r - r % 4 : i) ? l[i] + u[t[0]] : n + t.length >= s ? "" : u[t[0]]);
  });
}
String.prototype.trim = function () {
  for (var e = this, t = /\s/, n = (e = e.replace(/^\s\s*/, "")).length; t.test(e.charAt(--n)););
  return e.slice(0, n + 1);
};
var save17Data = function save17Data(e, t) {
    var n = sessionStorage.getItem("cut17Data_" + e).replace(/[\r\n]/g, "").replace(/\&nbsp\;/g, "").replace(/[\s]+/g, " ");
    ajax("/testbank/TBQuestion/saveBigQuestionStructure", {
      testBankId: e,
      qJson: encodeURIComponent(n)
    }, !0, "POST", "json", "raw").done(function (e) {
      1 == e.code ? t() : layer.msg(e.msg);
    });
  },
  get17Data = function get17Data(t) {
    ajax("/testbank/TBQuestion/getTestBankQuestionInfo", {
      testBankId: t
    }).done(function (e) {
      1 == e.code && sessionStorage.setItem("cut17Data_" + t, JSON.stringify(e.data));
    });
  },
  kklPreviewDialog = function kklPreviewDialog(e) {
    var t = "https://homeworkservice.iclass30.com/",
      e = (getQueryString("url").match("test") && !getQueryString("url").match("xwtest") && (t = "https://test.iclass30.com/"), "../cutTestPaper/preview.html?testBankId=" + e + "&token=" + getQueryString("token") + "&url=" + t);
    layer.open({
      type: 2,
      title: "学生端预览",
      area: ["600px", "550px"],
      closeBtn: 1,
      fix: !0,
      shade: .3,
      scrollbar: !0,
      resize: !0,
      moveOut: !1,
      content: e
    });
  },
  closeWebPage = function closeWebPage() {
    if (0 < navigator.userAgent.indexOf("MSIE")) (0 < navigator.userAgent.indexOf("MSIE 6.0") ? (window.opener = null, window) : (window.open("", "_top"), window.top)).close();else if (0 < navigator.userAgent.indexOf("Chrome")) try {
      window.location.href = "about:blank", window.close();
    } catch (e) {
      window.opener = null, window.open("", "_self"), window.close();
    } else 0 < navigator.userAgent.indexOf("Firefox") ? window.location.href = "about:blank " : (window.opener = null, window.open("", "_self", ""), window.close());
  },
  finishMakeQuestion = function finishMakeQuestion(e) {
    save17Data(e, function () {
      ajax("/testbank/testBank/overMakeBank", {
        bankid: e
      }).done(function (e) {
        1 == e.code ? 0 < navigator.userAgent.indexOf("Datedu") ? $(".makePaper-div .title-back", window.parent.document).trigger("click") : closeWebPage() : -1 == e.code && layer.msg("保存制题失败!");
      }).fail(function () {
        layer.msg("保存制题失败,请检查网络!");
      });
    });
  },
  PostMessageToParent = function PostMessageToParent(e) {
    window.parent.postMessage(e, "*");
  },
  isNoAutoSubj = function isNoAutoSubj(e) {
    return "12" == e || "3" == e || "1" == e || "10" == e;
  };
function isMQKeyBoard(e) {
  return -1 < $.inArray(e, ["2", "4", "5", "6", "9", "11", "13", "14", "15", "18", "23"]);
}
String.prototype.Trim = function (e) {
  if (null == e || "" == e) for (var t = /s/, n = (a = this.replace(/^s*/, "")).length; t.test(a.charAt(--n)););else for (var t = new RegExp("^" + e + "*"), a = this.replace(t, ""), n = (t = new RegExp(e), a.length); t.test(a.charAt(--n)););
  return a.slice(0, n + 1);
}, String.prototype.TrimStart = function (e) {
  return null == e || "" == e ? this.replace(/^s*/, "") : (e = new RegExp("^" + e + "*"), this.replace(e, ""));
}, String.prototype.trimEnd = function (e) {
  if (null == e || "" == e) for (var t = /s/, n = (a = this).length; t.test(a.charAt(--n)););else for (var a = this, t = new RegExp(e), n = a.length; t.test(a.charAt(--n)););
  return a.slice(0, n + 1);
};
