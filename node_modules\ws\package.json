{"_from": "ws@^3.2.0", "_id": "ws@3.3.3", "_inBundle": false, "_integrity": "sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==", "_location": "/ws", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ws@^3.2.0", "name": "ws", "escapedName": "ws", "rawSpec": "^3.2.0", "saveSpec": null, "fetchSpec": "^3.2.0"}, "_requiredBy": ["/websocket-stream"], "_resolved": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "_shasum": "f1cf84fe2d5e901ebce94efaece785f187a228f2", "_spec": "ws@^3.2.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\websocket-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "bugs": {"url": "https://github.com/websockets/ws/issues"}, "bundleDependencies": false, "dependencies": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}, "deprecated": false, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "devDependencies": {"benchmark": "~2.1.2", "bufferutil": "~3.0.0", "eslint": "~4.13.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-import": "~2.8.0", "eslint-plugin-node": "~5.2.0", "eslint-plugin-promise": "~3.6.0", "eslint-plugin-standard": "~3.0.0", "mocha": "~4.0.0", "nyc": "~11.3.0", "utf-8-validate": "~4.0.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "license": "MIT", "main": "index.js", "name": "ws", "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "scripts": {"integration": "eslint . && mocha test/*.integration.js", "lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js"}, "version": "3.3.3"}