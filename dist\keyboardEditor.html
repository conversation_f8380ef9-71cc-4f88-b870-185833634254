<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><link rel="stylesheet" href="js/thirdLib/keyboard/mathquill.css"><link rel="stylesheet" href="js/thirdLib/keyboard/keyboard.css"><style>body,html{width:100%;height:100%;margin:0}.btn_box input.open{width:55px}.btn_box input.open.isOpened{width:120px}.blue-triangle{border:8px solid transparent;border-top-color:#5c83ff;left:-52px}</style></head><body></body></html><script src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script src="js/thirdLib/keyboard/mathquill.js"></script><script src="js/thirdLib/keyboard/keyboard.js"></script><script type="text/javascript">$(function(){function e(e){e=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),e=window.location.search.substr(1).match(e);return null!=e?e[2]:null}var n=e("answerVal"),n=("undefined"==n&&(n={}),{css:{width:"100%",height:"100%",top:"0",left:"0"},data:{title:111,answer:JSON.parse(decodeURIComponent(n)),bgImgUrl:"./js/thirdLib/keyboard/img/"},isMQ:(n=e("subject"),-1<$.inArray(n,["2","4","5","6","9","11","13","14","15","18","23"])),showTitle:!1,type:1}),t=e("key"),i=e("empty"),r=e("number");new MQkeyBoard($("body"),n,function(e){parent.layer.closeAll("iframe"),parent.CT_TopicPreview.answerInputFinish(t,i,e,r)})})</script>