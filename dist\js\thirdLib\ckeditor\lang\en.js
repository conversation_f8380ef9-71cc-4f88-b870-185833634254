"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license/
*/
CKEDITOR.lang['en'] = {
  "application": "Rich Text Editor",
  "editor": "Editor",
  "editorPanel": "Rich Text Editor panel",
  "common": {
    "editorHelp": "Press ALT 0 for help",
    "browseServer": "Browse Server",
    "url": "URL",
    "protocol": "Protocol",
    "upload": "Upload",
    "uploadSubmit": "Send it to the Server",
    "image": "Image",
    "form": "Form",
    "checkbox": "Checkbox",
    "radio": "Radio Button",
    "textField": "Text Field",
    "textarea": "Textarea",
    "hiddenField": "Hidden Field",
    "button": "Button",
    "select": "Selection Field",
    "imageButton": "Image Button",
    "notSet": "<not set>",
    "id": "Id",
    "name": "Name",
    "langDir": "Language Direction",
    "langDirLtr": "Left to Right (LTR)",
    "langDirRtl": "Right to Left (RTL)",
    "langCode": "Language Code",
    "longDescr": "Long Description URL",
    "cssClass": "Stylesheet Classes",
    "advisoryTitle": "Advisory Title",
    "cssStyle": "Style",
    "ok": "OK",
    "cancel": "Cancel",
    "close": "Close",
    "preview": "Preview",
    "resize": "Resize",
    "generalTab": "General",
    "advancedTab": "Advanced",
    "validateNumberFailed": "This value is not a number.",
    "confirmNewPage": "Any unsaved changes to this content will be lost. Are you sure you want to load new page?",
    "confirmCancel": "You have changed some options. Are you sure you want to close the dialog window?",
    "options": "Options",
    "target": "Target",
    "targetNew": "New Window (_blank)",
    "targetTop": "Topmost Window (_top)",
    "targetSelf": "Same Window (_self)",
    "targetParent": "Parent Window (_parent)",
    "langDirLTR": "Left to Right (LTR)",
    "langDirRTL": "Right to Left (RTL)",
    "styles": "Style",
    "cssClasses": "Stylesheet Classes",
    "width": "Width",
    "height": "Height",
    "align": "Alignment",
    "left": "Left",
    "right": "Right",
    "center": "Center",
    "justify": "Justify",
    "alignLeft": "Align Left",
    "alignRight": "Align Right",
    "alignCenter": "Align Center",
    "alignTop": "Top",
    "alignMiddle": "Middle",
    "alignBottom": "Bottom",
    "alignNone": "None",
    "invalidValue": "Invalid value.",
    "invalidHeight": "Height must be a number.",
    "invalidWidth": "Width must be a number.",
    "invalidLength": "Value specified for the \"%1\" field must be a positive number with or without a valid measurement unit (%2).",
    "invalidCssLength": "Value specified for the \"%1\" field must be a positive number with or without a valid CSS measurement unit (px, %, in, cm, mm, em, ex, pt, or pc).",
    "invalidHtmlLength": "Value specified for the \"%1\" field must be a positive number with or without a valid HTML measurement unit (px or %).",
    "invalidInlineStyle": "Value specified for the inline style must consist of one or more tuples with the format of \"name : value\", separated by semi-colons.",
    "cssLengthTooltip": "Enter a number for a value in pixels or a number with a valid CSS unit (px, %, in, cm, mm, em, ex, pt, or pc).",
    "unavailable": "%1<span class=\"cke_accessibility\">, unavailable</span>",
    "keyboard": {
      "8": "Backspace",
      "13": "Enter",
      "16": "Shift",
      "17": "Ctrl",
      "18": "Alt",
      "32": "Space",
      "35": "End",
      "36": "Home",
      "46": "Delete",
      "112": "F1",
      "113": "F2",
      "114": "F3",
      "115": "F4",
      "116": "F5",
      "117": "F6",
      "118": "F7",
      "119": "F8",
      "120": "F9",
      "121": "F10",
      "122": "F11",
      "123": "F12",
      "124": "F13",
      "125": "F14",
      "126": "F15",
      "127": "F16",
      "128": "F17",
      "129": "F18",
      "130": "F19",
      "131": "F20",
      "132": "F21",
      "133": "F22",
      "134": "F23",
      "135": "F24",
      "224": "Command"
    },
    "keyboardShortcut": "Keyboard shortcut",
    "optionDefault": "Default"
  },
  "versionCheck": {
    "notificationMessage": "This CKEditor %current version is not secure. Consider <a target=\"_blank\" href=\"%link\">upgrading to the latest one</a>, %latest.",
    "consoleMessage": "This CKEditor %current version is not secure. Consider upgrading to the latest one, %latest: %link",
    "aboutDialogInsecureMessage": "This CKEditor %current version is not secure.<br>Consider upgrading to the latest one, %latest:<br><a target=\"_blank\" href=\"%link\">%link</a>",
    "aboutDialogUpgradeMessage": "Consider upgrading to the latest editor version, %latest:<br><a target=\"_blank\" href=\"%link\">%link</a>"
  },
  "about": {
    "copy": "Copyright &copy; $1. All rights reserved.",
    "dlgTitle": "About CKEditor 4",
    "moreInfo": "For licensing information please visit our web site:"
  },
  "basicstyles": {
    "bold": "Bold",
    "italic": "Italic",
    "strike": "Strikethrough",
    "subscript": "Subscript",
    "superscript": "Superscript",
    "underline": "Underline"
  },
  "bidi": {
    "ltr": "Text direction from left to right",
    "rtl": "Text direction from right to left"
  },
  "blockquote": {
    "toolbar": "Block Quote"
  },
  "notification": {
    "closed": "Notification closed."
  },
  "toolbar": {
    "toolbarCollapse": "Collapse Toolbar",
    "toolbarExpand": "Expand Toolbar",
    "toolbarGroups": {
      "document": "Document",
      "clipboard": "Clipboard/Undo",
      "editing": "Editing",
      "forms": "Forms",
      "basicstyles": "Basic Styles",
      "paragraph": "Paragraph",
      "links": "Links",
      "insert": "Insert",
      "styles": "Styles",
      "colors": "Colors",
      "tools": "Tools"
    },
    "toolbars": "Editor toolbars"
  },
  "clipboard": {
    "copy": "Copy",
    "copyError": "Your browser security settings don't permit the editor to automatically execute copying operations. Please use the keyboard for that (Ctrl/Cmd+C).",
    "cut": "Cut",
    "cutError": "Your browser security settings don't permit the editor to automatically execute cutting operations. Please use the keyboard for that (Ctrl/Cmd+X).",
    "paste": "Paste",
    "pasteNotification": "Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.",
    "pasteArea": "Paste Area",
    "pasteMsg": "Paste your content inside the area below and press OK.",
    "fileFormatNotSupportedNotification": "The ${formats} file format(s) are not supported.",
    "fileWithoutFormatNotSupportedNotification": "The file format is not supported."
  },
  "colorbutton": {
    "auto": "Automatic",
    "bgColorTitle": "Background Color",
    "colors": {
      "000": "Black",
      "800000": "Maroon",
      "8B4513": "Saddle Brown",
      "2F4F4F": "Dark Slate Gray",
      "008080": "Teal",
      "000080": "Navy",
      "4B0082": "Indigo",
      "696969": "Dark Gray",
      "B22222": "Fire Brick",
      "A52A2A": "Brown",
      "DAA520": "Golden Rod",
      "006400": "Dark Green",
      "40E0D0": "Turquoise",
      "0000CD": "Medium Blue",
      "800080": "Purple",
      "808080": "Gray",
      "F00": "Red",
      "FF8C00": "Dark Orange",
      "FFD700": "Gold",
      "008000": "Green",
      "0FF": "Cyan",
      "00F": "Blue",
      "EE82EE": "Violet",
      "A9A9A9": "Dim Gray",
      "FFA07A": "Light Salmon",
      "FFA500": "Orange",
      "FFFF00": "Yellow",
      "00FF00": "Lime",
      "AFEEEE": "Pale Turquoise",
      "ADD8E6": "Light Blue",
      "DDA0DD": "Plum",
      "D3D3D3": "Light Grey",
      "FFF0F5": "Lavender Blush",
      "FAEBD7": "Antique White",
      "FFFFE0": "Light Yellow",
      "F0FFF0": "Honeydew",
      "F0FFFF": "Azure",
      "F0F8FF": "Alice Blue",
      "E6E6FA": "Lavender",
      "FFF": "White",
      "1ABC9C": "Strong Cyan",
      "2ECC71": "Emerald",
      "3498DB": "Bright Blue",
      "9B59B6": "Amethyst",
      "4E5F70": "Grayish Blue",
      "F1C40F": "Vivid Yellow",
      "16A085": "Dark Cyan",
      "27AE60": "Dark Emerald",
      "2980B9": "Strong Blue",
      "8E44AD": "Dark Violet",
      "2C3E50": "Desaturated Blue",
      "F39C12": "Orange",
      "E67E22": "Carrot",
      "E74C3C": "Pale Red",
      "ECF0F1": "Bright Silver",
      "95A5A6": "Light Grayish Cyan",
      "DDD": "Light Gray",
      "D35400": "Pumpkin",
      "C0392B": "Strong Red",
      "BDC3C7": "Silver",
      "7F8C8D": "Grayish Cyan",
      "999": "Dark Gray"
    },
    "more": "More Colors...",
    "panelTitle": "Colors",
    "textColorTitle": "Text Color"
  },
  "colordialog": {
    "clear": "Clear",
    "highlight": "Highlight",
    "options": "Color Options",
    "selected": "Selected Color",
    "title": "Select color"
  },
  "templates": {
    "button": "Templates",
    "emptyListMsg": "(No templates defined)",
    "insertOption": "Replace actual contents",
    "options": "Template Options",
    "selectPromptMsg": "Please select the template to open in the editor",
    "title": "Content Templates"
  },
  "contextmenu": {
    "options": "Context Menu Options"
  },
  "copyformatting": {
    "label": "Copy Formatting",
    "notification": {
      "copied": "Formatting copied",
      "applied": "Formatting applied",
      "canceled": "Formatting canceled",
      "failed": "Formatting failed. You cannot apply styles without copying them first."
    }
  },
  "div": {
    "IdInputLabel": "Id",
    "advisoryTitleInputLabel": "Advisory Title",
    "cssClassInputLabel": "Stylesheet Classes",
    "edit": "Edit Div",
    "inlineStyleInputLabel": "Inline Style",
    "langDirLTRLabel": "Left to Right (LTR)",
    "langDirLabel": "Language Direction",
    "langDirRTLLabel": "Right to Left (RTL)",
    "languageCodeInputLabel": " Language Code",
    "remove": "Remove Div",
    "styleSelectLabel": "Style",
    "title": "Create Div Container",
    "toolbar": "Create Div Container"
  },
  "elementspath": {
    "eleLabel": "Elements path",
    "eleTitle": "%1 element"
  },
  "exportpdf": {
    "documentReady": "Document is ready!",
    "error": "Error occurred.",
    "processingDocument": "Processing PDF document...",
    "toolbar": "Export to PDF"
  },
  "filetools": {
    "loadError": "Error occurred during file read.",
    "networkError": "Network error occurred during file upload.",
    "httpError404": "HTTP error occurred during file upload (404: File not found).",
    "httpError403": "HTTP error occurred during file upload (403: Forbidden).",
    "httpError": "HTTP error occurred during file upload (error status: %1).",
    "noUrlError": "Upload URL is not defined.",
    "responseError": "Incorrect server response."
  },
  "find": {
    "find": "Find",
    "findOptions": "Find Options",
    "findWhat": "Find what:",
    "matchCase": "Match case",
    "matchCyclic": "Match cyclic",
    "matchWord": "Match whole word",
    "notFoundMsg": "The specified text was not found.",
    "replace": "Replace",
    "replaceAll": "Replace All",
    "replaceSuccessMsg": "%1 occurrence(s) replaced.",
    "replaceWith": "Replace with:",
    "title": "Find and Replace"
  },
  "font": {
    "fontSize": {
      "label": "Size",
      "voiceLabel": "Font Size",
      "panelTitle": "Font Size"
    },
    "label": "Font",
    "panelTitle": "Font Name",
    "voiceLabel": "Font"
  },
  "fakeobjects": {
    "anchor": "Anchor",
    "hiddenfield": "Hidden Field",
    "iframe": "IFrame",
    "unknown": "Unknown Object"
  },
  "forms": {
    "button": {
      "title": "Button Properties",
      "text": "Text (Value)",
      "type": "Type",
      "typeBtn": "Button",
      "typeSbm": "Submit",
      "typeRst": "Reset"
    },
    "checkboxAndRadio": {
      "checkboxTitle": "Checkbox Properties",
      "radioTitle": "Radio Button Properties",
      "value": "Value",
      "selected": "Selected",
      "required": "Required"
    },
    "form": {
      "title": "Form Properties",
      "menu": "Form Properties",
      "action": "Action",
      "method": "Method",
      "encoding": "Encoding"
    },
    "hidden": {
      "title": "Hidden Field Properties",
      "name": "Name",
      "value": "Value"
    },
    "select": {
      "title": "Selection Field Properties",
      "selectInfo": "Select Info",
      "opAvail": "Available Options",
      "value": "Value",
      "size": "Size",
      "lines": "lines",
      "chkMulti": "Allow multiple selections",
      "required": "Required",
      "opText": "Text",
      "opValue": "Value",
      "btnAdd": "Add",
      "btnModify": "Modify",
      "btnUp": "Up",
      "btnDown": "Down",
      "btnSetValue": "Set as selected value",
      "btnDelete": "Delete"
    },
    "textarea": {
      "title": "Textarea Properties",
      "cols": "Columns",
      "rows": "Rows"
    },
    "textfield": {
      "title": "Text Field Properties",
      "name": "Name",
      "value": "Value",
      "charWidth": "Character Width",
      "maxChars": "Maximum Characters",
      "required": "Required",
      "type": "Type",
      "typeText": "Text",
      "typePass": "Password",
      "typeEmail": "Email",
      "typeSearch": "Search",
      "typeTel": "Telephone Number",
      "typeUrl": "URL"
    }
  },
  "format": {
    "label": "Format",
    "panelTitle": "Paragraph Format",
    "tag_address": "Address",
    "tag_div": "Normal (DIV)",
    "tag_h1": "Heading 1",
    "tag_h2": "Heading 2",
    "tag_h3": "Heading 3",
    "tag_h4": "Heading 4",
    "tag_h5": "Heading 5",
    "tag_h6": "Heading 6",
    "tag_p": "Normal",
    "tag_pre": "Formatted"
  },
  "horizontalrule": {
    "toolbar": "Insert Horizontal Line"
  },
  "iframe": {
    "border": "Show frame border",
    "noUrl": "Please type the iframe URL",
    "scrolling": "Enable scrollbars",
    "title": "IFrame Properties",
    "toolbar": "IFrame",
    "tabindex": "Remove from tabindex"
  },
  "image": {
    "alt": "Alternative Text",
    "border": "Border",
    "btnUpload": "Send it to the Server",
    "button2Img": "Do you want to transform the selected image button on a simple image?",
    "hSpace": "HSpace",
    "img2Button": "Do you want to transform the selected image on a image button?",
    "infoTab": "Image Info",
    "linkTab": "Link",
    "lockRatio": "Lock Ratio",
    "menu": "Image Properties",
    "resetSize": "Reset Size",
    "title": "Image Properties",
    "titleButton": "Image Button Properties",
    "upload": "Upload",
    "urlMissing": "Image source URL is missing.",
    "vSpace": "VSpace",
    "validateBorder": "Border must be a whole number.",
    "validateHSpace": "HSpace must be a whole number.",
    "validateVSpace": "VSpace must be a whole number."
  },
  "indent": {
    "indent": "Increase Indent",
    "outdent": "Decrease Indent"
  },
  "smiley": {
    "options": "Smiley Options",
    "title": "Insert a Smiley",
    "toolbar": "Smiley"
  },
  "language": {
    "button": "Set language",
    "remove": "Remove language"
  },
  "link": {
    "acccessKey": "Access Key",
    "advanced": "Advanced",
    "advisoryContentType": "Advisory Content Type",
    "advisoryTitle": "Advisory Title",
    "anchor": {
      "toolbar": "Anchor",
      "menu": "Edit Anchor",
      "title": "Anchor Properties",
      "name": "Anchor Name",
      "errorName": "Please type the anchor name",
      "errorWhitespace": "Anchor name cannot contain space characters",
      "remove": "Remove Anchor"
    },
    "anchorId": "By Element Id",
    "anchorName": "By Anchor Name",
    "charset": "Linked Resource Charset",
    "cssClasses": "Stylesheet Classes",
    "download": "Force Download",
    "displayText": "Display Text",
    "emailAddress": "E-Mail Address",
    "emailBody": "Message Body",
    "emailSubject": "Message Subject",
    "id": "Id",
    "info": "Link Info",
    "langCode": "Language Code",
    "langDir": "Language Direction",
    "langDirLTR": "Left to Right (LTR)",
    "langDirRTL": "Right to Left (RTL)",
    "menu": "Edit Link",
    "name": "Name",
    "noAnchors": "(No anchors available in the document)",
    "noEmail": "Please type the e-mail address",
    "noUrl": "Please type the link URL",
    "noTel": "Please type the phone number",
    "other": "<other>",
    "phoneNumber": "Phone number",
    "popupDependent": "Dependent (Netscape)",
    "popupFeatures": "Popup Window Features",
    "popupFullScreen": "Full Screen (IE)",
    "popupLeft": "Left Position",
    "popupLocationBar": "Location Bar",
    "popupMenuBar": "Menu Bar",
    "popupResizable": "Resizable",
    "popupScrollBars": "Scroll Bars",
    "popupStatusBar": "Status Bar",
    "popupToolbar": "Toolbar",
    "popupTop": "Top Position",
    "rel": "Relationship",
    "selectAnchor": "Select an Anchor",
    "styles": "Style",
    "tabIndex": "Tab Index",
    "target": "Target",
    "targetFrame": "<frame>",
    "targetFrameName": "Target Frame Name",
    "targetPopup": "<popup window>",
    "targetPopupName": "Popup Window Name",
    "title": "Link",
    "toAnchor": "Link to anchor in the text",
    "toEmail": "E-mail",
    "toUrl": "URL",
    "toPhone": "Phone",
    "toolbar": "Link",
    "type": "Link Type",
    "unlink": "Unlink",
    "upload": "Upload"
  },
  "list": {
    "bulletedlist": "Insert/Remove Bulleted List",
    "numberedlist": "Insert/Remove Numbered List"
  },
  "liststyle": {
    "bulletedTitle": "Bulleted List Properties",
    "circle": "Circle",
    "decimal": "Decimal (1, 2, 3, etc.)",
    "disc": "Disc",
    "lowerAlpha": "Lower Alpha (a, b, c, d, e, etc.)",
    "lowerRoman": "Lower Roman (i, ii, iii, iv, v, etc.)",
    "none": "None",
    "notset": "<not set>",
    "numberedTitle": "Numbered List Properties",
    "square": "Square",
    "start": "Start",
    "type": "Type",
    "upperAlpha": "Upper Alpha (A, B, C, D, E, etc.)",
    "upperRoman": "Upper Roman (I, II, III, IV, V, etc.)",
    "validateStartNumber": "List start number must be a whole number."
  },
  "magicline": {
    "title": "Insert paragraph here"
  },
  "maximize": {
    "maximize": "Maximize",
    "minimize": "Minimize"
  },
  "newpage": {
    "toolbar": "New Page"
  },
  "pagebreak": {
    "alt": "Page Break",
    "toolbar": "Insert Page Break for Printing"
  },
  "pastetext": {
    "button": "Paste as plain text",
    "pasteNotification": "Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.",
    "title": "Paste as Plain Text"
  },
  "pastefromword": {
    "confirmCleanup": "The text you want to paste seems to be copied from Word. Do you want to clean it before pasting?",
    "error": "It was not possible to clean up the pasted data due to an internal error",
    "title": "Paste from Word",
    "toolbar": "Paste from Word"
  },
  "preview": {
    "preview": "Preview"
  },
  "print": {
    "toolbar": "Print"
  },
  "removeformat": {
    "toolbar": "Remove Format"
  },
  "save": {
    "toolbar": "Save"
  },
  "selectall": {
    "toolbar": "Select All"
  },
  "showblocks": {
    "toolbar": "Show Blocks"
  },
  "sourcearea": {
    "toolbar": "Source"
  },
  "specialchar": {
    "options": "Special Character Options",
    "title": "Select Special Character",
    "toolbar": "Insert Special Character"
  },
  "scayt": {
    "btn_about": "About SCAYT",
    "btn_dictionaries": "Dictionaries",
    "btn_disable": "Disable SCAYT",
    "btn_enable": "Enable SCAYT",
    "btn_langs": "Languages",
    "btn_options": "Options",
    "text_title": "Spell Check As You Type"
  },
  "stylescombo": {
    "label": "Styles",
    "panelTitle": "Formatting Styles",
    "panelTitle1": "Block Styles",
    "panelTitle2": "Inline Styles",
    "panelTitle3": "Object Styles"
  },
  "table": {
    "border": "Border size",
    "caption": "Caption",
    "cell": {
      "menu": "Cell",
      "insertBefore": "Insert Cell Before",
      "insertAfter": "Insert Cell After",
      "deleteCell": "Delete Cells",
      "merge": "Merge Cells",
      "mergeRight": "Merge Right",
      "mergeDown": "Merge Down",
      "splitHorizontal": "Split Cell Horizontally",
      "splitVertical": "Split Cell Vertically",
      "title": "Cell Properties",
      "cellType": "Cell Type",
      "rowSpan": "Rows Span",
      "colSpan": "Columns Span",
      "wordWrap": "Word Wrap",
      "hAlign": "Horizontal Alignment",
      "vAlign": "Vertical Alignment",
      "alignBaseline": "Baseline",
      "bgColor": "Background Color",
      "borderColor": "Border Color",
      "data": "Data",
      "header": "Header",
      "columnHeader": "Column Header",
      "rowHeader": "Row Header",
      "yes": "Yes",
      "no": "No",
      "invalidWidth": "Cell width must be a number.",
      "invalidHeight": "Cell height must be a number.",
      "invalidRowSpan": "Rows span must be a whole number.",
      "invalidColSpan": "Columns span must be a whole number.",
      "chooseColor": "Choose"
    },
    "cellPad": "Cell padding",
    "cellSpace": "Cell spacing",
    "column": {
      "menu": "Column",
      "insertBefore": "Insert Column Before",
      "insertAfter": "Insert Column After",
      "deleteColumn": "Delete Columns"
    },
    "columns": "Columns",
    "deleteTable": "Delete Table",
    "headers": "Headers",
    "headersBoth": "Both",
    "headersColumn": "First column",
    "headersNone": "None",
    "headersRow": "First Row",
    "heightUnit": "height unit",
    "invalidBorder": "Border size must be a number.",
    "invalidCellPadding": "Cell padding must be a positive number.",
    "invalidCellSpacing": "Cell spacing must be a positive number.",
    "invalidCols": "Number of columns must be a number greater than 0.",
    "invalidHeight": "Table height must be a number.",
    "invalidRows": "Number of rows must be a number greater than 0.",
    "invalidWidth": "Table width must be a number.",
    "menu": "Table Properties",
    "row": {
      "menu": "Row",
      "insertBefore": "Insert Row Before",
      "insertAfter": "Insert Row After",
      "deleteRow": "Delete Rows"
    },
    "rows": "Rows",
    "summary": "Summary",
    "title": "Table Properties",
    "toolbar": "Table",
    "widthPc": "percent",
    "widthPx": "pixels",
    "widthUnit": "width unit"
  },
  "undo": {
    "redo": "Redo",
    "undo": "Undo"
  },
  "widget": {
    "move": "Click and drag to move",
    "label": "%1 widget"
  },
  "uploadwidget": {
    "abort": "Upload aborted by the user.",
    "doneOne": "File successfully uploaded.",
    "doneMany": "Successfully uploaded %1 files.",
    "uploadOne": "Uploading file ({percentage}%)...",
    "uploadMany": "Uploading files, {current} of {max} done ({percentage}%)..."
  }
};
