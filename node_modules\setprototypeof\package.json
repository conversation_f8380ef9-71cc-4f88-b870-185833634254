{"_from": "setprototypeof@1.1.0", "_id": "setprototypeof@1.1.0", "_inBundle": false, "_integrity": "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==", "_location": "/setprototypeof", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "setprototypeof@1.1.0", "name": "setprot<PERSON>of", "escapedName": "setprot<PERSON>of", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/http-errors"], "_resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "_shasum": "d0bd85536887b6fe7c0d818cb962d9d91c54e656", "_spec": "setprototypeof@1.1.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\http-errors", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/wesleytodd/setprototypeof/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A small polyfill for Object.setprototypeof", "homepage": "https://github.com/wesleytodd/setprototypeof", "keywords": ["polyfill", "object", "setprot<PERSON>of"], "license": "ISC", "main": "index.js", "name": "setprot<PERSON>of", "repository": {"type": "git", "url": "git+https://github.com/wesleytodd/setprototypeof.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "typings": "index.d.ts", "version": "1.1.0"}