"use strict";

/**
 * ztree封装
 *
 * @type {{config: tree.config, addDiyDom: tree.addDiyDom, onClickTreeNode: tree.onClickTreeNode, init: tree.init, loadtree: tree.loadtree, onClickTreeNodeSuccess: tree.onClickTreeNodeSuccess}}
 */
var tree = {
  //Tree配置区
  config: function config(params) {
    setting = $.extend(true, {}, {
      view: {
        dblClickExpand: false,
        ////双击节点是否展开
        showLine: false,
        //是否展示节点间的连线
        nameIsHTML: true,
        //设置name属性支持脚本
        selectedMulti: false,
        //禁止多选
        //expandSpeed: "slow", //慢速动画效果
        //showIcon: true, //是否展示图标
        showTitle: false,
        //是否展示title
        addDiyDom: tree.addDiyDom //节点后增加按钮
      },
      data: {
        simpleData: {
          enable: true //如果数据为Json形式需要将其配为 true 并数据中需要有子父级关系 默认为：id/pId 形式
        }
      },
      callback: {
        //节点点击事件配置
        onClick: function onClick(event, treeId, treeNode) {
          tree.onClickTreeNode(event, treeId, treeNode);
        },
        onDblClick: function onDblClick(event, treeId, treeNode) {
          tree.onDblClick(treeNode);
        }
      }
    }, params);
  },
  //节点后增加按钮样式等
  addDiyDom: function addDiyDom(treeId, treeNode) {
    //调用时需要重写一个自己的函数 不重写则节点后无按钮
  },
  //点击树节点
  onClickTreeNode: function onClickTreeNode(event, treeId, treeNode) {
    tree.onClickTreeNodeSuccess(treeNode);
  },
  //Tree初始化配置
  init: function init(nId, params) {
    tree.config(params); //加载配置
    nodeId = nId; //赋值节点Id 可以用来默认选中树节点
  },
  //Tree加载函数
  loadtree: function loadtree(nodes) {
    $.fn.zTree.init($("#tree"), setting, nodes); //加载树
    //nodeId不为空 说明需要树默认选中
    if (nodeId != "") {
      var zTree = $.fn.zTree.getZTreeObj("tree"); //获取树对象
      var node = zTree.getNodeByParam("id", nodeId); //根据树节点获取节点对象
      zTree.selectNode(node); //展示默认选择的树
    }
  },
  //成功点击树节点
  onClickTreeNodeSuccess: function onClickTreeNodeSuccess(treeNode) {
    //调用时需要重写一个自己的函数
  },
  onDblClick: function onDblClick(treeNode) {}
};
