"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
!function (_$) {
  var settings = {},
    roots = {},
    caches = {},
    _consts = {
      className: {
        BUTTON: "button",
        LEVEL: "level",
        ICO_LOADING: "ico_loading",
        SWITCH: "switch",
        NAME: "node_name"
      },
      event: {
        NODECREATED: "ztree_nodeCreated",
        CLICK: "ztree_click",
        EXPAND: "ztree_expand",
        COLLAPSE: "ztree_collapse",
        ASYNC_SUCCESS: "ztree_async_success",
        ASYNC_ERROR: "ztree_async_error",
        REMOVE: "ztree_remove",
        SELECTED: "ztree_selected",
        UNSELECTED: "ztree_unselected"
      },
      id: {
        A: "_a",
        ICON: "_ico",
        SPAN: "_span",
        SWITCH: "_switch",
        UL: "_ul"
      },
      line: {
        ROOT: "root",
        ROOTS: "roots",
        CENTER: "center",
        BOTTOM: "bottom",
        NOLINE: "noline",
        LINE: "line"
      },
      folder: {
        OPEN: "open",
        CLOSE: "close",
        DOCU: "docu"
      },
      node: {
        CURSELECTED: "curSelectedNode"
      }
    },
    _setting = {
      treeId: "",
      treeObj: null,
      view: {
        addDiyDom: null,
        autoCancelSelected: !0,
        dblClickExpand: !0,
        expandSpeed: "fast",
        fontCss: {},
        nodeClasses: {},
        nameIsHTML: !1,
        selectedMulti: !0,
        showIcon: !0,
        showLine: !0,
        showTitle: !0,
        txtSelectedEnable: !1
      },
      data: {
        key: {
          isParent: "isParent",
          children: "children",
          name: "name",
          title: "",
          url: "url",
          icon: "icon"
        },
        render: {
          name: null,
          title: null
        },
        simpleData: {
          enable: !1,
          idKey: "id",
          pIdKey: "pId",
          rootPId: null
        },
        keep: {
          parent: !1,
          leaf: !1
        }
      },
      async: {
        enable: !1,
        contentType: "application/x-www-form-urlencoded",
        type: "post",
        dataType: "text",
        headers: {},
        xhrFields: {},
        url: "",
        autoParam: [],
        otherParam: [],
        dataFilter: null
      },
      callback: {
        beforeAsync: null,
        beforeClick: null,
        beforeDblClick: null,
        beforeRightClick: null,
        beforeMouseDown: null,
        beforeMouseUp: null,
        beforeExpand: null,
        beforeCollapse: null,
        beforeRemove: null,
        onAsyncError: null,
        onAsyncSuccess: null,
        onNodeCreated: null,
        onClick: null,
        onDblClick: null,
        onRightClick: null,
        onMouseDown: null,
        onMouseUp: null,
        onExpand: null,
        onCollapse: null,
        onRemove: null
      }
    },
    _initRoot = function _initRoot(e) {
      var t = data.getRoot(e);
      t || (t = {}, data.setRoot(e, t)), data.nodeChildren(e, t, []), t.expandTriggerFlag = !1, t.curSelectedList = [], t.noSelection = !0, t.createdNodes = [], t.zId = 0, t._ver = new Date().getTime();
    },
    _initCache = function _initCache(e) {
      var t = data.getCache(e);
      t || (t = {}, data.setCache(e, t)), t.nodes = [], t.doms = [];
    },
    _bindEvent = function _bindEvent(d) {
      var e = d.treeObj,
        t = consts.event;
      e.bind(t.NODECREATED, function (e, t, n) {
        tools.apply(d.callback.onNodeCreated, [e, t, n]);
      }), e.bind(t.CLICK, function (e, t, n, o, a) {
        tools.apply(d.callback.onClick, [t, n, o, a]);
      }), e.bind(t.EXPAND, function (e, t, n) {
        tools.apply(d.callback.onExpand, [e, t, n]);
      }), e.bind(t.COLLAPSE, function (e, t, n) {
        tools.apply(d.callback.onCollapse, [e, t, n]);
      }), e.bind(t.ASYNC_SUCCESS, function (e, t, n, o) {
        tools.apply(d.callback.onAsyncSuccess, [e, t, n, o]);
      }), e.bind(t.ASYNC_ERROR, function (e, t, n, o, a, r) {
        tools.apply(d.callback.onAsyncError, [e, t, n, o, a, r]);
      }), e.bind(t.REMOVE, function (e, t, n) {
        tools.apply(d.callback.onRemove, [e, t, n]);
      }), e.bind(t.SELECTED, function (e, t, n) {
        tools.apply(d.callback.onSelected, [t, n]);
      }), e.bind(t.UNSELECTED, function (e, t, n) {
        tools.apply(d.callback.onUnSelected, [t, n]);
      });
    },
    _unbindEvent = function _unbindEvent(e) {
      var t = e.treeObj,
        n = consts.event;
      t.unbind(n.NODECREATED).unbind(n.CLICK).unbind(n.EXPAND).unbind(n.COLLAPSE).unbind(n.ASYNC_SUCCESS).unbind(n.ASYNC_ERROR).unbind(n.REMOVE).unbind(n.SELECTED).unbind(n.UNSELECTED);
    },
    _eventProxy = function _eventProxy(e) {
      var t = e.target,
        n = data.getSetting(e.data.treeId),
        o = "",
        a = null,
        r = "",
        d = "",
        i = null,
        s = null,
        l = null;
      if (tools.eqs(e.type, "mousedown") ? d = "mousedown" : tools.eqs(e.type, "mouseup") ? d = "mouseup" : tools.eqs(e.type, "contextmenu") ? d = "contextmenu" : tools.eqs(e.type, "click") ? tools.eqs(t.tagName, "span") && null !== t.getAttribute("treeNode" + consts.id.SWITCH) ? (o = tools.getNodeMainDom(t).id, r = "switchNode") : (l = tools.getMDom(n, t, [{
        tagName: "a",
        attrName: "treeNode" + consts.id.A
      }])) && (o = tools.getNodeMainDom(l).id, r = "clickNode") : tools.eqs(e.type, "dblclick") && (d = "dblclick", (l = tools.getMDom(n, t, [{
        tagName: "a",
        attrName: "treeNode" + consts.id.A
      }])) && (o = tools.getNodeMainDom(l).id, r = "switchNode")), 0 < d.length && 0 == o.length && (l = tools.getMDom(n, t, [{
        tagName: "a",
        attrName: "treeNode" + consts.id.A
      }])) && (o = tools.getNodeMainDom(l).id), 0 < o.length) switch (a = data.getNodeCache(n, o), r) {
        case "switchNode":
          data.nodeIsParent(n, a) && (tools.eqs(e.type, "click") || tools.eqs(e.type, "dblclick") && tools.apply(n.view.dblClickExpand, [n.treeId, a], n.view.dblClickExpand)) ? i = handler.onSwitchNode : r = "";
          break;
        case "clickNode":
          i = handler.onClickNode;
      }
      switch (d) {
        case "mousedown":
          s = handler.onZTreeMousedown;
          break;
        case "mouseup":
          s = handler.onZTreeMouseup;
          break;
        case "dblclick":
          s = handler.onZTreeDblclick;
          break;
        case "contextmenu":
          s = handler.onZTreeContextmenu;
      }
      return {
        stop: !1,
        node: a,
        nodeEventType: r,
        nodeEventCallback: i,
        treeEventType: d,
        treeEventCallback: s
      };
    },
    _initNode = function _initNode(e, t, n, o, a, r, d) {
      if (n) {
        var i = data.getRoot(e),
          s = data.nodeChildren(e, n);
        n.level = t, n.tId = e.treeId + "_" + ++i.zId, n.parentTId = o ? o.tId : null, n.open = "string" == typeof n.open ? tools.eqs(n.open, "true") : !!n.open;
        var l = data.nodeIsParent(e, n);
        tools.isArray(s) ? (data.nodeIsParent(e, n, !0), n.zAsync = !0) : (l = data.nodeIsParent(e, n, l), n.open = !(!l || e.async.enable) && n.open, n.zAsync = !l), n.isFirstNode = a, n.isLastNode = r, n.getParentNode = function () {
          return data.getNodeCache(e, n.parentTId);
        }, n.getPreNode = function () {
          return data.getPreNode(e, n);
        }, n.getNextNode = function () {
          return data.getNextNode(e, n);
        }, n.getIndex = function () {
          return data.getNodeIndex(e, n);
        }, n.getPath = function () {
          return data.getNodePath(e, n);
        }, n.isAjaxing = !1, data.fixPIdKeyValue(e, n);
      }
    },
    _init = {
      bind: [_bindEvent],
      unbind: [_unbindEvent],
      caches: [_initCache],
      nodes: [_initNode],
      proxys: [_eventProxy],
      roots: [_initRoot],
      beforeA: [],
      afterA: [],
      innerBeforeA: [],
      innerAfterA: [],
      zTreeTools: []
    },
    data = {
      addNodeCache: function addNodeCache(e, t) {
        data.getCache(e).nodes[data.getNodeCacheId(t.tId)] = t;
      },
      getNodeCacheId: function getNodeCacheId(e) {
        return e.substring(e.lastIndexOf("_") + 1);
      },
      addAfterA: function addAfterA(e) {
        _init.afterA.push(e);
      },
      addBeforeA: function addBeforeA(e) {
        _init.beforeA.push(e);
      },
      addInnerAfterA: function addInnerAfterA(e) {
        _init.innerAfterA.push(e);
      },
      addInnerBeforeA: function addInnerBeforeA(e) {
        _init.innerBeforeA.push(e);
      },
      addInitBind: function addInitBind(e) {
        _init.bind.push(e);
      },
      addInitUnBind: function addInitUnBind(e) {
        _init.unbind.push(e);
      },
      addInitCache: function addInitCache(e) {
        _init.caches.push(e);
      },
      addInitNode: function addInitNode(e) {
        _init.nodes.push(e);
      },
      addInitProxy: function addInitProxy(e, t) {
        t ? _init.proxys.splice(0, 0, e) : _init.proxys.push(e);
      },
      addInitRoot: function addInitRoot(e) {
        _init.roots.push(e);
      },
      addNodesData: function addNodesData(e, t, n, o) {
        var a,
          r = data.nodeChildren(e, t);
        r ? n >= r.length && (n = -1) : (r = data.nodeChildren(e, t, []), n = -1), 0 < r.length && 0 === n ? (r[0].isFirstNode = !1, view.setNodeLineIcos(e, r[0])) : 0 < r.length && n < 0 && (r[r.length - 1].isLastNode = !1, view.setNodeLineIcos(e, r[r.length - 1])), data.nodeIsParent(e, t, !0), n < 0 ? data.nodeChildren(e, t, r.concat(o)) : (a = [n, 0].concat(o), r.splice.apply(r, a));
      },
      addSelectedNode: function addSelectedNode(e, t) {
        var n = data.getRoot(e);
        data.isSelectedNode(e, t) || n.curSelectedList.push(t);
      },
      addCreatedNode: function addCreatedNode(e, t) {
        (e.callback.onNodeCreated || e.view.addDiyDom) && data.getRoot(e).createdNodes.push(t);
      },
      addZTreeTools: function addZTreeTools(e) {
        _init.zTreeTools.push(e);
      },
      exSetting: function exSetting(e) {
        _$.extend(!0, _setting, e);
      },
      fixPIdKeyValue: function fixPIdKeyValue(e, t) {
        e.data.simpleData.enable && (t[e.data.simpleData.pIdKey] = t.parentTId ? t.getParentNode()[e.data.simpleData.idKey] : e.data.simpleData.rootPId);
      },
      getAfterA: function getAfterA(e, t, n) {
        for (var o = 0, a = _init.afterA.length; o < a; o++) _init.afterA[o].apply(this, arguments);
      },
      getBeforeA: function getBeforeA(e, t, n) {
        for (var o = 0, a = _init.beforeA.length; o < a; o++) _init.beforeA[o].apply(this, arguments);
      },
      getInnerAfterA: function getInnerAfterA(e, t, n) {
        for (var o = 0, a = _init.innerAfterA.length; o < a; o++) _init.innerAfterA[o].apply(this, arguments);
      },
      getInnerBeforeA: function getInnerBeforeA(e, t, n) {
        for (var o = 0, a = _init.innerBeforeA.length; o < a; o++) _init.innerBeforeA[o].apply(this, arguments);
      },
      getCache: function getCache(e) {
        return caches[e.treeId];
      },
      getNodeIndex: function getNodeIndex(e, t) {
        if (!t) return null;
        for (var n = t.parentTId ? t.getParentNode() : data.getRoot(e), o = data.nodeChildren(e, n), a = 0, r = o.length - 1; a <= r; a++) if (o[a] === t) return a;
        return -1;
      },
      getNextNode: function getNextNode(e, t) {
        if (!t) return null;
        for (var n = t.parentTId ? t.getParentNode() : data.getRoot(e), o = data.nodeChildren(e, n), a = 0, r = o.length - 1; a <= r; a++) if (o[a] === t) return a == r ? null : o[a + 1];
        return null;
      },
      getNodeByParam: function getNodeByParam(e, t, n, o) {
        if (!t || !n) return null;
        for (var a = 0, r = t.length; a < r; a++) {
          var d = t[a];
          if (d[n] == o) return t[a];
          var i = data.nodeChildren(e, d),
            s = data.getNodeByParam(e, i, n, o);
          if (s) return s;
        }
        return null;
      },
      getNodeCache: function getNodeCache(e, t) {
        if (!t) return null;
        var n = caches[e.treeId].nodes[data.getNodeCacheId(t)];
        return n || null;
      },
      getNodePath: function getNodePath(e, t) {
        return t ? ((n = t.parentTId ? t.getParentNode().getPath() : []) && n.push(t), n) : null;
        var n;
      },
      getNodes: function getNodes(e) {
        return data.nodeChildren(e, data.getRoot(e));
      },
      getNodesByParam: function getNodesByParam(e, t, n, o) {
        if (!t || !n) return [];
        for (var a = [], r = 0, d = t.length; r < d; r++) {
          var i = t[r];
          i[n] == o && a.push(i);
          var s = data.nodeChildren(e, i);
          a = a.concat(data.getNodesByParam(e, s, n, o));
        }
        return a;
      },
      getNodesByParamFuzzy: function getNodesByParamFuzzy(e, t, n, o) {
        if (!t || !n) return [];
        var a = [];
        o = o.toLowerCase();
        for (var r = 0, d = t.length; r < d; r++) {
          var i = t[r];
          "string" == typeof i[n] && -1 < t[r][n].toLowerCase().indexOf(o) && a.push(i);
          var s = data.nodeChildren(e, i);
          a = a.concat(data.getNodesByParamFuzzy(e, s, n, o));
        }
        return a;
      },
      getNodesByFilter: function getNodesByFilter(e, t, n, o, a) {
        if (!t) return o ? null : [];
        for (var r = o ? null : [], d = 0, i = t.length; d < i; d++) {
          var s = t[d];
          if (tools.apply(n, [s, a], !1)) {
            if (o) return s;
            r.push(s);
          }
          var l = data.nodeChildren(e, s),
            c = data.getNodesByFilter(e, l, n, o, a);
          if (o && c) return c;
          r = o ? c : r.concat(c);
        }
        return r;
      },
      getPreNode: function getPreNode(e, t) {
        if (!t) return null;
        for (var n = t.parentTId ? t.getParentNode() : data.getRoot(e), o = data.nodeChildren(e, n), a = 0, r = o.length; a < r; a++) if (o[a] === t) return 0 == a ? null : o[a - 1];
        return null;
      },
      getRoot: function getRoot(e) {
        return e ? roots[e.treeId] : null;
      },
      getRoots: function getRoots() {
        return roots;
      },
      getSetting: function getSetting(e) {
        return settings[e];
      },
      getSettings: function getSettings() {
        return settings;
      },
      getZTreeTools: function getZTreeTools(e) {
        var t = this.getRoot(this.getSetting(e));
        return t ? t.treeTools : null;
      },
      initCache: function initCache(e) {
        for (var t = 0, n = _init.caches.length; t < n; t++) _init.caches[t].apply(this, arguments);
      },
      initNode: function initNode(e, t, n, o, a, r) {
        for (var d = 0, i = _init.nodes.length; d < i; d++) _init.nodes[d].apply(this, arguments);
      },
      initRoot: function initRoot(e) {
        for (var t = 0, n = _init.roots.length; t < n; t++) _init.roots[t].apply(this, arguments);
      },
      isSelectedNode: function isSelectedNode(e, t) {
        for (var n = data.getRoot(e), o = 0, a = n.curSelectedList.length; o < a; o++) if (t === n.curSelectedList[o]) return !0;
        return !1;
      },
      nodeChildren: function nodeChildren(e, t, n) {
        if (!t) return null;
        var o = e.data.key.children;
        return void 0 !== n && (t[o] = n), t[o];
      },
      nodeIsParent: function nodeIsParent(e, t, n) {
        if (!t) return !1;
        var o = e.data.key.isParent;
        return void 0 !== n ? ("string" == typeof n && (n = tools.eqs(n, "true")), n = !!n, t[o] = n) : "string" == typeof t[o] ? t[o] = tools.eqs(t[o], "true") : t[o] = !!t[o], t[o];
      },
      nodeName: function nodeName(e, t, n) {
        var o = e.data.key.name;
        void 0 !== n && (t[o] = n);
        var a = "" + t[o];
        return "function" == typeof e.data.render.name ? e.data.render.name.call(this, a, t) : a;
      },
      nodeTitle: function nodeTitle(e, t) {
        var n = "" + t["" === e.data.key.title ? e.data.key.name : e.data.key.title];
        return "function" == typeof e.data.render.title ? e.data.render.title.call(this, n, t) : n;
      },
      removeNodeCache: function removeNodeCache(e, t) {
        var n = data.nodeChildren(e, t);
        if (n) for (var o = 0, a = n.length; o < a; o++) data.removeNodeCache(e, n[o]);
        data.getCache(e).nodes[data.getNodeCacheId(t.tId)] = null;
      },
      removeSelectedNode: function removeSelectedNode(e, t) {
        for (var n = data.getRoot(e), o = 0, a = n.curSelectedList.length; o < a; o++) t !== n.curSelectedList[o] && data.getNodeCache(e, n.curSelectedList[o].tId) || (n.curSelectedList.splice(o, 1), e.treeObj.trigger(consts.event.UNSELECTED, [e.treeId, t]), o--, a--);
      },
      setCache: function setCache(e, t) {
        caches[e.treeId] = t;
      },
      setRoot: function setRoot(e, t) {
        roots[e.treeId] = t;
      },
      setZTreeTools: function setZTreeTools(e, t) {
        for (var n = 0, o = _init.zTreeTools.length; n < o; n++) _init.zTreeTools[n].apply(this, arguments);
      },
      transformToArrayFormat: function transformToArrayFormat(n, e) {
        if (!e) return [];
        var o = [];
        if (tools.isArray(e)) for (var t = 0, a = e.length; t < a; t++) {
          r(e[t]);
        } else r(e);
        return o;
        function r(e) {
          o.push(e);
          var t = data.nodeChildren(n, e);
          t && (o = o.concat(data.transformToArrayFormat(n, t)));
        }
      },
      transformTozTreeFormat: function transformTozTreeFormat(e, t) {
        var n,
          o,
          a = e.data.simpleData.idKey,
          r = e.data.simpleData.pIdKey;
        if (!a || "" == a || !t) return [];
        if (tools.isArray(t)) {
          var d = [],
            i = {};
          for (n = 0, o = t.length; n < o; n++) i[t[n][a]] = t[n];
          for (n = 0, o = t.length; n < o; n++) {
            var s = i[t[n][r]];
            if (s && t[n][a] != t[n][r]) {
              var l = data.nodeChildren(e, s);
              (l = l || data.nodeChildren(e, s, [])).push(t[n]);
            } else d.push(t[n]);
          }
          return d;
        }
        return [t];
      }
    },
    event = {
      bindEvent: function bindEvent(e) {
        for (var t = 0, n = _init.bind.length; t < n; t++) _init.bind[t].apply(this, arguments);
      },
      unbindEvent: function unbindEvent(e) {
        for (var t = 0, n = _init.unbind.length; t < n; t++) _init.unbind[t].apply(this, arguments);
      },
      bindTree: function bindTree(e) {
        var t = {
            treeId: e.treeId
          },
          n = e.treeObj;
        e.view.txtSelectedEnable || n.bind("selectstart", handler.onSelectStart).css({
          "-moz-user-select": "-moz-none"
        }), n.bind("click", t, event.proxy), n.bind("dblclick", t, event.proxy), n.bind("mouseover", t, event.proxy), n.bind("mouseout", t, event.proxy), n.bind("mousedown", t, event.proxy), n.bind("mouseup", t, event.proxy), n.bind("contextmenu", t, event.proxy);
      },
      unbindTree: function unbindTree(e) {
        e.treeObj.unbind("selectstart", handler.onSelectStart).unbind("click", event.proxy).unbind("dblclick", event.proxy).unbind("mouseover", event.proxy).unbind("mouseout", event.proxy).unbind("mousedown", event.proxy).unbind("mouseup", event.proxy).unbind("contextmenu", event.proxy);
      },
      doProxy: function doProxy(e) {
        for (var t = [], n = 0, o = _init.proxys.length; n < o; n++) {
          var a = _init.proxys[n].apply(this, arguments);
          if (t.push(a), a.stop) break;
        }
        return t;
      },
      proxy: function proxy(e) {
        var t = data.getSetting(e.data.treeId);
        if (!tools.uCanDo(t, e)) return !0;
        for (var n = event.doProxy(e), o = !0, a = 0, r = n.length; a < r; a++) {
          var d = n[a];
          d.nodeEventCallback && (o = d.nodeEventCallback.apply(d, [e, d.node]) && o), d.treeEventCallback && (o = d.treeEventCallback.apply(d, [e, d.node]) && o);
        }
        return o;
      }
    },
    handler = {
      onSwitchNode: function onSwitchNode(e, t) {
        var n = data.getSetting(e.data.treeId);
        if (t.open) {
          if (0 == tools.apply(n.callback.beforeCollapse, [n.treeId, t], !0)) return !0;
          data.getRoot(n).expandTriggerFlag = !0, view.switchNode(n, t);
        } else {
          if (0 == tools.apply(n.callback.beforeExpand, [n.treeId, t], !0)) return !0;
          data.getRoot(n).expandTriggerFlag = !0, view.switchNode(n, t);
        }
        return !0;
      },
      onClickNode: function onClickNode(e, t) {
        var n = data.getSetting(e.data.treeId),
          o = n.view.autoCancelSelected && (e.ctrlKey || e.metaKey) && data.isSelectedNode(n, t) ? 0 : n.view.autoCancelSelected && (e.ctrlKey || e.metaKey) && n.view.selectedMulti ? 2 : 1;
        return 0 == tools.apply(n.callback.beforeClick, [n.treeId, t, o], !0) || (0 == o ? view.cancelPreSelectedNode(n, t) : view.selectNode(n, t, 2 == o), n.treeObj.trigger(consts.event.CLICK, [e, n.treeId, t, o])), !0;
      },
      onZTreeMousedown: function onZTreeMousedown(e, t) {
        var n = data.getSetting(e.data.treeId);
        return tools.apply(n.callback.beforeMouseDown, [n.treeId, t], !0) && tools.apply(n.callback.onMouseDown, [e, n.treeId, t]), !0;
      },
      onZTreeMouseup: function onZTreeMouseup(e, t) {
        var n = data.getSetting(e.data.treeId);
        return tools.apply(n.callback.beforeMouseUp, [n.treeId, t], !0) && tools.apply(n.callback.onMouseUp, [e, n.treeId, t]), !0;
      },
      onZTreeDblclick: function onZTreeDblclick(e, t) {
        var n = data.getSetting(e.data.treeId);
        return tools.apply(n.callback.beforeDblClick, [n.treeId, t], !0) && tools.apply(n.callback.onDblClick, [e, n.treeId, t]), !0;
      },
      onZTreeContextmenu: function onZTreeContextmenu(e, t) {
        var n = data.getSetting(e.data.treeId);
        return tools.apply(n.callback.beforeRightClick, [n.treeId, t], !0) && tools.apply(n.callback.onRightClick, [e, n.treeId, t]), "function" != typeof n.callback.onRightClick;
      },
      onSelectStart: function onSelectStart(e) {
        var t = e.originalEvent.srcElement.nodeName.toLowerCase();
        return "input" === t || "textarea" === t;
      }
    },
    tools = {
      apply: function apply(e, t, n) {
        return "function" == typeof e ? e.apply(zt, t || []) : n;
      },
      canAsync: function canAsync(e, t) {
        var n = data.nodeChildren(e, t),
          o = data.nodeIsParent(e, t);
        return e.async.enable && t && o && !(t.zAsync || n && 0 < n.length);
      },
      clone: function clone(e) {
        if (null === e) return null;
        var t = tools.isArray(e) ? [] : {};
        for (var n in e) t[n] = e[n] instanceof Date ? new Date(e[n].getTime()) : "object" == _typeof(e[n]) ? tools.clone(e[n]) : e[n];
        return t;
      },
      eqs: function eqs(e, t) {
        return e.toLowerCase() === t.toLowerCase();
      },
      isArray: function isArray(e) {
        return "[object Array]" === Object.prototype.toString.apply(e);
      },
      isElement: function isElement(e) {
        return "object" == (typeof HTMLElement === "undefined" ? "undefined" : _typeof(HTMLElement)) ? e instanceof HTMLElement : e && "object" == _typeof(e) && null !== e && 1 === e.nodeType && "string" == typeof e.nodeName;
      },
      $: function $(e, t, n) {
        return t && "string" != typeof t && (n = t, t = ""), "string" == typeof e ? _$(e, n ? n.treeObj.get(0).ownerDocument : null) : _$("#" + e.tId + t, n ? n.treeObj : null);
      },
      getMDom: function getMDom(e, t, n) {
        if (!t) return null;
        for (; t && t.id !== e.treeId;) {
          for (var o = 0, a = n.length; t.tagName && o < a; o++) if (tools.eqs(t.tagName, n[o].tagName) && null !== t.getAttribute(n[o].attrName)) return t;
          t = t.parentNode;
        }
        return null;
      },
      getNodeMainDom: function getNodeMainDom(e) {
        return _$(e).parent("li").get(0) || _$(e).parentsUntil("li").parent().get(0);
      },
      isChildOrSelf: function isChildOrSelf(e, t) {
        return 0 < _$(e).closest("#" + t).length;
      },
      uCanDo: function uCanDo(e, t) {
        return !0;
      }
    },
    view = {
      addNodes: function addNodes(e, t, n, o, a) {
        var r = data.nodeIsParent(e, t);
        if (!e.data.keep.leaf || !t || r) if (tools.isArray(o) || (o = [o]), e.data.simpleData.enable && (o = data.transformTozTreeFormat(e, o)), t) {
          var d = $$(t, consts.id.SWITCH, e),
            i = $$(t, consts.id.ICON, e),
            s = $$(t, consts.id.UL, e);
          t.open || (view.replaceSwitchClass(t, d, consts.folder.CLOSE), view.replaceIcoClass(t, i, consts.folder.CLOSE), t.open = !1, s.css({
            display: "none"
          })), data.addNodesData(e, t, n, o), view.createNodes(e, t.level + 1, o, t, n), a || view.expandCollapseParentNode(e, t, !0);
        } else data.addNodesData(e, data.getRoot(e), n, o), view.createNodes(e, 0, o, null, n);
      },
      appendNodes: function appendNodes(e, t, n, o, a, r, d) {
        if (!n) return [];
        var i,
          s,
          l = [],
          c = o || data.getRoot(e),
          u = data.nodeChildren(e, c);
        (!u || a >= u.length - n.length) && (a = -1);
        for (var p = 0, f = n.length; p < f; p++) {
          var g = n[p];
          r && (i = (0 === a || u.length == n.length) && 0 == p, s = a < 0 && p == n.length - 1, data.initNode(e, t, g, o, i, s, d), data.addNodeCache(e, g));
          var v = data.nodeIsParent(e, g),
            N = [],
            h = data.nodeChildren(e, g);
          h && 0 < h.length && (N = view.appendNodes(e, t + 1, h, g, -1, r, d && g.open)), d && (view.makeDOMNodeMainBefore(l, e, g), view.makeDOMNodeLine(l, e, g), data.getBeforeA(e, g, l), view.makeDOMNodeNameBefore(l, e, g), data.getInnerBeforeA(e, g, l), view.makeDOMNodeIcon(l, e, g), data.getInnerAfterA(e, g, l), view.makeDOMNodeNameAfter(l, e, g), data.getAfterA(e, g, l), v && g.open && view.makeUlHtml(e, g, l, N.join("")), view.makeDOMNodeMainAfter(l, e, g), data.addCreatedNode(e, g));
        }
        return l;
      },
      appendParentULDom: function appendParentULDom(e, t) {
        var n = [],
          o = $$(t, e);
        !o.get(0) && t.parentTId && (view.appendParentULDom(e, t.getParentNode()), o = $$(t, e));
        var a = $$(t, consts.id.UL, e);
        a.get(0) && a.remove();
        var r = data.nodeChildren(e, t),
          d = view.appendNodes(e, t.level + 1, r, t, -1, !1, !0);
        view.makeUlHtml(e, t, n, d.join("")), o.append(n.join(""));
      },
      asyncNode: function asyncNode(setting, node, isSilent, callback) {
        var i,
          l,
          isParent = data.nodeIsParent(setting, node);
        if (node && !isParent) return tools.apply(callback), !1;
        if (node && node.isAjaxing) return !1;
        if (0 == tools.apply(setting.callback.beforeAsync, [setting.treeId, node], !0)) return tools.apply(callback), !1;
        if (node) {
          node.isAjaxing = !0;
          var icoObj = $$(node, consts.id.ICON, setting);
          icoObj.attr({
            style: "",
            "class": consts.className.BUTTON + " " + consts.className.ICO_LOADING
          });
        }
        var tmpParam = {},
          autoParam = tools.apply(setting.async.autoParam, [setting.treeId, node], setting.async.autoParam);
        for (i = 0, l = autoParam.length; node && i < l; i++) {
          var pKey = autoParam[i].split("="),
            spKey = pKey;
          1 < pKey.length && (spKey = pKey[1], pKey = pKey[0]), tmpParam[spKey] = node[pKey];
        }
        var otherParam = tools.apply(setting.async.otherParam, [setting.treeId, node], setting.async.otherParam);
        if (tools.isArray(otherParam)) for (i = 0, l = otherParam.length; i < l; i += 2) tmpParam[otherParam[i]] = otherParam[i + 1];else for (var p in otherParam) tmpParam[p] = otherParam[p];
        var _tmpV = data.getRoot(setting)._ver;
        return _$.ajax({
          contentType: setting.async.contentType,
          cache: !1,
          type: setting.async.type,
          url: tools.apply(setting.async.url, [setting.treeId, node], setting.async.url),
          data: -1 < setting.async.contentType.indexOf("application/json") ? JSON.stringify(tmpParam) : tmpParam,
          dataType: setting.async.dataType,
          headers: setting.async.headers,
          xhrFields: setting.async.xhrFields,
          success: function success(msg) {
            if (_tmpV == data.getRoot(setting)._ver) {
              var newNodes = [];
              try {
                newNodes = msg && 0 != msg.length ? "string" == typeof msg ? eval("(" + msg + ")") : msg : [];
              } catch (e) {
                newNodes = msg;
              }
              node && (node.isAjaxing = null, node.zAsync = !0), view.setNodeLineIcos(setting, node), newNodes && "" !== newNodes ? (newNodes = tools.apply(setting.async.dataFilter, [setting.treeId, node, newNodes], newNodes), view.addNodes(setting, node, -1, newNodes ? tools.clone(newNodes) : [], !!isSilent)) : view.addNodes(setting, node, -1, [], !!isSilent), setting.treeObj.trigger(consts.event.ASYNC_SUCCESS, [setting.treeId, node, msg]), tools.apply(callback);
            }
          },
          error: function error(e, t, n) {
            _tmpV == data.getRoot(setting)._ver && (node && (node.isAjaxing = null), view.setNodeLineIcos(setting, node), setting.treeObj.trigger(consts.event.ASYNC_ERROR, [setting.treeId, node, e, t, n]));
          }
        }), !0;
      },
      cancelPreSelectedNode: function cancelPreSelectedNode(e, t, n) {
        var o,
          a,
          r = data.getRoot(e).curSelectedList;
        for (o = r.length - 1; 0 <= o; o--) if (t === (a = r[o]) || !t && (!n || n !== a)) {
          if ($$(a, consts.id.A, e).removeClass(consts.node.CURSELECTED), t) {
            data.removeSelectedNode(e, t);
            break;
          }
          r.splice(o, 1), e.treeObj.trigger(consts.event.UNSELECTED, [e.treeId, a]);
        }
      },
      createNodeCallback: function createNodeCallback(e) {
        if (e.callback.onNodeCreated || e.view.addDiyDom) for (var t = data.getRoot(e); 0 < t.createdNodes.length;) {
          var n = t.createdNodes.shift();
          tools.apply(e.view.addDiyDom, [e.treeId, n]), e.callback.onNodeCreated && e.treeObj.trigger(consts.event.NODECREATED, [e.treeId, n]);
        }
      },
      createNodes: function createNodes(e, t, n, o, a) {
        if (n && 0 != n.length) {
          var r = data.getRoot(e),
            d = !o || o.open || !!$$(data.nodeChildren(e, o)[0], e).get(0);
          r.createdNodes = [];
          var i,
            s,
            l = view.appendNodes(e, t, n, o, a, !0, d);
          if (o) {
            var c = $$(o, consts.id.UL, e);
            c.get(0) && (i = c);
          } else i = e.treeObj;
          i && (0 <= a && (s = i.children()[a]), 0 <= a && s ? _$(s).before(l.join("")) : i.append(l.join(""))), view.createNodeCallback(e);
        }
      },
      destroy: function destroy(e) {
        e && (data.initCache(e), data.initRoot(e), event.unbindTree(e), event.unbindEvent(e), e.treeObj.empty(), delete settings[e.treeId]);
      },
      expandCollapseNode: function expandCollapseNode(e, t, n, o, a) {
        var r,
          d = data.getRoot(e);
        if (t) {
          var i = data.nodeChildren(e, t),
            s = data.nodeIsParent(e, t);
          if (d.expandTriggerFlag && (r = a, a = function a() {
            r && r(), t.open ? e.treeObj.trigger(consts.event.EXPAND, [e.treeId, t]) : e.treeObj.trigger(consts.event.COLLAPSE, [e.treeId, t]);
          }, d.expandTriggerFlag = !1), !t.open && s && (!$$(t, consts.id.UL, e).get(0) || i && 0 < i.length && !$$(i[0], e).get(0)) && (view.appendParentULDom(e, t), view.createNodeCallback(e)), t.open != n) {
            var l = $$(t, consts.id.UL, e),
              c = $$(t, consts.id.SWITCH, e),
              u = $$(t, consts.id.ICON, e);
            s ? (t.open = !t.open, t.iconOpen && t.iconClose && u.attr("style", view.makeNodeIcoStyle(e, t)), t.open ? (view.replaceSwitchClass(t, c, consts.folder.OPEN), view.replaceIcoClass(t, u, consts.folder.OPEN), 0 == o || "" == e.view.expandSpeed ? (l.show(), tools.apply(a, [])) : i && 0 < i.length ? l.slideDown(e.view.expandSpeed, a) : (l.show(), tools.apply(a, []))) : (view.replaceSwitchClass(t, c, consts.folder.CLOSE), view.replaceIcoClass(t, u, consts.folder.CLOSE), 0 != o && "" != e.view.expandSpeed && i && 0 < i.length ? l.slideUp(e.view.expandSpeed, a) : (l.hide(), tools.apply(a, [])))) : tools.apply(a, []);
          } else tools.apply(a, []);
        } else tools.apply(a, []);
      },
      expandCollapseParentNode: function expandCollapseParentNode(e, t, n, o, a) {
        t && (t.parentTId ? (view.expandCollapseNode(e, t, n, o), t.parentTId && view.expandCollapseParentNode(e, t.getParentNode(), n, o, a)) : view.expandCollapseNode(e, t, n, o, a));
      },
      expandCollapseSonNode: function expandCollapseSonNode(e, t, n, o, a) {
        var r = data.getRoot(e),
          d = t ? data.nodeChildren(e, t) : data.nodeChildren(e, r),
          i = !t && o,
          s = data.getRoot(e).expandTriggerFlag;
        if (data.getRoot(e).expandTriggerFlag = !1, d) for (var l = 0, c = d.length; l < c; l++) d[l] && view.expandCollapseSonNode(e, d[l], n, i);
        data.getRoot(e).expandTriggerFlag = s, view.expandCollapseNode(e, t, n, o, a);
      },
      isSelectedNode: function isSelectedNode(e, t) {
        if (!t) return !1;
        var n,
          o = data.getRoot(e).curSelectedList;
        for (n = o.length - 1; 0 <= n; n--) if (t === o[n]) return !0;
        return !1;
      },
      makeDOMNodeIcon: function makeDOMNodeIcon(e, t, n) {
        var o = data.nodeName(t, n),
          a = t.view.nameIsHTML ? o : o.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
        e.push("<span id='", n.tId, consts.id.ICON, "' title='' treeNode", consts.id.ICON, " class='", view.makeNodeIcoClass(t, n), "' style='", view.makeNodeIcoStyle(t, n), "'></span><span id='", n.tId, consts.id.SPAN, "' class='", consts.className.NAME, "'>", a, "</span>");
      },
      makeDOMNodeLine: function makeDOMNodeLine(e, t, n) {
        e.push("<span id='", n.tId, consts.id.SWITCH, "' title='' class='", view.makeNodeLineClass(t, n), "' treeNode", consts.id.SWITCH, "></span>");
      },
      makeDOMNodeMainAfter: function makeDOMNodeMainAfter(e, t, n) {
        e.push("</li>");
      },
      makeDOMNodeMainBefore: function makeDOMNodeMainBefore(e, t, n) {
        e.push("<li id='", n.tId, "' class='", consts.className.LEVEL, n.level, "' tabindex='0' hidefocus='true' treenode>");
      },
      makeDOMNodeNameAfter: function makeDOMNodeNameAfter(e, t, n) {
        e.push("</a>");
      },
      makeDOMNodeNameBefore: function makeDOMNodeNameBefore(e, t, n) {
        var o = data.nodeTitle(t, n),
          a = view.makeNodeUrl(t, n),
          r = view.makeNodeFontCss(t, n),
          d = view.makeNodeClasses(t, n),
          i = [];
        for (var s in r) i.push(s, ":", r[s], ";");
        e.push("<a id='", n.tId, consts.id.A, "' class='", consts.className.LEVEL, n.level, d.add ? " " + d.add.join(" ") : "", "' treeNode", consts.id.A, n.click ? ' onclick="' + n.click + '"' : "", null != a && 0 < a.length ? " href='" + a + "'" : "", " target='", view.makeNodeTarget(n), "' style='", i.join(""), "'"), tools.apply(t.view.showTitle, [t.treeId, n], t.view.showTitle) && o && e.push("title='", o.replace(/'/g, "&#39;").replace(/</g, "&lt;").replace(/>/g, "&gt;"), "'"), e.push(">");
      },
      makeNodeFontCss: function makeNodeFontCss(e, t) {
        var n = tools.apply(e.view.fontCss, [e.treeId, t], e.view.fontCss);
        return n && "function" != typeof n ? n : {};
      },
      makeNodeClasses: function makeNodeClasses(e, t) {
        var n = tools.apply(e.view.nodeClasses, [e.treeId, t], e.view.nodeClasses);
        return n && "function" != typeof n ? n : {
          add: [],
          remove: []
        };
      },
      makeNodeIcoClass: function makeNodeIcoClass(e, t) {
        var n = ["ico"];
        if (!t.isAjaxing) {
          var o = data.nodeIsParent(e, t);
          n[0] = (t.iconSkin ? t.iconSkin + "_" : "") + n[0], o ? n.push(t.open ? consts.folder.OPEN : consts.folder.CLOSE) : n.push(consts.folder.DOCU);
        }
        return consts.className.BUTTON + " " + n.join("_");
      },
      makeNodeIcoStyle: function makeNodeIcoStyle(e, t) {
        var n = [];
        if (!t.isAjaxing) {
          var o = data.nodeIsParent(e, t) && t.iconOpen && t.iconClose ? t.open ? t.iconOpen : t.iconClose : t[e.data.key.icon];
          o && n.push("background:url(", o, ") 0 0 no-repeat;"), 0 != e.view.showIcon && tools.apply(e.view.showIcon, [e.treeId, t], !0) || n.push("display:none;");
        }
        return n.join("");
      },
      makeNodeLineClass: function makeNodeLineClass(e, t) {
        var n = [];
        return e.view.showLine ? 0 == t.level && t.isFirstNode && t.isLastNode ? n.push(consts.line.ROOT) : 0 == t.level && t.isFirstNode ? n.push(consts.line.ROOTS) : t.isLastNode ? n.push(consts.line.BOTTOM) : n.push(consts.line.CENTER) : n.push(consts.line.NOLINE), data.nodeIsParent(e, t) ? n.push(t.open ? consts.folder.OPEN : consts.folder.CLOSE) : n.push(consts.folder.DOCU), view.makeNodeLineClassEx(t) + n.join("_");
      },
      makeNodeLineClassEx: function makeNodeLineClassEx(e) {
        return consts.className.BUTTON + " " + consts.className.LEVEL + e.level + " " + consts.className.SWITCH + " ";
      },
      makeNodeTarget: function makeNodeTarget(e) {
        return e.target || "_blank";
      },
      makeNodeUrl: function makeNodeUrl(e, t) {
        var n = e.data.key.url;
        return t[n] ? t[n] : null;
      },
      makeUlHtml: function makeUlHtml(e, t, n, o) {
        n.push("<ul id='", t.tId, consts.id.UL, "' class='", consts.className.LEVEL, t.level, " ", view.makeUlLineClass(e, t), "' style='display:", t.open ? "block" : "none", "'>"), n.push(o), n.push("</ul>");
      },
      makeUlLineClass: function makeUlLineClass(e, t) {
        return e.view.showLine && !t.isLastNode ? consts.line.LINE : "";
      },
      removeChildNodes: function removeChildNodes(e, t) {
        if (t) {
          var n = data.nodeChildren(e, t);
          if (n) {
            for (var o = 0, a = n.length; o < a; o++) data.removeNodeCache(e, n[o]);
            if (data.removeSelectedNode(e), delete t[e.data.key.children], e.data.keep.parent) $$(t, consts.id.UL, e).empty();else {
              data.nodeIsParent(e, t, !1), t.open = !1;
              var r = $$(t, consts.id.SWITCH, e),
                d = $$(t, consts.id.ICON, e);
              view.replaceSwitchClass(t, r, consts.folder.DOCU), view.replaceIcoClass(t, d, consts.folder.DOCU), $$(t, consts.id.UL, e).remove();
            }
          }
        }
      },
      scrollIntoView: function scrollIntoView(e, t) {
        if (t) if ("undefined" != typeof Element && "undefined" != typeof HTMLElement) Element.prototype.scrollIntoViewIfNeeded || (Element.prototype.scrollIntoViewIfNeeded = function (n) {
          "use strict";

          function e(e, t) {
            return {
              start: e,
              length: t,
              end: e + t
            };
          }
          function t(e, t) {
            return !1 === n || t.start < e.end && e.start < t.end ? Math.max(e.end - t.length, Math.min(t.start, e.start)) : (e.start + e.end - t.length) / 2;
          }
          function a(n, o) {
            return {
              x: n,
              y: o,
              translate: function translate(e, t) {
                return a(n + e, o + t);
              }
            };
          }
          function o(e, t) {
            for (; e;) t = t.translate(e.offsetLeft, e.offsetTop), e = e.offsetParent;
            return t;
          }
          for (var r, d = o(this, a(0, 0)), i = a(this.offsetWidth, this.offsetHeight), s = this.parentNode; s instanceof HTMLElement;) r = o(s, a(s.clientLeft, s.clientTop)), s.scrollLeft = t(e(d.x - r.x, i.x), e(s.scrollLeft, s.clientWidth)), s.scrollTop = t(e(d.y - r.y, i.y), e(s.scrollTop, s.clientHeight)), d = d.translate(-s.scrollLeft, -s.scrollTop), s = s.parentNode;
        }), t.scrollIntoViewIfNeeded();else {
          var n = e.treeObj.get(0).getBoundingClientRect(),
            o = t.getBoundingClientRect();
          (o.top < n.top || o.bottom > n.bottom || o.right > n.right || o.left < n.left) && t.scrollIntoView();
        }
      },
      setFirstNode: function setFirstNode(e, t) {
        var n = data.nodeChildren(e, t);
        0 < n.length && (n[0].isFirstNode = !0);
      },
      setLastNode: function setLastNode(e, t) {
        var n = data.nodeChildren(e, t);
        0 < n.length && (n[n.length - 1].isLastNode = !0);
      },
      removeNode: function removeNode(e, t) {
        var n = data.getRoot(e),
          o = t.parentTId ? t.getParentNode() : n;
        if (t.isFirstNode = !1, t.isLastNode = !1, t.getPreNode = function () {
          return null;
        }, t.getNextNode = function () {
          return null;
        }, data.getNodeCache(e, t.tId)) {
          $$(t, e).remove(), data.removeNodeCache(e, t), data.removeSelectedNode(e, t);
          for (var a = data.nodeChildren(e, o), r = 0, d = a.length; r < d; r++) if (a[r].tId == t.tId) {
            a.splice(r, 1);
            break;
          }
          view.setFirstNode(e, o), view.setLastNode(e, o);
          var i,
            s,
            l,
            c = a.length;
          if (e.data.keep.parent || 0 != c) {
            if (e.view.showLine && 0 < c) {
              var u = a[c - 1];
              if (i = $$(u, consts.id.UL, e), s = $$(u, consts.id.SWITCH, e), l = $$(u, consts.id.ICON, e), o == n) {
                if (1 == a.length) view.replaceSwitchClass(u, s, consts.line.ROOT);else {
                  var p = $$(a[0], consts.id.SWITCH, e);
                  view.replaceSwitchClass(a[0], p, consts.line.ROOTS), view.replaceSwitchClass(u, s, consts.line.BOTTOM);
                }
              } else view.replaceSwitchClass(u, s, consts.line.BOTTOM);
              i.removeClass(consts.line.LINE);
            }
          } else data.nodeIsParent(e, o, !1), o.open = !1, delete o[e.data.key.children], i = $$(o, consts.id.UL, e), s = $$(o, consts.id.SWITCH, e), l = $$(o, consts.id.ICON, e), view.replaceSwitchClass(o, s, consts.folder.DOCU), view.replaceIcoClass(o, l, consts.folder.DOCU), i.css("display", "none");
        }
      },
      replaceIcoClass: function replaceIcoClass(e, t, n) {
        if (t && !e.isAjaxing) {
          var o = t.attr("class");
          if (null != o) {
            var a = o.split("_");
            switch (n) {
              case consts.folder.OPEN:
              case consts.folder.CLOSE:
              case consts.folder.DOCU:
                a[a.length - 1] = n;
            }
            t.attr("class", a.join("_"));
          }
        }
      },
      replaceSwitchClass: function replaceSwitchClass(e, t, n) {
        if (t) {
          var o = t.attr("class");
          if (null != o) {
            var a = o.split("_");
            switch (n) {
              case consts.line.ROOT:
              case consts.line.ROOTS:
              case consts.line.CENTER:
              case consts.line.BOTTOM:
              case consts.line.NOLINE:
                a[0] = view.makeNodeLineClassEx(e) + n;
                break;
              case consts.folder.OPEN:
              case consts.folder.CLOSE:
              case consts.folder.DOCU:
                a[1] = n;
            }
            t.attr("class", a.join("_")), n !== consts.folder.DOCU ? t.removeAttr("disabled") : t.attr("disabled", "disabled");
          }
        }
      },
      selectNode: function selectNode(e, t, n) {
        n || view.cancelPreSelectedNode(e, null, t), $$(t, consts.id.A, e).addClass(consts.node.CURSELECTED), data.addSelectedNode(e, t), e.treeObj.trigger(consts.event.SELECTED, [e.treeId, t]);
      },
      setNodeFontCss: function setNodeFontCss(e, t) {
        var n = $$(t, consts.id.A, e),
          o = view.makeNodeFontCss(e, t);
        o && n.css(o);
      },
      setNodeClasses: function setNodeClasses(e, t) {
        var n = $$(t, consts.id.A, e),
          o = view.makeNodeClasses(e, t);
        "add" in o && o.add.length && n.addClass(o.add.join(" ")), "remove" in o && o.remove.length && n.removeClass(o.remove.join(" "));
      },
      setNodeLineIcos: function setNodeLineIcos(e, t) {
        if (t) {
          var n = $$(t, consts.id.SWITCH, e),
            o = $$(t, consts.id.UL, e),
            a = $$(t, consts.id.ICON, e),
            r = view.makeUlLineClass(e, t);
          0 == r.length ? o.removeClass(consts.line.LINE) : o.addClass(r), n.attr("class", view.makeNodeLineClass(e, t)), data.nodeIsParent(e, t) ? n.removeAttr("disabled") : n.attr("disabled", "disabled"), a.removeAttr("style"), a.attr("style", view.makeNodeIcoStyle(e, t)), a.attr("class", view.makeNodeIcoClass(e, t));
        }
      },
      setNodeName: function setNodeName(e, t) {
        var n = data.nodeTitle(e, t),
          o = $$(t, consts.id.SPAN, e);
        o.empty(), e.view.nameIsHTML ? o.html(data.nodeName(e, t)) : o.text(data.nodeName(e, t)), tools.apply(e.view.showTitle, [e.treeId, t], e.view.showTitle) && $$(t, consts.id.A, e).attr("title", n || "");
      },
      setNodeTarget: function setNodeTarget(e, t) {
        $$(t, consts.id.A, e).attr("target", view.makeNodeTarget(t));
      },
      setNodeUrl: function setNodeUrl(e, t) {
        var n = $$(t, consts.id.A, e),
          o = view.makeNodeUrl(e, t);
        null == o || 0 == o.length ? n.removeAttr("href") : n.attr("href", o);
      },
      switchNode: function switchNode(e, t) {
        if (t.open || !tools.canAsync(e, t)) view.expandCollapseNode(e, t, !t.open);else if (e.async.enable) {
          if (!view.asyncNode(e, t)) return void view.expandCollapseNode(e, t, !t.open);
        } else t && view.expandCollapseNode(e, t, !t.open);
      }
    };
  _$.fn.zTree = {
    consts: _consts,
    _z: {
      tools: tools,
      view: view,
      event: event,
      data: data
    },
    getZTreeObj: function getZTreeObj(e) {
      var t = data.getZTreeTools(e);
      return t || null;
    },
    destroy: function destroy(e) {
      if (e && 0 < e.length) view.destroy(data.getSetting(e));else for (var t in settings) view.destroy(settings[t]);
    },
    init: function init(e, t, n) {
      var s = tools.clone(_setting);
      _$.extend(!0, s, t), s.treeId = e.attr("id"), s.treeObj = e, s.treeObj.empty(), settings[s.treeId] = s, void 0 === document.body.style.maxHeight && (s.view.expandSpeed = ""), data.initRoot(s);
      var o = data.getRoot(s);
      n = n ? tools.clone(tools.isArray(n) ? n : [n]) : [], s.data.simpleData.enable ? data.nodeChildren(s, o, data.transformTozTreeFormat(s, n)) : data.nodeChildren(s, o, n), data.initCache(s), event.unbindTree(s), event.bindTree(s), event.unbindEvent(s), event.bindEvent(s);
      var r = {
        setting: s,
        addNodes: function addNodes(e, t, n, o) {
          e = e || null;
          var a = data.nodeIsParent(s, e);
          if (e && !a && s.data.keep.leaf) return null;
          var r = parseInt(t, 10);
          if (t = isNaN(r) ? (o = !!n, n = t, -1) : r, !n) return null;
          var d = tools.clone(tools.isArray(n) ? n : [n]);
          function i() {
            view.addNodes(s, e, t, d, 1 == o);
          }
          return tools.canAsync(s, e) ? view.asyncNode(s, e, o, i) : i(), d;
        },
        cancelSelectedNode: function cancelSelectedNode(e) {
          view.cancelPreSelectedNode(s, e);
        },
        destroy: function destroy() {
          view.destroy(s);
        },
        expandAll: function expandAll(e) {
          return e = !!e, view.expandCollapseSonNode(s, null, e, !0), e;
        },
        expandNode: function expandNode(t, e, n, o, a) {
          return t && data.nodeIsParent(s, t) ? (!0 !== e && !1 !== e && (e = !t.open), (a = !!a) && e && 0 == tools.apply(s.callback.beforeExpand, [s.treeId, t], !0) || a && !e && 0 == tools.apply(s.callback.beforeCollapse, [s.treeId, t], !0) ? null : (e && t.parentTId && view.expandCollapseParentNode(s, t.getParentNode(), e, !1), e !== t.open || n ? (data.getRoot(s).expandTriggerFlag = a, !tools.canAsync(s, t) && n ? view.expandCollapseSonNode(s, t, e, !0, r) : (t.open = !e, view.switchNode(this.setting, t), r()), e) : null)) : null;
          function r() {
            var e = $$(t, consts.id.A, s).get(0);
            e && !1 !== o && view.scrollIntoView(s, e);
          }
        },
        getNodes: function getNodes() {
          return data.getNodes(s);
        },
        getNodeByParam: function getNodeByParam(e, t, n) {
          return e ? data.getNodeByParam(s, n ? data.nodeChildren(s, n) : data.getNodes(s), e, t) : null;
        },
        getNodeByTId: function getNodeByTId(e) {
          return data.getNodeCache(s, e);
        },
        getNodesByParam: function getNodesByParam(e, t, n) {
          return e ? data.getNodesByParam(s, n ? data.nodeChildren(s, n) : data.getNodes(s), e, t) : null;
        },
        getNodesByParamFuzzy: function getNodesByParamFuzzy(e, t, n) {
          return e ? data.getNodesByParamFuzzy(s, n ? data.nodeChildren(s, n) : data.getNodes(s), e, t) : null;
        },
        getNodesByFilter: function getNodesByFilter(e, t, n, o) {
          return t = !!t, e && "function" == typeof e ? data.getNodesByFilter(s, n ? data.nodeChildren(s, n) : data.getNodes(s), e, t, o) : t ? null : [];
        },
        getNodeIndex: function getNodeIndex(e) {
          if (!e) return null;
          for (var t = e.parentTId ? e.getParentNode() : data.getRoot(s), n = data.nodeChildren(s, t), o = 0, a = n.length; o < a; o++) if (n[o] == e) return o;
          return -1;
        },
        getSelectedNodes: function getSelectedNodes() {
          for (var e = [], t = data.getRoot(s).curSelectedList, n = 0, o = t.length; n < o; n++) e.push(t[n]);
          return e;
        },
        isSelectedNode: function isSelectedNode(e) {
          return data.isSelectedNode(s, e);
        },
        reAsyncChildNodesPromise: function reAsyncChildNodesPromise(n, o, a) {
          return new Promise(function (e, t) {
            try {
              r.reAsyncChildNodes(n, o, a, function () {
                e(n);
              });
            } catch (e) {
              t(e);
            }
          });
        },
        reAsyncChildNodes: function reAsyncChildNodes(e, t, n, o) {
          if (this.setting.async.enable) {
            var a = !e;
            if (a && (e = data.getRoot(s)), "refresh" == t) {
              for (var r = data.nodeChildren(s, e), d = 0, i = r ? r.length : 0; d < i; d++) data.removeNodeCache(s, r[d]);
              if (data.removeSelectedNode(s), data.nodeChildren(s, e, []), a) this.setting.treeObj.empty();else $$(e, consts.id.UL, s).empty();
            }
            view.asyncNode(this.setting, a ? null : e, !!n, o);
          }
        },
        refresh: function refresh() {
          this.setting.treeObj.empty();
          var e = data.getRoot(s),
            t = data.nodeChildren(s, e);
          data.initRoot(s), data.nodeChildren(s, e, t), data.initCache(s), view.createNodes(s, 0, data.nodeChildren(s, e), null, -1);
        },
        removeChildNodes: function removeChildNodes(e) {
          if (!e) return null;
          var t = data.nodeChildren(s, e);
          return view.removeChildNodes(s, e), t || null;
        },
        removeNode: function removeNode(e, t) {
          e && ((t = !!t) && 0 == tools.apply(s.callback.beforeRemove, [s.treeId, e], !0) || (view.removeNode(s, e), t && this.setting.treeObj.trigger(consts.event.REMOVE, [s.treeId, e])));
        },
        selectNode: function selectNode(t, e, n) {
          if (t && tools.uCanDo(s)) {
            if (e = s.view.selectedMulti && e, t.parentTId) view.expandCollapseParentNode(s, t.getParentNode(), !0, !1, function () {
              if (n) return;
              var e = $$(t, s).get(0);
              view.scrollIntoView(s, e);
            });else if (!n) try {
              $$(t, s).focus().blur();
            } catch (e) {}
            view.selectNode(s, t, e);
          }
        },
        transformTozTreeNodes: function transformTozTreeNodes(e) {
          return data.transformTozTreeFormat(s, e);
        },
        transformToArray: function transformToArray(e) {
          return data.transformToArrayFormat(s, e);
        },
        updateNode: function updateNode(e, t) {
          e && $$(e, s).get(0) && tools.uCanDo(s) && (view.setNodeName(s, e), view.setNodeTarget(s, e), view.setNodeUrl(s, e), view.setNodeLineIcos(s, e), view.setNodeFontCss(s, e), view.setNodeClasses(s, e));
        }
      };
      o.treeTools = r, data.setZTreeTools(s, r);
      var a = data.nodeChildren(s, o);
      return a && 0 < a.length ? view.createNodes(s, 0, a, null, -1) : s.async.enable && s.async.url && "" !== s.async.url && view.asyncNode(s), r;
    }
  };
  var zt = _$.fn.zTree,
    $$ = tools.$,
    consts = zt.consts;
}(jQuery);
!function (e) {
  var t = {
      event: {
        CHECK: "ztree_check"
      },
      id: {
        CHECK: "_check"
      },
      checkbox: {
        STYLE: "checkbox",
        DEFAULT: "chk",
        DISABLED: "disable",
        FALSE: "false",
        TRUE: "true",
        FULL: "full",
        PART: "part",
        FOCUS: "focus"
      },
      radio: {
        STYLE: "radio",
        TYPE_ALL: "all",
        TYPE_LEVEL: "level"
      }
    },
    c = {
      check: {
        enable: !1,
        autoCheckTrigger: !1,
        chkStyle: t.checkbox.STYLE,
        nocheckInherit: !1,
        chkDisabledInherit: !1,
        radioType: t.radio.TYPE_LEVEL,
        chkboxType: {
          Y: "ps",
          N: "ps"
        }
      },
      data: {
        key: {
          checked: "checked"
        }
      },
      callback: {
        beforeCheck: null,
        onCheck: null
      }
    },
    r = {
      onCheckNode: function onCheckNode(e, t) {
        if (!0 === t.chkDisabled) return !1;
        var c = C.getSetting(e.data.treeId);
        if (0 == o.apply(c.callback.beforeCheck, [c.treeId, t], !0)) return !0;
        var h = C.nodeChecked(c, t);
        C.nodeChecked(c, t, !h), l.checkNodeRelation(c, t);
        var a = f(t, s.id.CHECK, c);
        return l.setChkClass(c, a, t), l.repairParentChkClassWithSelf(c, t), c.treeObj.trigger(s.event.CHECK, [e, c.treeId, t]), !0;
      },
      onMouseoverCheck: function onMouseoverCheck(e, t) {
        if (!0 === t.chkDisabled) return !1;
        var c = C.getSetting(e.data.treeId),
          h = f(t, s.id.CHECK, c);
        return t.check_Focus = !0, l.setChkClass(c, h, t), !0;
      },
      onMouseoutCheck: function onMouseoutCheck(e, t) {
        if (!0 === t.chkDisabled) return !1;
        var c = C.getSetting(e.data.treeId),
          h = f(t, s.id.CHECK, c);
        return t.check_Focus = !1, l.setChkClass(c, h, t), !0;
      }
    },
    h = {
      tools: {},
      view: {
        checkNodeRelation: function checkNodeRelation(e, t) {
          var c,
            h,
            a,
            n = s.radio,
            i = C.nodeChecked(e, t);
          if (e.check.chkStyle == n.STYLE) {
            var r = C.getRadioCheckedList(e);
            if (i) {
              if (e.check.radioType == n.TYPE_ALL) {
                for (h = r.length - 1; 0 <= h; h--) {
                  c = r[h], C.nodeChecked(e, c) && c != t && (C.nodeChecked(e, c, !1), r.splice(h, 1), l.setChkClass(e, f(c, s.id.CHECK, e), c), c.parentTId != t.parentTId && l.repairParentChkClassWithSelf(e, c));
                }
                r.push(t);
              } else {
                var o = t.parentTId ? t.getParentNode() : C.getRoot(e);
                for (h = 0, a = (d = C.nodeChildren(e, o)).length; h < a; h++) {
                  c = d[h], C.nodeChecked(e, c) && c != t && (C.nodeChecked(e, c, !1), l.setChkClass(e, f(c, s.id.CHECK, e), c));
                }
              }
            } else if (e.check.radioType == n.TYPE_ALL) for (h = 0, a = r.length; h < a; h++) if (t == r[h]) {
              r.splice(h, 1);
              break;
            }
          } else {
            var d = C.nodeChildren(e, t);
            i && (!d || 0 == d.length || -1 < e.check.chkboxType.Y.indexOf("s")) && l.setSonNodeCheckBox(e, t, !0), i || d && 0 != d.length && !(-1 < e.check.chkboxType.N.indexOf("s")) || l.setSonNodeCheckBox(e, t, !1), i && -1 < e.check.chkboxType.Y.indexOf("p") && l.setParentNodeCheckBox(e, t, !0), !i && -1 < e.check.chkboxType.N.indexOf("p") && l.setParentNodeCheckBox(e, t, !1);
          }
        },
        makeChkClass: function makeChkClass(e, t) {
          var c = s.checkbox,
            h = s.radio,
            a = "",
            n = C.nodeChecked(e, t);
          a = !0 === t.chkDisabled ? c.DISABLED : t.halfCheck ? c.PART : e.check.chkStyle == h.STYLE ? t.check_Child_State < 1 ? c.FULL : c.PART : n ? 2 === t.check_Child_State || -1 === t.check_Child_State ? c.FULL : c.PART : t.check_Child_State < 1 ? c.FULL : c.PART;
          var i = e.check.chkStyle + "_" + (n ? c.TRUE : c.FALSE) + "_" + a;
          return i = t.check_Focus && !0 !== t.chkDisabled ? i + "_" + c.FOCUS : i, s.className.BUTTON + " " + c.DEFAULT + " " + i;
        },
        repairAllChk: function repairAllChk(e, t) {
          if (e.check.enable && e.check.chkStyle === s.checkbox.STYLE) for (var c = C.getRoot(e), h = C.nodeChildren(e, c), a = 0, n = h.length; a < n; a++) {
            var i = h[a];
            !0 !== i.nocheck && !0 !== i.chkDisabled && C.nodeChecked(e, i, t), l.setSonNodeCheckBox(e, i, t);
          }
        },
        repairChkClass: function repairChkClass(e, t) {
          if (t && (C.makeChkFlag(e, t), !0 !== t.nocheck)) {
            var c = f(t, s.id.CHECK, e);
            l.setChkClass(e, c, t);
          }
        },
        repairParentChkClass: function repairParentChkClass(e, t) {
          if (t && t.parentTId) {
            var c = t.getParentNode();
            l.repairChkClass(e, c), l.repairParentChkClass(e, c);
          }
        },
        repairParentChkClassWithSelf: function repairParentChkClassWithSelf(e, t) {
          if (t) {
            var c = C.nodeChildren(e, t);
            c && 0 < c.length ? l.repairParentChkClass(e, c[0]) : l.repairParentChkClass(e, t);
          }
        },
        repairSonChkDisabled: function repairSonChkDisabled(e, t, c, h) {
          if (t) {
            t.chkDisabled != c && (t.chkDisabled = c), l.repairChkClass(e, t);
            var a = C.nodeChildren(e, t);
            if (a && h) for (var n = 0, i = a.length; n < i; n++) {
              var r = a[n];
              l.repairSonChkDisabled(e, r, c, h);
            }
          }
        },
        repairParentChkDisabled: function repairParentChkDisabled(e, t, c, h) {
          t && (t.chkDisabled != c && h && (t.chkDisabled = c), l.repairChkClass(e, t), l.repairParentChkDisabled(e, t.getParentNode(), c, h));
        },
        setChkClass: function setChkClass(e, t, c) {
          t && (!0 === c.nocheck ? t.hide() : t.show(), t.attr("class", l.makeChkClass(e, c)));
        },
        setParentNodeCheckBox: function setParentNodeCheckBox(e, t, c, h) {
          var a = f(t, s.id.CHECK, e);
          if (h = h || t, C.makeChkFlag(e, t), !0 !== t.nocheck && !0 !== t.chkDisabled && (C.nodeChecked(e, t, c), l.setChkClass(e, a, t), e.check.autoCheckTrigger && t != h && e.treeObj.trigger(s.event.CHECK, [null, e.treeId, t])), t.parentTId) {
            var n = !0;
            if (!c) for (var i = C.nodeChildren(e, t.getParentNode()), r = 0, o = i.length; r < o; r++) {
              var d = i[r],
                k = C.nodeChecked(e, d);
              if (!0 !== d.nocheck && !0 !== d.chkDisabled && k || (!0 === d.nocheck || !0 === d.chkDisabled) && 0 < d.check_Child_State) {
                n = !1;
                break;
              }
            }
            n && l.setParentNodeCheckBox(e, t.getParentNode(), c, h);
          }
        },
        setSonNodeCheckBox: function setSonNodeCheckBox(e, t, c, h) {
          if (t) {
            var a = f(t, s.id.CHECK, e);
            h = h || t;
            var n = !1,
              i = C.nodeChildren(e, t);
            if (i) for (var r = 0, o = i.length; r < o; r++) {
              var d = i[r];
              l.setSonNodeCheckBox(e, d, c, h), !0 === d.chkDisabled && (n = !0);
            }
            t != C.getRoot(e) && !0 !== t.chkDisabled && (n && !0 !== t.nocheck && C.makeChkFlag(e, t), !0 !== t.nocheck && !0 !== t.chkDisabled ? (C.nodeChecked(e, t, c), n || (t.check_Child_State = i && 0 < i.length ? c ? 2 : 0 : -1)) : t.check_Child_State = -1, l.setChkClass(e, a, t), e.check.autoCheckTrigger && t != h && !0 !== t.nocheck && !0 !== t.chkDisabled && e.treeObj.trigger(s.event.CHECK, [null, e.treeId, t]));
          }
        }
      },
      event: {},
      data: {
        getRadioCheckedList: function getRadioCheckedList(e) {
          for (var t = C.getRoot(e).radioCheckedList, c = 0, h = t.length; c < h; c++) C.getNodeCache(e, t[c].tId) || (t.splice(c, 1), c--, h--);
          return t;
        },
        getCheckStatus: function getCheckStatus(e, t) {
          if (!e.check.enable || t.nocheck || t.chkDisabled) return null;
          var c = C.nodeChecked(e, t);
          return {
            checked: c,
            half: t.halfCheck ? t.halfCheck : e.check.chkStyle == s.radio.STYLE ? 2 === t.check_Child_State : c ? -1 < t.check_Child_State && t.check_Child_State < 2 : 0 < t.check_Child_State
          };
        },
        getTreeCheckedNodes: function getTreeCheckedNodes(e, t, c, h) {
          if (!t) return [];
          var a = c && e.check.chkStyle == s.radio.STYLE && e.check.radioType == s.radio.TYPE_ALL;
          h = h || [];
          for (var n = 0, i = t.length; n < i; n++) {
            var r = t[n],
              o = C.nodeChildren(e, r),
              d = C.nodeChecked(e, r);
            if (!0 !== r.nocheck && !0 !== r.chkDisabled && d == c && (h.push(r), a)) break;
            if (C.getTreeCheckedNodes(e, o, c, h), a && 0 < h.length) break;
          }
          return h;
        },
        getTreeChangeCheckedNodes: function getTreeChangeCheckedNodes(e, t, c) {
          if (!t) return [];
          c = c || [];
          for (var h = 0, a = t.length; h < a; h++) {
            var n = t[h],
              i = C.nodeChildren(e, n),
              r = C.nodeChecked(e, n);
            !0 !== n.nocheck && !0 !== n.chkDisabled && r != n.checkedOld && c.push(n), C.getTreeChangeCheckedNodes(e, i, c);
          }
          return c;
        },
        makeChkFlag: function makeChkFlag(e, t) {
          if (t) {
            var c = -1,
              h = C.nodeChildren(e, t);
            if (h) for (var a = 0, n = h.length; a < n; a++) {
              var i = h[a],
                r = C.nodeChecked(e, i),
                o = -1;
              if (e.check.chkStyle == s.radio.STYLE) {
                if (2 == (o = !0 === i.nocheck || !0 === i.chkDisabled ? i.check_Child_State : !0 === i.halfCheck || r || 0 < i.check_Child_State ? 2 : 0)) {
                  c = 2;
                  break;
                }
                0 == o && (c = 0);
              } else if (e.check.chkStyle == s.checkbox.STYLE) {
                if (1 === (o = !0 === i.nocheck || !0 === i.chkDisabled ? i.check_Child_State : !0 === i.halfCheck ? 1 : r ? -1 === i.check_Child_State || 2 === i.check_Child_State ? 2 : 1 : 0 < i.check_Child_State ? 1 : 0)) {
                  c = 1;
                  break;
                }
                if (2 === o && -1 < c && 0 < a && o !== c) {
                  c = 1;
                  break;
                }
                if (2 === c && -1 < o && o < 2) {
                  c = 1;
                  break;
                }
                -1 < o && (c = o);
              }
            }
            t.check_Child_State = c;
          }
        }
      }
    };
  e.extend(!0, e.fn.zTree.consts, t), e.extend(!0, e.fn.zTree._z, h);
  var a = e.fn.zTree,
    o = a._z.tools,
    s = a.consts,
    l = a._z.view,
    C = a._z.data,
    f = (a._z.event, o.$);
  C.nodeChecked = function (e, t, c) {
    if (!t) return !1;
    var h = e.data.key.checked;
    return void 0 !== c ? ("string" == typeof c && (c = o.eqs(c, "true")), c = !!c, t[h] = c) : "string" == typeof t[h] ? t[h] = o.eqs(t[h], "true") : t[h] = !!t[h], t[h];
  }, C.exSetting(c), C.addInitBind(function (a) {
    var e = a.treeObj,
      t = s.event;
    e.bind(t.CHECK, function (e, t, c, h) {
      e.srcEvent = t, o.apply(a.callback.onCheck, [e, c, h]);
    });
  }), C.addInitUnBind(function (e) {
    var t = e.treeObj,
      c = s.event;
    t.unbind(c.CHECK);
  }), C.addInitCache(function (e) {}), C.addInitNode(function (e, t, c, h, a, n, i) {
    if (c) {
      var r = C.nodeChecked(e, c);
      if (c.checkedOld = r, "string" == typeof c.nocheck && (c.nocheck = o.eqs(c.nocheck, "true")), c.nocheck = !!c.nocheck || e.check.nocheckInherit && h && !!h.nocheck, "string" == typeof c.chkDisabled && (c.chkDisabled = o.eqs(c.chkDisabled, "true")), c.chkDisabled = !!c.chkDisabled || e.check.chkDisabledInherit && h && !!h.chkDisabled, "string" == typeof c.halfCheck && (c.halfCheck = o.eqs(c.halfCheck, "true")), c.halfCheck = !!c.halfCheck, c.check_Child_State = -1, c.check_Focus = !1, c.getCheckStatus = function () {
        return C.getCheckStatus(e, c);
      }, e.check.chkStyle == s.radio.STYLE && e.check.radioType == s.radio.TYPE_ALL && r) C.getRoot(e).radioCheckedList.push(c);
    }
  }), C.addInitProxy(function (e) {
    var t = e.target,
      c = C.getSetting(e.data.treeId),
      h = "",
      a = null,
      n = "",
      i = null;
    if (o.eqs(e.type, "mouseover") ? c.check.enable && o.eqs(t.tagName, "span") && null !== t.getAttribute("treeNode" + s.id.CHECK) && (h = o.getNodeMainDom(t).id, n = "mouseoverCheck") : o.eqs(e.type, "mouseout") ? c.check.enable && o.eqs(t.tagName, "span") && null !== t.getAttribute("treeNode" + s.id.CHECK) && (h = o.getNodeMainDom(t).id, n = "mouseoutCheck") : o.eqs(e.type, "click") && c.check.enable && o.eqs(t.tagName, "span") && null !== t.getAttribute("treeNode" + s.id.CHECK) && (h = o.getNodeMainDom(t).id, n = "checkNode"), 0 < h.length) switch (a = C.getNodeCache(c, h), n) {
      case "checkNode":
        i = r.onCheckNode;
        break;
      case "mouseoverCheck":
        i = r.onMouseoverCheck;
        break;
      case "mouseoutCheck":
        i = r.onMouseoutCheck;
    }
    return {
      stop: "checkNode" === n,
      node: a,
      nodeEventType: n,
      nodeEventCallback: i,
      treeEventType: "",
      treeEventCallback: null
    };
  }, !0), C.addInitRoot(function (e) {
    C.getRoot(e).radioCheckedList = [];
  }), C.addBeforeA(function (e, t, c) {
    e.check.enable && (C.makeChkFlag(e, t), c.push("<span ID='", t.tId, s.id.CHECK, "' class='", l.makeChkClass(e, t), "' treeNode", s.id.CHECK, !0 === t.nocheck ? " style='display:none;'" : "", "></span>"));
  }), C.addZTreeTools(function (i, h) {
    h.checkNode = function (e, t, c, h) {
      var a = C.nodeChecked(i, e);
      if (!0 !== e.chkDisabled && (!0 !== t && !1 !== t && (t = !a), h = !!h, (a !== t || c) && (!h || 0 != o.apply(this.setting.callback.beforeCheck, [this.setting.treeId, e], !0)) && o.uCanDo(this.setting) && this.setting.check.enable && !0 !== e.nocheck)) {
        C.nodeChecked(i, e, t);
        var n = f(e, s.id.CHECK, this.setting);
        !c && this.setting.check.chkStyle !== s.radio.STYLE || l.checkNodeRelation(this.setting, e), l.setChkClass(this.setting, n, e), l.repairParentChkClassWithSelf(this.setting, e), h && this.setting.treeObj.trigger(s.event.CHECK, [null, this.setting.treeId, e]);
      }
    }, h.checkAllNodes = function (e) {
      l.repairAllChk(this.setting, !!e);
    }, h.getCheckedNodes = function (e) {
      e = !1 !== e;
      var t = C.nodeChildren(i, C.getRoot(this.setting));
      return C.getTreeCheckedNodes(this.setting, t, e);
    }, h.getChangeCheckedNodes = function () {
      var e = C.nodeChildren(i, C.getRoot(this.setting));
      return C.getTreeChangeCheckedNodes(this.setting, e);
    }, h.setChkDisabled = function (e, t, c, h) {
      t = !!t, c = !!c, h = !!h, l.repairSonChkDisabled(this.setting, e, t, h), l.repairParentChkDisabled(this.setting, e.getParentNode(), t, c);
    };
    var a = h.updateNode;
    h.updateNode = function (e, t) {
      if ((a && a.apply(h, arguments), e && this.setting.check.enable) && f(e, this.setting).get(0) && o.uCanDo(this.setting)) {
        var c = f(e, s.id.CHECK, this.setting);
        1 != t && this.setting.check.chkStyle !== s.radio.STYLE || l.checkNodeRelation(this.setting, e), l.setChkClass(this.setting, c, e), l.repairParentChkClassWithSelf(this.setting, e);
      }
    };
  });
  var n = l.createNodes;
  l.createNodes = function (e, t, c, h, a) {
    n && n.apply(l, arguments), c && l.repairParentChkClassWithSelf(e, h);
  };
  var i = l.removeNode;
  l.removeNode = function (e, t) {
    var c = t.getParentNode();
    i && i.apply(l, arguments), t && c && (l.repairChkClass(e, c), l.repairParentChkClass(e, c));
  };
  var d = l.appendNodes;
  l.appendNodes = function (e, t, c, h, a, n, i) {
    var r = "";
    return d && (r = d.apply(l, arguments)), h && C.makeChkFlag(e, h), r;
  };
}(jQuery);
!function (fe) {
  var Ee = {
      event: {
        DRAG: "ztree_drag",
        DROP: "ztree_drop",
        RENAME: "ztree_rename",
        DRAGMOVE: "ztree_dragmove"
      },
      id: {
        EDIT: "_edit",
        INPUT: "_input",
        REMOVE: "_remove"
      },
      move: {
        TYPE_INNER: "inner",
        TYPE_PREV: "prev",
        TYPE_NEXT: "next"
      },
      node: {
        CURSELECTED_EDIT: "curSelectedNode_Edit",
        TMPTARGET_TREE: "tmpTargetzTree",
        TMPTARGET_NODE: "tmpTargetNode"
      }
    },
    s = {
      onHoverOverNode: function onHoverOverNode(e, t) {
        var o = Re.getSetting(e.data.treeId),
          d = Re.getRoot(o);
        d.curHoverNode != t && s.onHoverOutNode(e), d.curHoverNode = t, be.addHoverDom(o, t);
      },
      onHoverOutNode: function onHoverOutNode(e, t) {
        var o = Re.getSetting(e.data.treeId),
          d = Re.getRoot(o);
        d.curHoverNode && !Re.isSelectedNode(o, d.curHoverNode) && (be.removeTreeDom(o, d.curHoverNode), d.curHoverNode = null);
      },
      onMousedownNode: function onMousedownNode(e, t) {
        var o,
          d,
          Z = Re.getSetting(e.data.treeId),
          $ = Re.getRoot(Z),
          J = Re.getRoots();
        if (2 == e.button || !Z.edit.enable || !Z.edit.drag.isCopy && !Z.edit.drag.isMove) return !0;
        var r = e.target,
          n = Re.getRoot(Z).curSelectedList,
          ee = [];
        if (Re.isSelectedNode(Z, t)) for (o = 0, d = n.length; o < d; o++) {
          if (n[o].editNameFlag && Ie.eqs(r.tagName, "input") && null !== r.getAttribute("treeNode" + he.id.INPUT)) return !0;
          if (ee.push(n[o]), ee[0].parentTId !== n[o].parentTId) {
            ee = [t];
            break;
          }
        } else ee = [t];
        be.editNodeBlur = !0, be.cancelCurEditNode(Z);
        var te,
          oe,
          de,
          re,
          ne,
          ae = fe(Z.treeObj.get(0).ownerDocument),
          ie = fe(Z.treeObj.get(0).ownerDocument.body),
          le = !1,
          se = Z,
          l = Z,
          ce = null,
          Ne = null,
          ue = null,
          ve = he.move.TYPE_INNER,
          ge = e.clientX,
          me = e.clientY,
          pe = new Date().getTime();
        function s(e) {
          if (0 == $.dragFlag && Math.abs(ge - e.clientX) < Z.edit.drag.minMoveSize && Math.abs(me - e.clientY) < Z.edit.drag.minMoveSize) return !0;
          var t, o, d, r, n;
          if (ie.css("cursor", "pointer"), 0 == $.dragFlag) {
            if (0 == Ie.apply(Z.callback.beforeDrag, [Z.treeId, ee], !0)) return Te(e), !0;
            for (t = 0, o = ee.length; t < o; t++) 0 == t && ($.dragNodeShowBefore = []), d = ee[t], Re.nodeIsParent(Z, d) && d.open ? (be.expandCollapseNode(Z, d, !d.open), $.dragNodeShowBefore[d.tId] = !0) : $.dragNodeShowBefore[d.tId] = !1;
            $.dragFlag = 1, J.showHoverDom = !1, Ie.showIfameMask(Z, !0);
            var a = !0,
              i = -1;
            if (1 < ee.length) {
              var l = ee[0].parentTId ? Re.nodeChildren(Z, ee[0].getParentNode()) : Re.getNodes(Z);
              for (n = [], t = 0, o = l.length; t < o; t++) if (void 0 !== $.dragNodeShowBefore[l[t].tId] && (a && -1 < i && i + 1 !== t && (a = !1), n.push(l[t]), i = t), ee.length === n.length) {
                ee = n;
                break;
              }
            }
            for (a && (re = ee[0].getPreNode(), ne = ee[ee.length - 1].getNextNode()), te = Pe("<ul class='zTreeDragUL'></ul>", Z), t = 0, o = ee.length; t < o; t++) (d = ee[t]).editNameFlag = !1, be.selectNode(Z, d, 0 < t), be.removeTreeDom(Z, d), t > Z.edit.drag.maxShowNodeNum - 1 || ((r = Pe("<li id='" + d.tId + "_tmp'></li>", Z)).append(Pe(d, he.id.A, Z).clone()), r.css("padding", "0"), r.children("#" + d.tId + he.id.A).removeClass(he.node.CURSELECTED), te.append(r), t == Z.edit.drag.maxShowNodeNum - 1 && (r = Pe("<li id='" + d.tId + "_moretmp'><a>  ...  </a></li>", Z), te.append(r)));
            te.attr("id", ee[0].tId + he.id.UL + "_tmp"), te.addClass(Z.treeObj.attr("class")), te.appendTo(ie), (oe = Pe("<span class='tmpzTreeMove_arrow'></span>", Z)).attr("id", "zTreeMove_arrow_tmp"), oe.appendTo(ie), Z.treeObj.trigger(he.event.DRAG, [e, Z.treeId, ee]);
          }
          if (1 == $.dragFlag) {
            if (de && oe.attr("id") == e.target.id && ue && e.clientX + ae.scrollLeft() + 2 > fe("#" + ue + he.id.A, de).offset().left) {
              var s = fe("#" + ue + he.id.A, de);
              e.target = 0 < s.length ? s.get(0) : e.target;
            } else de && (de.removeClass(he.node.TMPTARGET_TREE), ue && fe("#" + ue + he.id.A, de).removeClass(he.node.TMPTARGET_NODE + "_" + he.move.TYPE_PREV).removeClass(he.node.TMPTARGET_NODE + "_" + Ee.move.TYPE_NEXT).removeClass(he.node.TMPTARGET_NODE + "_" + Ee.move.TYPE_INNER));
            ue = de = null, le = !1, se = Z;
            var c = Re.getSettings();
            for (var N in c) c[N].treeId && c[N].edit.enable && c[N].treeId != Z.treeId && (e.target.id == c[N].treeId || 0 < fe(e.target).parents("#" + c[N].treeId).length) && (le = !0, se = c[N]);
            var u = ae.scrollTop(),
              v = ae.scrollLeft(),
              g = se.treeObj.offset(),
              m = se.treeObj.get(0).scrollHeight,
              p = se.treeObj.get(0).scrollWidth,
              T = e.clientY + u - g.top,
              f = se.treeObj.height() + g.top - e.clientY - u,
              E = e.clientX + v - g.left,
              I = se.treeObj.width() + g.left - e.clientX - v,
              h = T < Z.edit.drag.borderMax && T > Z.edit.drag.borderMin,
              b = f < Z.edit.drag.borderMax && f > Z.edit.drag.borderMin,
              R = E < Z.edit.drag.borderMax && E > Z.edit.drag.borderMin,
              P = I < Z.edit.drag.borderMax && I > Z.edit.drag.borderMin,
              C = T > Z.edit.drag.borderMin && f > Z.edit.drag.borderMin && E > Z.edit.drag.borderMin && I > Z.edit.drag.borderMin,
              w = h && se.treeObj.scrollTop() <= 0,
              M = b && se.treeObj.scrollTop() + se.treeObj.height() + 10 >= m,
              _ = R && se.treeObj.scrollLeft() <= 0,
              O = P && se.treeObj.scrollLeft() + se.treeObj.width() + 10 >= p;
            if (e.target && Ie.isChildOrSelf(e.target, se.treeId)) {
              for (var D = e.target; D && D.tagName && !Ie.eqs(D.tagName, "li") && D.id != se.treeId;) D = D.parentNode;
              var y = !0;
              for (t = 0, o = ee.length; t < o; t++) {
                if (d = ee[t], D.id === d.tId) {
                  y = !1;
                  break;
                }
                if (0 < Pe(d, Z).find("#" + D.id).length) {
                  y = !1;
                  break;
                }
              }
              y && e.target && Ie.isChildOrSelf(e.target, D.id + he.id.A) && (de = fe(D), ue = D.id);
            }
            d = ee[0], C && Ie.isChildOrSelf(e.target, se.treeId) && (!de && (e.target.id == se.treeId || w || M || _ || O) && (le || !le && d.parentTId) && (de = se.treeObj), h ? se.treeObj.scrollTop(se.treeObj.scrollTop() - 10) : b && se.treeObj.scrollTop(se.treeObj.scrollTop() + 10), R ? se.treeObj.scrollLeft(se.treeObj.scrollLeft() - 10) : P && se.treeObj.scrollLeft(se.treeObj.scrollLeft() + 10), de && de != se.treeObj && de.offset().left < se.treeObj.offset().left && se.treeObj.scrollLeft(se.treeObj.scrollLeft() + de.offset().left - se.treeObj.offset().left)), te.css({
              top: e.clientY + u + 3 + "px",
              left: e.clientX + v + 3 + "px"
            });
            var k = 0,
              S = 0;
            if (de && de.attr("id") != se.treeId) {
              var F = function F() {
                de = null, ue = "", ve = he.move.TYPE_INNER, oe.css({
                  display: "none"
                }), window.zTreeMoveTimer && (clearTimeout(window.zTreeMoveTimer), window.zTreeMoveTargetNodeTId = null);
              };
              var L = null == ue ? null : Re.getNodeCache(se, ue),
                A = (e.ctrlKey || e.metaKey) && Z.edit.drag.isMove && Z.edit.drag.isCopy || !Z.edit.drag.isMove && Z.edit.drag.isCopy,
                Y = !(!re || ue !== re.tId),
                x = !(!ne || ue !== ne.tId),
                j = d.parentTId && d.parentTId == ue,
                z = (A || !x) && Ie.apply(se.edit.drag.prev, [se.treeId, ee, L], !!se.edit.drag.prev),
                B = (A || !Y) && Ie.apply(se.edit.drag.next, [se.treeId, ee, L], !!se.edit.drag.next),
                H = (A || !j) && !(se.data.keep.leaf && !Re.nodeIsParent(Z, L)) && Ie.apply(se.edit.drag.inner, [se.treeId, ee, L], !!se.edit.drag.inner);
              if (z || B || H) {
                var V = fe("#" + ue + he.id.A, de),
                  U = L.isLastNode ? null : fe("#" + L.getNextNode().tId + he.id.A, de.next()),
                  G = V.offset().top,
                  X = V.offset().left,
                  q = z ? H ? .25 : B ? .5 : 1 : -1,
                  W = B ? H ? .75 : z ? .5 : 0 : -1,
                  K = (e.clientY + u - G) / V.height();
                if ((1 == q || K <= q && -.2 <= K) && z ? (k = 1 - oe.width(), S = G - oe.height() / 2, ve = he.move.TYPE_PREV) : (0 == W || W <= K && K <= 1.2) && B ? (k = 1 - oe.width(), S = null == U || Re.nodeIsParent(Z, L) && L.open ? G + V.height() - oe.height() / 2 : U.offset().top - oe.height() / 2, ve = he.move.TYPE_NEXT) : H ? (k = 5 - oe.width(), S = G, ve = he.move.TYPE_INNER) : F(), de && (oe.css({
                  display: "block",
                  top: S + "px",
                  left: X + k + "px"
                }), V.addClass(he.node.TMPTARGET_NODE + "_" + ve), ce == ue && Ne == ve || (pe = new Date().getTime()), L && Re.nodeIsParent(Z, L) && ve == he.move.TYPE_INNER)) {
                  var Q = !0;
                  window.zTreeMoveTimer && window.zTreeMoveTargetNodeTId !== L.tId ? (clearTimeout(window.zTreeMoveTimer), window.zTreeMoveTargetNodeTId = null) : window.zTreeMoveTimer && window.zTreeMoveTargetNodeTId === L.tId && (Q = !1), Q && (window.zTreeMoveTimer = setTimeout(function () {
                    ve == he.move.TYPE_INNER && L && Re.nodeIsParent(Z, L) && !L.open && new Date().getTime() - pe > se.edit.drag.autoOpenTime && Ie.apply(se.callback.beforeDragOpen, [se.treeId, L], !0) && (be.switchNode(se, L), se.edit.drag.autoExpandTrigger && se.treeObj.trigger(he.event.EXPAND, [se.treeId, L]));
                  }, se.edit.drag.autoOpenTime + 50), window.zTreeMoveTargetNodeTId = L.tId);
                }
              } else F();
            } else ve = he.move.TYPE_INNER, de && Ie.apply(se.edit.drag.inner, [se.treeId, ee, null], !!se.edit.drag.inner) ? de.addClass(he.node.TMPTARGET_TREE) : de = null, oe.css({
              display: "none"
            }), window.zTreeMoveTimer && (clearTimeout(window.zTreeMoveTimer), window.zTreeMoveTargetNodeTId = null);
            ce = ue, Ne = ve, Z.treeObj.trigger(he.event.DRAGMOVE, [e, Z.treeId, ee]);
          }
          return !1;
        }
        function Te(d) {
          if (window.zTreeMoveTimer && (clearTimeout(window.zTreeMoveTimer), window.zTreeMoveTargetNodeTId = null), Ne = ce = null, ae.unbind("mousemove", s), ae.unbind("mouseup", Te), ae.unbind("selectstart", c), ie.css("cursor", ""), de && (de.removeClass(he.node.TMPTARGET_TREE), ue && fe("#" + ue + he.id.A, de).removeClass(he.node.TMPTARGET_NODE + "_" + he.move.TYPE_PREV).removeClass(he.node.TMPTARGET_NODE + "_" + Ee.move.TYPE_NEXT).removeClass(he.node.TMPTARGET_NODE + "_" + Ee.move.TYPE_INNER)), Ie.showIfameMask(Z, !1), J.showHoverDom = !0, 0 != $.dragFlag) {
            var e, t, o;
            for (e = $.dragFlag = 0, t = ee.length; e < t; e++) o = ee[e], Re.nodeIsParent(Z, o) && $.dragNodeShowBefore[o.tId] && !o.open && (be.expandCollapseNode(Z, o, !o.open), delete $.dragNodeShowBefore[o.tId]);
            te && te.remove(), oe && oe.remove();
            var r = (d.ctrlKey || d.metaKey) && Z.edit.drag.isMove && Z.edit.drag.isCopy || !Z.edit.drag.isMove && Z.edit.drag.isCopy;
            if (!r && de && ue && ee[0].parentTId && ue == ee[0].parentTId && ve == he.move.TYPE_INNER && (de = null), de) {
              var _i = function _i() {
                if (le) {
                  if (!r) for (var e = 0, t = ee.length; e < t; e++) be.removeNode(Z, ee[e]);
                  ve == he.move.TYPE_INNER ? be.addNodes(se, n, -1, a) : be.addNodes(se, n.getParentNode(), ve == he.move.TYPE_PREV ? n.getIndex() : n.getIndex() + 1, a);
                } else if (r && ve == he.move.TYPE_INNER) be.addNodes(se, n, -1, a);else if (r) be.addNodes(se, n.getParentNode(), ve == he.move.TYPE_PREV ? n.getIndex() : n.getIndex() + 1, a);else if (ve != he.move.TYPE_NEXT) for (e = 0, t = a.length; e < t; e++) be.moveNode(se, n, a[e], ve, !1);else for (e = -1, t = a.length - 1; e < t; t--) be.moveNode(se, n, a[t], ve, !1);
                be.selectNodes(se, a);
                var o = Pe(a[0], Z).get(0);
                be.scrollIntoView(Z, o), Z.treeObj.trigger(he.event.DROP, [d, se.treeId, a, n, ve, r]);
              };
              var n = null == ue ? null : Re.getNodeCache(se, ue);
              if (0 == Ie.apply(Z.callback.beforeDrop, [se.treeId, ee, n, ve, r], !0)) return void be.selectNodes(l, ee);
              var a = r ? Ie.clone(ee) : ee;
              ve == he.move.TYPE_INNER && Ie.canAsync(se, n) ? be.asyncNode(se, n, !1, _i) : _i();
            } else be.selectNodes(l, ee), Z.treeObj.trigger(he.event.DROP, [d, Z.treeId, ee, null, null, null]);
          }
        }
        function c() {
          return !1;
        }
        return Ie.uCanDo(Z) && ae.bind("mousemove", s), ae.bind("mouseup", Te), ae.bind("selectstart", c), !0;
      }
    },
    e = {
      tools: {
        getAbs: function getAbs(e) {
          var t = e.getBoundingClientRect(),
            o = document.body.scrollTop + document.documentElement.scrollTop,
            d = document.body.scrollLeft + document.documentElement.scrollLeft;
          return [t.left + d, t.top + o];
        },
        inputFocus: function inputFocus(e) {
          e.get(0) && (e.focus(), Ie.setCursorPosition(e.get(0), e.val().length));
        },
        inputSelect: function inputSelect(e) {
          e.get(0) && (e.focus(), e.select());
        },
        setCursorPosition: function setCursorPosition(e, t) {
          if (e.setSelectionRange) e.focus(), e.setSelectionRange(t, t);else if (e.createTextRange) {
            var o = e.createTextRange();
            o.collapse(!0), o.moveEnd("character", t), o.moveStart("character", t), o.select();
          }
        },
        showIfameMask: function showIfameMask(e, t) {
          for (var o = Re.getRoot(e); 0 < o.dragMaskList.length;) o.dragMaskList[0].remove(), o.dragMaskList.shift();
          if (t) for (var d = Pe("iframe", e), r = 0, n = d.length; r < n; r++) {
            var a = d.get(r),
              i = Ie.getAbs(a),
              l = Pe("<div id='zTreeMask_" + r + "' class='zTreeMask' style='top:" + i[1] + "px; left:" + i[0] + "px; width:" + a.offsetWidth + "px; height:" + a.offsetHeight + "px;'></div>", e);
            l.appendTo(Pe("body", e)), o.dragMaskList.push(l);
          }
        }
      },
      view: {
        addEditBtn: function addEditBtn(e, t) {
          if (!(t.editNameFlag || 0 < Pe(t, he.id.EDIT, e).length) && Ie.apply(e.edit.showRenameBtn, [e.treeId, t], e.edit.showRenameBtn)) {
            var o = Pe(t, he.id.A, e),
              d = "<span class='" + he.className.BUTTON + " edit' id='" + t.tId + he.id.EDIT + "' title='" + Ie.apply(e.edit.renameTitle, [e.treeId, t], e.edit.renameTitle) + "' treeNode" + he.id.EDIT + " style='display:none;'></span>";
            o.append(d), Pe(t, he.id.EDIT, e).bind("click", function () {
              return Ie.uCanDo(e) && 0 != Ie.apply(e.callback.beforeEditName, [e.treeId, t], !0) && be.editNode(e, t), !1;
            }).show();
          }
        },
        addRemoveBtn: function addRemoveBtn(e, t) {
          if (!(t.editNameFlag || 0 < Pe(t, he.id.REMOVE, e).length) && Ie.apply(e.edit.showRemoveBtn, [e.treeId, t], e.edit.showRemoveBtn)) {
            var o = Pe(t, he.id.A, e),
              d = "<span class='" + he.className.BUTTON + " remove' id='" + t.tId + he.id.REMOVE + "' title='" + Ie.apply(e.edit.removeTitle, [e.treeId, t], e.edit.removeTitle) + "' treeNode" + he.id.REMOVE + " style='display:none;'></span>";
            o.append(d), Pe(t, he.id.REMOVE, e).bind("click", function () {
              return Ie.uCanDo(e) && 0 != Ie.apply(e.callback.beforeRemove, [e.treeId, t], !0) && (be.removeNode(e, t), e.treeObj.trigger(he.event.REMOVE, [e.treeId, t])), !1;
            }).bind("mousedown", function (e) {
              return !0;
            }).show();
          }
        },
        addHoverDom: function addHoverDom(e, t) {
          Re.getRoots().showHoverDom && (t.isHover = !0, e.edit.enable && (be.addEditBtn(e, t), be.addRemoveBtn(e, t)), Ie.apply(e.view.addHoverDom, [e.treeId, t]));
        },
        cancelCurEditNode: function cancelCurEditNode(e, t, o) {
          var d = Re.getRoot(e),
            r = d.curEditNode;
          if (r) {
            var n = d.curEditInput,
              a = t || (o ? Re.nodeName(e, r) : n.val());
            if (!1 === Ie.apply(e.callback.beforeRename, [e.treeId, r, a, o], !0)) return !1;
            Re.nodeName(e, r, a), Pe(r, he.id.A, e).removeClass(he.node.CURSELECTED_EDIT), n.unbind(), be.setNodeName(e, r), r.editNameFlag = !1, d.curEditNode = null, d.curEditInput = null, be.selectNode(e, r, !1), e.treeObj.trigger(he.event.RENAME, [e.treeId, r, o]);
          }
          return d.noSelection = !0;
        },
        editNode: function editNode(t, e) {
          var o = Re.getRoot(t);
          if (be.editNodeBlur = !1, Re.isSelectedNode(t, e) && o.curEditNode == e && e.editNameFlag) setTimeout(function () {
            Ie.inputFocus(o.curEditInput);
          }, 0);else {
            e.editNameFlag = !0, be.removeTreeDom(t, e), be.cancelCurEditNode(t), be.selectNode(t, e, !1), Pe(e, he.id.SPAN, t).html("<input type=text class='rename' id='" + e.tId + he.id.INPUT + "' treeNode" + he.id.INPUT + " >");
            var d = Pe(e, he.id.INPUT, t);
            d.attr("value", Re.nodeName(t, e)), t.edit.editNameSelectAll ? Ie.inputSelect(d) : Ie.inputFocus(d), d.bind("blur", function (e) {
              be.editNodeBlur || be.cancelCurEditNode(t);
            }).bind("keydown", function (e) {
              "13" == e.keyCode ? (be.editNodeBlur = !0, be.cancelCurEditNode(t)) : "27" == e.keyCode && be.cancelCurEditNode(t, null, !0);
            }).bind("click", function (e) {
              return !1;
            }).bind("dblclick", function (e) {
              return !1;
            }), Pe(e, he.id.A, t).addClass(he.node.CURSELECTED_EDIT), o.curEditInput = d, o.noSelection = !1, o.curEditNode = e;
          }
        },
        moveNode: function moveNode(e, t, o, d, r, n) {
          var a = Re.getRoot(e);
          if (t != o && (!e.data.keep.leaf || !t || Re.nodeIsParent(e, t) || d != he.move.TYPE_INNER)) {
            var i = o.parentTId ? o.getParentNode() : a,
              l = null === t || t == a;
            l && null === t && (t = a), l && (d = he.move.TYPE_INNER);
            var s,
              c,
              N = t.parentTId ? t.getParentNode() : a;
            if (d != he.move.TYPE_PREV && d != he.move.TYPE_NEXT && (d = he.move.TYPE_INNER), d == he.move.TYPE_INNER && (l ? o.parentTId = null : (Re.nodeIsParent(e, t) || (Re.nodeIsParent(e, t, !0), t.open = !!t.open, be.setNodeLineIcos(e, t)), o.parentTId = t.tId)), l) c = s = e.treeObj;else {
              if (n || d != he.move.TYPE_INNER ? n || be.expandCollapseNode(e, t.getParentNode(), !0, !1) : be.expandCollapseNode(e, t, !0, !1), s = Pe(t, e), c = Pe(t, he.id.UL, e), s.get(0) && !c.get(0)) {
                var u = [];
                be.makeUlHtml(e, t, u, ""), s.append(u.join(""));
              }
              c = Pe(t, he.id.UL, e);
            }
            var v = Pe(o, e);
            v.get(0) ? s.get(0) || v.remove() : v = be.appendNodes(e, o.level, [o], null, -1, !1, !0).join(""), c.get(0) && d == he.move.TYPE_INNER ? c.append(v) : s.get(0) && d == he.move.TYPE_PREV ? s.before(v) : s.get(0) && d == he.move.TYPE_NEXT && s.after(v);
            var g,
              m,
              p = -1,
              T = 0,
              f = null,
              E = null,
              I = o.level,
              h = Re.nodeChildren(e, i),
              b = Re.nodeChildren(e, N),
              R = Re.nodeChildren(e, t);
            if (o.isFirstNode) p = 0, 1 < h.length && ((f = h[1]).isFirstNode = !0);else if (o.isLastNode) (f = h[(p = h.length - 1) - 1]).isLastNode = !0;else for (g = 0, m = h.length; g < m; g++) if (h[g].tId == o.tId) {
              p = g;
              break;
            }
            if (0 <= p && h.splice(p, 1), d != he.move.TYPE_INNER) for (g = 0, m = b.length; g < m; g++) b[g].tId == t.tId && (T = g);
            if (d == he.move.TYPE_INNER ? (0 < (R = R || Re.nodeChildren(e, t, [])).length && ((E = R[R.length - 1]).isLastNode = !1), R.splice(R.length, 0, o), o.isLastNode = !0, o.isFirstNode = 1 == R.length) : t.isFirstNode && d == he.move.TYPE_PREV ? (b.splice(T, 0, o), (E = t).isFirstNode = !1, o.parentTId = t.parentTId, o.isFirstNode = !0, o.isLastNode = !1) : t.isLastNode && d == he.move.TYPE_NEXT ? (b.splice(T + 1, 0, o), (E = t).isLastNode = !1, o.parentTId = t.parentTId, o.isFirstNode = !1, o.isLastNode = !0) : (d == he.move.TYPE_PREV ? b.splice(T, 0, o) : b.splice(T + 1, 0, o), o.parentTId = t.parentTId, o.isFirstNode = !1, o.isLastNode = !1), Re.fixPIdKeyValue(e, o), Re.setSonNodeLevel(e, o.getParentNode(), o), be.setNodeLineIcos(e, o), be.repairNodeLevelClass(e, o, I), !e.data.keep.parent && h.length < 1) {
              Re.nodeIsParent(e, i, !1), i.open = !1;
              var P = Pe(i, he.id.UL, e),
                C = Pe(i, he.id.SWITCH, e),
                w = Pe(i, he.id.ICON, e);
              be.replaceSwitchClass(i, C, he.folder.DOCU), be.replaceIcoClass(i, w, he.folder.DOCU), P.css("display", "none");
            } else f && be.setNodeLineIcos(e, f);
            E && be.setNodeLineIcos(e, E), e.check && e.check.enable && be.repairChkClass && (be.repairChkClass(e, i), be.repairParentChkClassWithSelf(e, i), i != o.parent && be.repairParentChkClassWithSelf(e, o)), n || be.expandCollapseParentNode(e, o.getParentNode(), !0, r);
          }
        },
        removeEditBtn: function removeEditBtn(e, t) {
          Pe(t, he.id.EDIT, e).unbind().remove();
        },
        removeRemoveBtn: function removeRemoveBtn(e, t) {
          Pe(t, he.id.REMOVE, e).unbind().remove();
        },
        removeTreeDom: function removeTreeDom(e, t) {
          t.isHover = !1, be.removeEditBtn(e, t), be.removeRemoveBtn(e, t), Ie.apply(e.view.removeHoverDom, [e.treeId, t]);
        },
        repairNodeLevelClass: function repairNodeLevelClass(e, t, o) {
          if (o !== t.level) {
            var d = Pe(t, e),
              r = Pe(t, he.id.A, e),
              n = Pe(t, he.id.UL, e),
              a = he.className.LEVEL + o,
              i = he.className.LEVEL + t.level;
            d.removeClass(a), d.addClass(i), r.removeClass(a), r.addClass(i), n.removeClass(a), n.addClass(i);
          }
        },
        selectNodes: function selectNodes(e, t) {
          for (var o = 0, d = t.length; o < d; o++) be.selectNode(e, t[o], 0 < o);
        }
      },
      event: {},
      data: {
        setSonNodeLevel: function setSonNodeLevel(e, t, o) {
          if (o) {
            var d = Re.nodeChildren(e, o),
              r = o.level;
            if (o.level = t ? t.level + 1 : 0, be.repairNodeLevelClass(e, o, r), d) for (var n = 0, a = d.length; n < a; n++) d[n] && Re.setSonNodeLevel(e, o, d[n]);
          }
        }
      }
    };
  fe.extend(!0, fe.fn.zTree.consts, Ee), fe.extend(!0, fe.fn.zTree._z, e);
  var t = fe.fn.zTree,
    Ie = t._z.tools,
    he = t.consts,
    be = t._z.view,
    Re = t._z.data,
    Pe = (t._z.event, Ie.$);
  Re.exSetting({
    edit: {
      enable: !1,
      editNameSelectAll: !1,
      showRemoveBtn: !0,
      showRenameBtn: !0,
      removeTitle: "remove",
      renameTitle: "rename",
      drag: {
        autoExpandTrigger: !1,
        isCopy: !0,
        isMove: !0,
        prev: !0,
        next: !0,
        inner: !0,
        minMoveSize: 5,
        borderMax: 10,
        borderMin: -5,
        maxShowNodeNum: 5,
        autoOpenTime: 500
      }
    },
    view: {
      addHoverDom: null,
      removeHoverDom: null
    },
    callback: {
      beforeDrag: null,
      beforeDragOpen: null,
      beforeDrop: null,
      beforeEditName: null,
      beforeRename: null,
      onDrag: null,
      onDragMove: null,
      onDrop: null,
      onRename: null
    }
  }), Re.addInitBind(function (i) {
    var e = i.treeObj,
      t = he.event;
    e.bind(t.RENAME, function (e, t, o, d) {
      Ie.apply(i.callback.onRename, [e, t, o, d]);
    }), e.bind(t.DRAG, function (e, t, o, d) {
      Ie.apply(i.callback.onDrag, [t, o, d]);
    }), e.bind(t.DRAGMOVE, function (e, t, o, d) {
      Ie.apply(i.callback.onDragMove, [t, o, d]);
    }), e.bind(t.DROP, function (e, t, o, d, r, n, a) {
      Ie.apply(i.callback.onDrop, [t, o, d, r, n, a]);
    });
  }), Re.addInitUnBind(function (e) {
    var t = e.treeObj,
      o = he.event;
    t.unbind(o.RENAME), t.unbind(o.DRAG), t.unbind(o.DRAGMOVE), t.unbind(o.DROP);
  }), Re.addInitCache(function (e) {}), Re.addInitNode(function (e, t, o, d, r, n, a) {
    o && (o.isHover = !1, o.editNameFlag = !1);
  }), Re.addInitProxy(function (e) {
    var t = e.target,
      o = Re.getSetting(e.data.treeId),
      d = e.relatedTarget,
      r = "",
      n = null,
      a = "",
      i = null,
      l = null;
    if (Ie.eqs(e.type, "mouseover") ? (l = Ie.getMDom(o, t, [{
      tagName: "a",
      attrName: "treeNode" + he.id.A
    }])) && (r = Ie.getNodeMainDom(l).id, a = "hoverOverNode") : Ie.eqs(e.type, "mouseout") ? (l = Ie.getMDom(o, d, [{
      tagName: "a",
      attrName: "treeNode" + he.id.A
    }])) || (r = "remove", a = "hoverOutNode") : Ie.eqs(e.type, "mousedown") && (l = Ie.getMDom(o, t, [{
      tagName: "a",
      attrName: "treeNode" + he.id.A
    }])) && (r = Ie.getNodeMainDom(l).id, a = "mousedownNode"), 0 < r.length) switch (n = Re.getNodeCache(o, r), a) {
      case "mousedownNode":
        i = s.onMousedownNode;
        break;
      case "hoverOverNode":
        i = s.onHoverOverNode;
        break;
      case "hoverOutNode":
        i = s.onHoverOutNode;
    }
    return {
      stop: !1,
      node: n,
      nodeEventType: a,
      nodeEventCallback: i,
      treeEventType: "",
      treeEventCallback: null
    };
  }), Re.addInitRoot(function (e) {
    var t = Re.getRoot(e),
      o = Re.getRoots();
    t.curEditNode = null, t.curEditInput = null, t.curHoverNode = null, t.dragFlag = 0, t.dragNodeShowBefore = [], t.dragMaskList = new Array(), o.showHoverDom = !0;
  }), Re.addZTreeTools(function (l, e) {
    e.cancelEditName = function (e) {
      Re.getRoot(this.setting).curEditNode && be.cancelCurEditNode(this.setting, e || null, !0);
    }, e.copyNode = function (e, t, o, d) {
      if (!t) return null;
      var r = Re.nodeIsParent(l, e);
      if (e && !r && this.setting.data.keep.leaf && o === he.move.TYPE_INNER) return null;
      var n = this,
        a = Ie.clone(t);
      if (e || (e = null, o = he.move.TYPE_INNER), o == he.move.TYPE_INNER) {
        var _i2 = function _i2() {
          be.addNodes(n.setting, e, -1, [a], d);
        };
        Ie.canAsync(this.setting, e) ? be.asyncNode(this.setting, e, d, _i2) : _i2();
      } else be.addNodes(this.setting, e.parentNode, -1, [a], d), be.moveNode(this.setting, e, a, o, !1, d);
      return a;
    }, e.editName = function (e) {
      e && e.tId && e === Re.getNodeCache(this.setting, e.tId) && (e.parentTId && be.expandCollapseParentNode(this.setting, e.getParentNode(), !0), be.editNode(this.setting, e));
    }, e.moveNode = function (e, t, o, d) {
      if (!t) return t;
      var r = Re.nodeIsParent(l, e);
      if (e && !r && this.setting.data.keep.leaf && o === he.move.TYPE_INNER) return null;
      if (e && (t.parentTId == e.tId && o == he.move.TYPE_INNER || 0 < Pe(t, this.setting).find("#" + e.tId).length)) return null;
      e = e || null;
      var n = this;
      function a() {
        be.moveNode(n.setting, e, t, o, !1, d);
      }
      return Ie.canAsync(this.setting, e) && o === he.move.TYPE_INNER ? be.asyncNode(this.setting, e, d, a) : a(), t;
    }, e.setEditable = function (e) {
      return this.setting.edit.enable = e, this.refresh();
    };
  });
  var n = be.cancelPreSelectedNode;
  be.cancelPreSelectedNode = function (e, t) {
    for (var o = Re.getRoot(e).curSelectedList, d = 0, r = o.length; d < r && (t && t !== o[d] || (be.removeTreeDom(e, o[d]), !t)); d++);
    n && n.apply(be, arguments);
  };
  var a = be.createNodes;
  be.createNodes = function (e, t, o, d, r) {
    a && a.apply(be, arguments), o && be.repairParentChkClassWithSelf && be.repairParentChkClassWithSelf(e, d);
  };
  var o = be.makeNodeUrl;
  be.makeNodeUrl = function (e, t) {
    return e.edit.enable ? null : o.apply(be, arguments);
  };
  var d = be.removeNode;
  be.removeNode = function (e, t) {
    var o = Re.getRoot(e);
    o.curEditNode === t && (o.curEditNode = null), d && d.apply(be, arguments);
  };
  var r = be.selectNode;
  be.selectNode = function (e, t, o) {
    var d = Re.getRoot(e);
    return (!Re.isSelectedNode(e, t) || d.curEditNode != t || !t.editNameFlag) && (r && r.apply(be, arguments), be.addHoverDom(e, t), !0);
  };
  var i = Ie.uCanDo;
  Ie.uCanDo = function (e, t) {
    var o = Re.getRoot(e);
    return !(!t || !(Ie.eqs(t.type, "mouseover") || Ie.eqs(t.type, "mouseout") || Ie.eqs(t.type, "mousedown") || Ie.eqs(t.type, "mouseup"))) || (o.curEditNode && (be.editNodeBlur = !1, o.curEditInput.focus()), !o.curEditNode && (!i || i.apply(be, arguments)));
  };
}(jQuery);
