"use strict";

/**
 * 自定义着重号
 */
function selectionStyle(className) {
  var selection = window.getSelection();
  if (!selection) return;
  var range = selection.getRangeAt(0);
  var optionClass = className;
  var parentNode = range.startContainer.parentElement;
  if (parentNode.classList.contains(optionClass)) {
    parentNode.outerHTML = parentNode.innerText;
    return;
  }
  var spanNode = document.createElement("span");
  var rangeString = range.toString();
  spanNode.className = optionClass;
  range.surroundContents(spanNode);
  spanNode.innerText = rangeString;
}
CKEDITOR.plugins.add("UnderDot", {
  init: function init(editor) {
    editor.ui.addButton("UnderDot", {
      label: "着重号",
      icon: this.path + "icon.png",
      command: "UnderDot",
      click: function click(e) {
        selectionStyle("tb-dot");
      }
    });
    editor.ui.addButton("SolidWaveline", {
      label: '波浪线',
      icon: this.path + "wave.png",
      command: "SolidWaveline",
      click: function click(e) {
        selectionStyle("tb-wave");
      }
    });
  }
});
