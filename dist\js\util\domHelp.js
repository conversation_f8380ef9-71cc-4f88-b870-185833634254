"use strict";

window.DomHelp = {
  removeEmptyTags: function removeEmptyTags(e) {
    var n = ["img"].join("|"),
      r = new RegExp("<(?!(?:".concat(n, ")\\b)([a-z]+)(?:[^>]*?)>\\s*</\\1>"), "gi");
    var t = "",
      i = e;
    for (; t !== i;) i = (t = i).replace(r, "");
    return i;
  },
  fixIncompleteHtml: function fixIncompleteHtml(e) {
    var n = document.createElement("div"),
      e = (n.innerHTML = e, this.removeEmptyTags(n.innerHTML));
    return e;
  }
};
