<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">89.44% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1947/2177</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">79.45% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>816/1027</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">89.06% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>228/256</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">89.53% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1941/2168</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="lib/"><a href="lib/index.html">lib/</a></td>
	<td data-value="87.09" class="pic high"><div class="chart"><div class="cover-fill" style="width: 87%;"></div><div class="cover-empty" style="width:13%;"></div></div></td>
	<td data-value="87.09" class="pct high">87.09%</td>
	<td data-value="581" class="abs high">506/581</td>
	<td data-value="75.55" class="pct medium">75.55%</td>
	<td data-value="274" class="abs medium">207/274</td>
	<td data-value="84.15" class="pct high">84.15%</td>
	<td data-value="82" class="abs high">69/82</td>
	<td data-value="87.09" class="pct high">87.09%</td>
	<td data-value="581" class="abs high">506/581</td>
	</tr>

<tr>
	<td class="file high" data-value="lib/protocol/"><a href="lib/protocol/index.html">lib/protocol/</a></td>
	<td data-value="90.29" class="pic high"><div class="chart"><div class="cover-fill" style="width: 90%;"></div><div class="cover-empty" style="width:10%;"></div></div></td>
	<td data-value="90.29" class="pct high">90.29%</td>
	<td data-value="1596" class="abs high">1441/1596</td>
	<td data-value="80.88" class="pct high">80.88%</td>
	<td data-value="753" class="abs high">609/753</td>
	<td data-value="91.38" class="pct high">91.38%</td>
	<td data-value="174" class="abs high">159/174</td>
	<td data-value="90.42" class="pct high">90.42%</td>
	<td data-value="1587" class="abs high">1435/1587</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Aug 23 2017 13:12:39 GMT-0700 (PDT)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
