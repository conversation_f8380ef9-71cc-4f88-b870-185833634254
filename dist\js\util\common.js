"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
!function (e) {
  var t = {
    data: {
      aliba: "127.0.0.1" == location.hostname ? "https://fs.iclass30.com" : ""
    },
    methods: {
      bubbleSort: function bubbleSort(e) {
        for (var t = e.length - 1; 0 < t; t--) for (var n, a = 0; a < t; a++) e[a] > e[a + 1] && (n = e[a], e.splice(a, 1, e[a + 1]), e.splice(a + 1, 1, n));
      },
      arrange: function arrange(e) {
        var t,
          n,
          a = [];
        return e.forEach(function (e) {
          t === e ? (n.push(t), t++) : (n = [e], t = e + 1, a.push(n));
        }), a;
      },
      getHtmlCont: function getHtmlCont(e) {
        $(e).each(function () {
          var n = $(this),
            e = (n.html("正在加载中..."), n.data("htmlurl"));
          "" != e && ajax(t.data.aliba + e, {}, !0, "get", "html").done(function (e) {
            var t = $(document.createElement("div")).append(e),
              a = e,
              e = ($(".ck-math-tex", t).each(function (e, t) {
                a = a.replace(t.outerHTML, '<span tex="' + t.getAttribute("tex") + '" contenteditable="false" class="ck-math-tex">' + t.getAttribute("tex") + "</span>");
              }), $(".math-show", t).each(function (e, t) {
                0 < $(t).prev(".MathJax_Preview").length && (a = a.replace($(t).prev(".MathJax_Preview").get(0).outerHTML, ""));
                var n = "<span tex=\"\\(".concat($(t).find(".mathTex").text(), "\\)\" contenteditable=\"false\" class=\"ck-math-tex\">\\(").concat($(t).find(".mathTex").text(), "\\)</span>");
                a = a.replace($(t).get(0).outerHTML, n);
              }), $(n).data("qid"));
            sessionStorage.setItem("matchinfo_qtext_" + e, a), n.html(a), MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
          }).fail(function () {
            n.html("加载失败...");
          });
        });
      }
    }
  };
  e.jcUtils = t.methods;
}(window);
var headerEvent = {
    pageJump: function pageJump(e, t) {
      if (-1 < window.location.pathname.indexOf("matchQues.html")) {
        var n = !0;
        if (0 == $(".r-que").length) layer.msg("尚未制作题目!");else {
          for (var a, r = $(".select-ul,.judge-ul"), s = 0; s < r.length; s++) if (0 == $(r[s]).find(".selected").length) n = !1;else if (8 == $(r[s]).parent().data("type") && 1 < $(r[s]).find(".selected").length) return a = $(r[s]).parents(".r-que").find(".tit-show .tit").text(), void layer.msg(a + "为单选题，只能设置一个正确答案，请修正答案或题型");
          0 == (n = 0 < $(".fill-auto-li.set-answer").length ? !1 : n) ? layer.msg("客观题答案尚未设置完成!") : headerEvent.updateCutHtmlSort(e, t);
        }
      } else headerEvent.pageJumpEvent(e, t);
    },
    pageJumpEvent: function pageJumpEvent(e, t) {
      var n = "";
      save17Data(t, 0, function () {
        1 == e ? ($(".title-wrap li.qs-deline", window.parent.document).addClass("active").siblings().removeClass("active"), n = "../cutTestPaper/matchQues.html" + window.location.search) : 2 == e ? ($(".title-wrap li.qs-answer", window.parent.document).addClass("active").siblings().removeClass("active"), n = "../cutTestPaper/matchAns.html" + window.location.search) : 3 == e && ($(".title-wrap li.qs-mark", window.parent.document).addClass("active").siblings().removeClass("active"), n = "../cutTestPaper/matchInfo.html" + window.location.search), window.location.replace(n);
      });
    },
    updateCutHtmlSort: function updateCutHtmlSort(e, t) {
      if (1 == CutHtml.v.manualState) {
        var n = $("#htmlDiv .sliceMainClass");
        if (0 < n.length) {
          for (var a = "", r = 0; r < n.length; r++) "" == a ? a = $(n[r]).attr("id") : a += "," + $(n[r]).attr("id");
          ajax("/testbank/testBank/batchUpdateCutHtmlSort", {
            cutlist: a
          }, !1).done(function (e) {});
        }
      }
      headerEvent.pageJumpEvent(e, t);
    }
  },
  CommonJs = {
    v: {
      network: "https://fs.iclass30.com",
      admin: ",1emcaa-oorvkzh7smvpifw||,-cpcaaanvz1cqqdmcgrlog,ac1caaanhahmdtecwfsvqq,fszcaaan2pvbuv-lmxudgq,jc5caaanxbrb0jvut6uxtg,ocpcaaanfa1jt0ewph8vsq,tytcaaan2ipbhxm-hs8qaw"
    },
    methods: {
      replaceALiUrl: function replaceALiUrl(e) {
        var t;
        return e ? (t = CommonJs.v.network, e.startsWith("http") ? e : e.startsWith("/") ? t + e : t + "/" + e) : "";
      },
      numberConvert: function numberConvert(e) {
        var t,
          n,
          a = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
        return 1 == e.length ? e = a[e] : 2 == e.length && (t = e.substring(0, 1), n = e.substring(1), e = "1" == t ? "十" + a[n] : a[e.substring(0, 1)] + "十" + a[e.substring(1)]), e.replace("零", "");
      },
      getSubjectBot: function getSubjectBot(e) {
        var t = "";
        switch (e) {
          case "1":
            t = "初中语文";
            break;
          case "2":
            t = "bot-20250514095347-9bttj";
            break;
          case "3":
            t = "初中英语";
            break;
          case "4":
            t = "初中物理";
            break;
          case "5":
            t = "初中化学";
            break;
          case "6":
            t = "初中生物";
            break;
          case "7":
            t = "初中思品";
            break;
          case "8":
            t = "初中历史";
            break;
          case "9":
            t = "初中地理";
            break;
          case "10":
            t = "高中语文";
            break;
          case "11":
            t = "bot-20250514095638-c6tcb";
            break;
          case "12":
            t = "高中英语";
            break;
          case "13":
            t = "高中物理";
            break;
          case "14":
            t = "高中化学";
            break;
          case "15":
            t = "高中生物";
            break;
          case "16":
            t = "高中政治";
            break;
          case "17":
            t = "高中历史";
            break;
          case "18":
            t = "高中地理";
            break;
          case "24":
            t = "小学语文";
            break;
          case "23":
            t = "小学数学";
            break;
          case "25":
            t = "小学英语";
            break;
          case "19":
            t = "小学科学";
            break;
          case "233":
            t = "小学道德与法治";
            break;
          default:
            t = "未知";
        }
        return t;
      },
      isObjective: function isObjective(e) {
        return 8 == e || 1 == e || 2 == e || 5 == e || 7 == e;
      },
      getVolcengineToken: function getVolcengineToken() {
        return ajax("https://kklservicetest.iclass30.com/pscan/coze/getVolcengineToken", {
          botIds: this.getSubjectBot(getQueryString("subject"))
        }, !1);
      },
      getAiQuesKnowledge: function getAiQuesKnowledge(e, t) {
        t = {
          "Content-Type": "application/json",
          Authorization: "Bearer " + t
        }, e = {
          messages: [{
            content: e,
            role: "user"
          }],
          model: this.getSubjectBot(getQueryString("subject"))
        };
        return fetch("https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions", {
          method: "POST",
          headers: t,
          body: JSON.stringify(e)
        });
      }
    }
  };
function layout() {
  var e = $(window).height() - $(".top").outerHeight() - $(".foot").outerHeight();
  $(".main-body").css("minHeight", e);
}
function getQueryString(e) {
  e = new RegExp("(^|&)" + e + "=([^&]*)(&|$)", "i"), e = window.location.search.substr(1).match(e);
  return null != e ? decodeURI(e[2]) : null;
}
$(function () {
  layout(), $(".user-title").click(function () {
    $(this).parent(".user-info").is(".open") ? ($(this).parent(".user-info").removeClass("open"), $(this).parent(".user-info").find(".user-list").slideUp()) : ($(this).parent(".user-info").addClass("open"), $(this).parent(".user-info").find(".user-list").slideDown());
  }), $(document).bind("click", function () {
    $(".user-info").removeClass("open"), $(".user-list").slideUp();
  }), $(".user-info").click(function (e) {
    e.stopPropagation();
  }), window.location.host.substring(0, 5);
}), $(window).resize(function () {
  layout();
}), String.prototype.format = function (e) {
  if (0 < arguments.length) {
    var t = this;
    if (1 == arguments.length && "object" == _typeof(e)) for (var n in e) var a = new RegExp("\\{" + n + "\\}", "g"), t = t.replace(a, e[n]);else for (var r = 0; r < arguments.length; r++) {
      if (null == arguments[r]) return "";
      a = new RegExp("\\{" + r + "\\}", "g");
      t = t.replace(a, arguments[r]);
    }
    return t;
  }
  return this;
};
var ObjToMap = function ObjToMap(obj, keyName) {
    var strMap = new Map();
    return obj.forEach(function (value) {
      strMap.set(eval("value." + keyName), value);
    }), strMap;
  },
  MapToObj = function MapToObj(e) {
    var t = new Array();
    return e.forEach(function (e) {
      t.push(e);
    }), t;
  },
  getConvertMathHtml = function getConvertMathHtml(e) {},
  helpCarousel = (String.prototype.replaceAll = function (e, t) {
    return regExp = new RegExp(e, "g"), this.replace(regExp, t);
  }, Array.prototype["delete"] = function (e) {
    for (var t = 0; t < this.length; t++) this[t] == e && this.splice(t, 1);
  }, Array.prototype.get = function (e) {
    for (var t = 0; t < this.length; t++) if (this[t].id == e) return this[t];
    return null;
  }, {
    n: 1,
    dir: 1,
    img_num: 0,
    init: function init() {
      helpCarousel.n = 1, helpCarousel.dir = 1, helpCarousel.img_num = $(".scrollobj img").length - 1, $(".btnLeft").click(function () {
        helpCarousel.runR();
      }), $(".btnRight").click(function () {
        helpCarousel.runL();
      }), $(".ctrl li").click(function () {
        n = $(this).index() + 1, $(".ctrl li").removeClass("current"), $(this).addClass("current"), $(".scrollobj").stop(!0, !0).animate({
          left: -700 * n
        }, 1e3);
      });
    },
    runL: function runL() {
      helpCarousel.dir = 1, helpCarousel.n < helpCarousel.img_num ? helpCarousel.n = helpCarousel.n + 1 : (helpCarousel.n = 2, $(".scrollobj").css({
        left: -700 * (helpCarousel.n - 1)
      })), $(".scrollobj").stop(!0, !0).animate({
        left: -700 * helpCarousel.n
      }, 1e3), $(".ctrl li").removeClass("current"), (helpCarousel.n == helpCarousel.img_num ? $(".ctrl li").eq(0) : $(".ctrl li").eq(helpCarousel.n - 1)).addClass("current");
    },
    runR: function runR() {
      (helpCarousel.dir = 0) < helpCarousel.n ? helpCarousel.n = helpCarousel.n - 1 : (helpCarousel.n = 2, $(".scrollobj").css({
        left: -700 * (helpCarousel.n + 1)
      })), $(".scrollobj").stop(!0, !0).animate({
        left: -700 * helpCarousel.n
      }, 1e3), $(".ctrl li").removeClass("current"), $(".ctrl li").eq(helpCarousel.n - 1).addClass("current");
    }
  });
function intToChinese(e) {
  var i = (e += "").length - 1,
    l = ["", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万", "十", "百", "千", "亿"],
    c = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  return e.replace(/([1-9]|0+)/g, function (e, t, n, a) {
    var r,
      s,
      o = 0;
    return "0" != t[0] ? (o = i - n, 0 == n && 1 == t[0] && "十" == l[i - n] ? l[i - n] : c[t[0]] + l[i - n]) : (s = (r = i - n) + t.length, (o = 0 < Math.floor(s / 4) - Math.floor(r / 4) ? r - r % 4 : o) ? l[o] + c[t[0]] : n + t.length >= i ? "" : c[t[0]]);
  });
}
function isNullOrUndefined(e) {
  return null == e || void 0 === e || "" === e || "undefined" === e;
}
String.prototype.trim = function () {
  for (var e = this, t = /\s/, n = (e = e.replace(/^\s\s*/, "")).length; t.test(e.charAt(--n)););
  return e.slice(0, n + 1);
};
var save17Data = function save17Data(e, t, n) {
    var a = sessionStorage.getItem("cut17Data_" + e),
      a = (console.log("前：" + a), a.replace(/[\r\n]/g, "").replace(/(?<!<u[^>]*>)\s*&nbsp;\s*(?![^<]*<\/u>)/g, " "));
    console.log("后：" + a), ajax("/testbank/TBQuestion/saveBigQuestionStructure", {
      testBankId: e,
      source: t || 0,
      qJson: a
    }, !0, "POST", "json", "raw").done(function (e) {
      1 == e.code ? n() : layer.msg(e.msg);
    });
  },
  get17Data = function get17Data(t) {
    ajax("/testbank/TBQuestion/getTestBankQuestionInfo", {
      testBankId: t
    }).done(function (e) {
      1 == e.code && sessionStorage.setItem("cut17Data_" + t, JSON.stringify(e.data));
    });
  },
  kklPreviewDialog = function kklPreviewDialog(e) {
    var t = "https://homeworkservice.iclass30.com/",
      e = (getQueryString("url").match("test") && !getQueryString("url").match("xwtest") && (t = "https://test.iclass30.com/"), "../cutTestPaper/preview.html?testBankId=" + e + "&token=" + getQueryString("token") + "&url=" + t);
    layer.open({
      type: 2,
      title: "学生端预览",
      area: ["600px", "550px"],
      closeBtn: 1,
      fix: !0,
      shade: .3,
      scrollbar: !0,
      resize: !0,
      moveOut: !1,
      content: e
    });
  },
  PostMessageToParent = function PostMessageToParent(e) {
    window.parent.postMessage(e, "*");
  },
  isNoAutoSubj = function isNoAutoSubj(e) {
    return "12" == e || "3" == e || "1" == e || "10" == e;
  },
  isEnglish = function isEnglish(e) {
    return "3" == e || "12" == e || "25" == e;
  };
function isMQKeyBoard(e) {
  return -1 < $.inArray(e, ["2", "4", "5", "6", "9", "11", "13", "14", "15", "18", "23"]);
}
String.prototype.Trim = function (e) {
  if (null == e || "" == e) for (var t = /s/, n = (a = this.replace(/^s*/, "")).length; t.test(a.charAt(--n)););else for (var t = new RegExp("^" + e + "*"), a = this.replace(t, ""), n = (t = new RegExp(e), a.length); t.test(a.charAt(--n)););
  return a.slice(0, n + 1);
}, String.prototype.TrimStart = function (e) {
  return null == e || "" == e ? this.replace(/^s*/, "") : (e = new RegExp("^" + e + "*"), this.replace(e, ""));
}, String.prototype.trimEnd = function (e) {
  if (null == e || "" == e) for (var t = /s/, n = (a = this).length; t.test(a.charAt(--n)););else for (var a = this, t = new RegExp(e), n = a.length; t.test(a.charAt(--n)););
  return a.slice(0, n + 1);
};
