/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'tr', {
	alt: 'Alternatif Yazı',
	border: 'Kenar',
	btnUpload: 'Sunucuya Yolla',
	button2Img: 'Seçili resim butonunu basit resime çevirmek istermisiniz?',
	hSpace: 'Ya<PERSON><PERSON>',
	img2Button: '<PERSON><PERSON><PERSON> olan resimi, resimli butona çevirmek istermisiniz?',
	infoTab: 'Resim Bilgisi',
	linkTab: '<PERSON>öpr<PERSON>',
	lockRatio: '<PERSON>an<PERSON> Kilitle',
	menu: 'Resim Özellikleri',
	resetSize: 'Boyutu Başa Döndür',
	title: 'Resi<PERSON> Özellikleri',
	titleButton: '<PERSON>si<PERSON><PERSON> Düğme Özellikleri',
	upload: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	urlMissing: 'Resmin URL kaynağı eksiktir.',
	vSpace: 'Dikey Boşluk',
	validateBorder: 'Çerçeve tam sayı olmalıdır.',
	validateHSpace: 'HSpace tam sayı olmalıdır.',
	validateVSpace: 'VSpace tam sayı olmalıdır.'
} );
