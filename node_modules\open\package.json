{"_from": "open@^8.0.0", "_id": "open@8.4.2", "_inBundle": false, "_integrity": "sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==", "_location": "/open", "_phantomChildren": {"is-docker": "2.2.1"}, "_requested": {"type": "range", "registry": true, "raw": "open@^8.0.0", "name": "open", "escapedName": "open", "rawSpec": "^8.0.0", "saveSpec": null, "fetchSpec": "^8.0.0"}, "_requiredBy": ["/grunt-contrib-connect"], "_resolved": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "_shasum": "5b5ffe2a8f793dcd2aad73e550cb87b59cb084f9", "_spec": "open@^8.0.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\grunt-contrib-connect", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/open/issues"}, "bundleDependencies": false, "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "deprecated": false, "description": "Open stuff like URLs, files, executables. Cross-platform.", "devDependencies": {"@types/node": "^15.0.0", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "engines": {"node": ">=12"}, "files": ["index.js", "index.d.ts", "xdg-open"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/open#readme", "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "license": "MIT", "name": "open", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/open.git"}, "scripts": {"test": "xo && tsd"}, "version": "8.4.2"}