"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang('image', 'az', {
  alt: 'Alternativ mətn',
  border: 'Sərhəd',
  btnUpload: 'Serverə yüklə',
  button2Img: 'Şəkil tipli düyməni şəklə çevirmək istədiyinizə əminsinizmi?',
  hSpace: 'Üfüqi boşluq',
  img2Button: 'Şəkli şəkil tipli düyməyə çevirmək istədiyinizə əminsinizmi?',
  infoTab: 'Şəkil haqqında məlumat',
  linkTab: 'Link',
  lockRatio: 'Ölçülərin uyğunluğu saxla',
  menu: '<PERSON>ək<PERSON> seçimləri',
  resetSize: 'Ö<PERSON><PERSON><PERSON><PERSON>ə<PERSON> qaytar',
  title: '<PERSON><PERSON><PERSON><PERSON> seçimləri',
  titleButton: '<PERSON>əkil tipli düyməsinin seçimləri',
  upload: 'Serverə yüklə',
  urlMissing: 'Şəklin ünvanı yanlışdır.',
  vSpace: 'Şaquli boşluq',
  validateBorder: 'Sərhədin eni rəqəm olmalıdır.',
  validateHSpace: 'Üfüqi boşluq rəqəm olmalıdır.',
  validateVSpace: 'Şaquli boşluq rəqəm olmalıdır.'
});
