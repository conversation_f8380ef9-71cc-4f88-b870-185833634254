{"_from": "ultron@~1.1.0", "_id": "ultron@1.1.1", "_inBundle": false, "_integrity": "sha512-UIEXBNeYmKptWH6z8ZnqTeS8fV74zG0/eRU9VGkpzz+LIJNs8W/zM/L+7ctCkRrgbNnnR0xxw4bKOr0cW0N0Og==", "_location": "/ultron", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ultron@~1.1.0", "name": "ultron", "escapedName": "ultron", "rawSpec": "~1.1.0", "saveSpec": null, "fetchSpec": "~1.1.0"}, "_requiredBy": ["/ws"], "_resolved": "https://registry.npmjs.org/ultron/-/ultron-1.1.1.tgz", "_shasum": "9fe1536a10a664a65266a1e3ccf85fd36302bc9c", "_spec": "ultron@~1.1.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\ws", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/unshiftio/ultron/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Ultron is high-intelligence robot. It gathers intel so it can start improving upon his rudimentary design", "devDependencies": {"assume": "~1.5.0", "eventemitter3": "2.0.x", "istanbul": "0.4.x", "mocha": "~4.0.0", "pre-commit": "~1.2.0"}, "homepage": "https://github.com/unshiftio/ultron", "keywords": ["Ultron", "robot", "gather", "intelligence", "event", "events", "eventemitter", "emitter", "cleanup"], "license": "MIT", "main": "index.js", "name": "ultron", "repository": {"type": "git", "url": "git+https://github.com/unshiftio/ultron.git"}, "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "coverage": "istanbul cover _mocha -- test.js", "test": "mocha test.js", "test-travis": "istanbul cover _mocha --report lcovonly -- test.js", "watch": "mocha --watch test.js"}, "version": "1.1.1"}