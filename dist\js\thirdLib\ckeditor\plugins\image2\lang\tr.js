"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang('image2', 'tr', {
  alt: 'Alternatif Yazı',
  btnUpload: '<PERSON><PERSON><PERSON><PERSON>lla',
  captioned: 'Başlıklı resim',
  captionPlaceholder: '<PERSON><PERSON>lı<PERSON>',
  infoTab: 'Resim Bilgisi',
  lockRatio: '<PERSON><PERSON><PERSON>',
  menu: 'Resim Özellikleri',
  pathName: 'Resim',
  pathNameCaption: 'başlık',
  resetSize: '<PERSON>utu Başa Döndür',
  resizer: 'Boyutlandırmak için, tıklayın ve sürükleyin',
  title: '<PERSON>si<PERSON> Ö<PERSON>likleri',
  uploadTab: '<PERSON><PERSON><PERSON><PERSON><PERSON> Yü<PERSON>',
  urlMissing: '<PERSON><PERSON><PERSON> URL kaynağı bulunamadı.',
  altMissing: 'Alternatif yazı eksik.'
});
