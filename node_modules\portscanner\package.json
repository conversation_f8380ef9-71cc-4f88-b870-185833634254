{"_from": "portscanner@^2.2.0", "_id": "portscanner@2.2.0", "_inBundle": false, "_integrity": "sha512-IFroCz/59Lqa2uBvzK3bKDbDDIEaAY8XJ1jFxcLWTqosrsc32//P4VuSB2vZXoHiHqOmx8B5L5hnKOxL/7FlPw==", "_location": "/portscanner", "_phantomChildren": {"lodash": "4.17.21"}, "_requested": {"type": "range", "registry": true, "raw": "portscanner@^2.2.0", "name": "portscanner", "escapedName": "portscanner", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/grunt-contrib-connect"], "_resolved": "https://registry.npmjs.org/portscanner/-/portscanner-2.2.0.tgz", "_shasum": "6059189b3efa0965c9d96a56b958eb9508411cf1", "_spec": "portscanner@^2.2.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\grunt-contrib-connect", "author": "", "bugs": {"url": "https://github.com/baalexander/node-portscanner/issues"}, "bundleDependencies": false, "dependencies": {"async": "^2.6.0", "is-number-like": "^1.0.3"}, "deprecated": false, "description": "Asynchronous port scanner for Node.js", "devDependencies": {"ava": "^0.4.2", "eslint": "^3.10.2", "eslint-config-standard": "^6.2.1", "nyc": "^11.3.0", "standard": "^8.5.0"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.4", "npm": ">=1.0.0"}, "homepage": "https://github.com/baalexander/node-portscanner", "keywords": ["portscanner", "port", "scanner", "checker", "status"], "license": "MIT", "main": "./lib/portscanner.js", "name": "portscanner", "preferGlobal": false, "repository": {"type": "git", "url": "git://github.com/baalexander/node-portscanner.git"}, "scripts": {"coverage": "nyc npm run test", "lint": "standard", "test": "ava"}, "version": "2.2.0"}