<!DOCTYPE html><head><meta charset="UTF-8"><title>课堂3.0-制卷</title><link rel="stylesheet" href="css/base.css"><link rel="stylesheet" href="css/testbank/common.css"><link rel="stylesheet" href="css/testbank/testbank.css"><link rel="stylesheet" href="js/thirdLib/layui/css/layui.css"><link rel="stylesheet" href="css/avalonComponent.css"><link rel="stylesheet" href="css/testbank/matchQues.css"><link rel="stylesheet" href="css/testbank/help.css"><style>#htmlDiv img{border:1px solid #3676ff!important;background:#fff}#htmlDiv img:hover{border:1px solid #3676ff!important;background:#fff}#htmlDiv .letter-tag{border:1px solid #13b1004f!important;background:#13b1004f}.wrs_editor .wrs_tickContainer{display:none}#htmlDiv .ck-math-tex{user-select:none;-webkit-user-select:none}</style><script src="js/thirdLib/<EMAIL>"></script><script defer="defer" type="text/javascript" src="https://fs.iclass30.com/aliba/plug/mathjax/MathJax.js"></script><script type="text/x-mathjax-config">MathJax.Hub.Register.StartupHook("TeX Jax Ready", function () {
            var TEX = MathJax.InputJax.TeX;
            // 注册预处理钩子，在处理 TeX 公式前执行
            TEX.prefilterHooks.Add(function (data) {
                // 判断是否是行内公式（非 display 模式）
                if (!data.display) {
                    // 给行内公式前面添加 \displaystyle 
                    data.math = '\\displaystyle ' + data.math;
                }
                return data;
            });
        });
        MathJax.Hub.Config({
            showProcessingMessages: false,
            messageStyle: "none",
            menuSettings: {
              context: "Browser"
            },
            "HTML-CSS": {
              webFont: "STIX-Web",
              availableFonts: ["STIX-Web"],
              preferredFont: "STIX-Web",
              styles: {".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
              undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
              scale:110
            },
            "CommonHTML": {
              webFont: "STIX-Web",
              availableFonts: ["STIX-Web"],
              preferredFont: "STIX-Web",
              styles: {".MathJax_Preview": {visibility: "hidden"},".MJXc-processed":{"display":"inline-block"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
              undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
              scale:110
            },
            tex2jax: {
              inlineMath: [['$$','$$'],["\\(","\\)"],["\\[", "\\]"]],
              processEscapes: true,
              skipTags: ['script', 'style',  'pre', 'code']
            },
            MMLorHTML: {prefer: "HTML"},
            jax: ["input/TeX","output/CommonHTML"],
            extensions: ["tex2jax.js","mml2jax.js","MathMenu.js","MathZoom.js", "fast-preview.js", "AssistiveMML.js"],
            TeX: {
                extensions: ["extpfeil.js","AMSmath.js","AMSsymbols.js","noErrors.js","noUndefined.js","cancel.js","mhchem.js","autoload-all.js"]
            },
            font:"STIX-Web",
      
            SVG: {
              scale: 110,
              linebreaks: {
                   automatic: true
              },
              addMMLclasses: false
            }
          });</script></head><body><div><div class="header-mask" id="header-mask"><div class="loading-cover"></div><div class="loading-box"><div class="img"></div><div class="loading-txt"></div></div></div><div class="main"><div class="cur-opr-wrap"><div class="cur-opr-box"><div class="btn-box-top" style=""><div class="cut-left-top"><div class="tip-box"><div class="tip"><span class="blue"></span> <a>: 题目</a></div><div class="tip"><span class="red"></span> <a>: 答案解析</a></div><div class="tip"><span class="img"><svg class="icon" style="width:45px;height:35px;vertical-align:middle;fill:currentColor;overflow:hidden" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="722"><path d="M928 192H96c-17.6 0-32 14.4-32 32v576c0 17.6 14.4 32 32 32h832c17.6 0 32-14.4 32-32V224c0-17.6-14.4-32-32-32zM192 416c0-52.8 43.2-96 96-96s96 43.2 96 96-43.2 96-96 96-96-43.2-96-96z m384 288H317.6l128.8-224L520 607.2l128-221.6L832 704H576z" p-id="723"></path></svg></span><a>: 图片</a></div></div></div><div class="skip topButton return" id="return"><span class="icon"></span> <span class="txt">返回列表</span></div><div class="siblin"><span class="topButton next" id="done"><span class="txt">下一步</span></span></div><div class="siblin-blue"><span class="topButton" id="clear">清空划题</span> <span class="topButton" id="auto">重新划题</span> <span class="topButton" id="manual">手动划题</span> <span class="topButton" id="editor">编辑试卷内容</span> <span class="topButton" id="batch">批量修改</span></div></div></div></div><div class="cut-main" ms-controller="cut-main"><div class="cut-left"><div class="word-area"><ms-page-state ms-widget="{state:@dataState}"></ms-page-state><h1 class="testBank-title" id="testPaperName"></h1><div id="htmlDiv" class="cutImageArea"></div></div></div><div class="cut-right"><div id="topicMask" class="set-que-win-bg" style="display:none"></div><div class="r-hd-box"><div class="blue-tip-line"></div><span class="txt">题目预览</span> <span class="topButton" id="student-preview">学生预览</span></div><div class="lib-tip-close"><span class="txt">请在此栏核对题目及客观题答案</span></div><div id="topicPreview"></div><div id="topPic-set-box" style="display:none"></div></div><div class="clearfix"></div></div><div id="topicSetting"></div></div><div class="header-mask" id="editor-mask"><div class="loading-cover" id="editor-cover"></div><div class="loading-box" style="display:none" id="editor-loading"><div class="img"></div><div class="loading-txt">正在保存，请稍候</div></div></div><script type="text/html" id="topicBatchSettingHtml"><div class="set-que-win set-que-win-batch">
            <div class="set-que-top">
                <span class="icon"></span>
                <span class="txt">批量修改</span>
                <span class="close-icon" onclick="CutHtml.closeTopicSetting()">×</span>
            </div>
            <div class="set-que-con batch">
                <div class="button cancel-set" onclick="CutHtml.batchDeleteEvent(CutHtml.e.BATCHDELETETOPIC,this)">
                    取消题目设置
<!--                    <span class="">(点此取消设置错误的题目)</span>-->
                </div>
                <div class="button delete-set" onclick="CutHtml.batchDeleteEvent(CutHtml.e.BATCHDELETECUTHTML,this)">
                    删除题目
                    <!--批量删除题目-->
<!--                    <span class="">(点此删除错误题目)</span>-->
                </div>
                <div class="button type-set">
                    更改题型
<!--                    批量设置题型-->
<!--                    <span class="">(点此设置、更改题型)</span>-->
                </div>
                <div class="set-type-wrapper">
                    <ul class="type-con type-con-batch">
                        <li data-type="8">单选题</li>
                        <li data-type="1">多选题</li>
                        <li data-type="2">判断题</li>
                        <li data-type="3">填空题</li>
                        <li data-type="6">简答题</li>
                        <li data-type="7">填空题智批</li>
                    </ul>
                    <div class="gray-area">
                        <div class="choice-opt-num">
                            <span class="label">选项个数：</span>
                            <div class="add-answerOperate">
                                <span class="aaq-button reduce"></span>
                                <div class="aaq-number">
                                    <input type="text" value="4" readonly="true"></div>
                                <span class="aaq-button add"></span>
                            </div>
                        </div>
                        <div class="new-big-que">
                            <a class="newque chkbx checked">
                                <span class="txt">新增大题</span>
                                <span class="icon"></span>
                            </a>
                            <a class="insertque chkbx">
                                <span class="txt">插入已有大题</span>
                                <span class="icon"></span>
                            </a>
                        </div>
                        <div class="set-que-name set-que-name-new">
                            <span class="label">题目名称</span>
                            <div class="que-name-div">
                                <input class="que-name-input" type="text" name="" id="bigTopicName_Batch" 
                                       value=""/>
                                <!-- <span class="word-limit">20字以内</span> -->
                            </div>
                        </div>
                        <div class="set-que-name set-que-name-select">
                            <span class="label">题目名称</span>
                            <div class="que-name-choice-box">
                                <select name="interest" class="select-bq" id="bqSelect">
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn-box btn-box-batchSet">
                <input class="yes" type="button" value="确定" onclick="CutHtml.batchTopicSettingSave()">
                <input class="cancel" type="button" value="取消" onclick="CutHtml.closeTopicSetting()">
            </div>
        </div></script><script type="text/html" id="topicSettingHtml"><div class="set-que-win">
            <div class="set-que-top">
                <span class="icon"></span>
                <span class="txt">设置</span>
                <span class="close-icon" onclick="CutHtml.closeTopicSetting()">×</span>
            </div>
            <div class="set-que-con">
                <div class="type-tit">将该题设置为</div>
                <ul class="qt-tab-ul">
                    <li class="selected">题目</li>
                </ul>
                <div class="type-tit">题型为</div>
                <ul class="type-con type-con-single">
                    <li data-type="8">单选题</li>
                    <li data-type="1">多选题</li>
                    <li data-type="2">判断题</li>
                    <li data-type="3">填空题</li>
                    <li data-type="6">简答题</li>
                    <li data-type="7">填空题智批</li>
                </ul>
                <div class="new-gray-area">
                    <ul class="check-ul">
                        <li id="check_topic">
                            <span class="check-label">共用该题面并新增大题</span>
                            <span class="check-icon select margin-r"></span>
                        </li>
                        <li id="check_new" style="float:right">
                            <span class="check-label">仅新增大题</span>
                            <span class="check-icon"></span>
                        </li>
                        <!--<li id="check_tip" class="tip">请至左边栏选择对应的小题</li>-->
                    </ul>
                    <div class="stip" id="check_tip">请至左边栏选择对应的小题，所勾选小题将共用此题面</div>
                    <div class="set-que-name">
                        <span class="label">题目名称</span>
                        <div class="que-name-div">
                            <input class="que-name-input" type="text" name="" id="bigTopicName" maxlength="20" value=""/>
                            <span class="word-limit">20字以内</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn-box">
                <input class="yes" type="button" value="确定" onclick="CutHtml.topicSettingSave()">
                <input class="cancel" type="button" value="取消" onclick="CutHtml.closeTopicSetting()">
            </div>
        </div></script><script id="noDataHtml" type="text/html"><div class="noresource-bg noData"><p>暂无题目</p></div></script><script id="ckeditorTop" type="text/html"><div class="ckeditor-top">
            <a class="no" href="javascript:CutHtml.cacleEditor();">取消</a>
            <a class="yes" href="javascript:CutHtml.saveEditor();">保存</a>
        </div></script><script type="text/html" id="topPic-set-tpl"><div class="set-top">
            {{# if (d.type=='1'){}}
                多选题
            {{# }else if(d.type=='2'){}}
                判断题
            {{# }else if(d.type=='3'){}}
                填空题
            {{# } else if(d.type=='6'){}}
                简答题
            {{# } else if(d.type=='7'){}}
                填空题智批
            {{# } else if(d.type=='8'){}}
                单选题
            {{# }else {}}
                {{d.name}}
            {{# }}}
            <span class="close-icon" onclick="CutHtml.closeChooseSetDiv()">×</span>
        </div>
        <div class="set-middle">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">题目名称</label>
                        <div class="layui-input-block">
                            <input type="text"  placeholder="20字以内" lay-verify="required" value="{{d.name}}" autocomplete="off" class="layui-input ques-tilte">
                        </div>
                    </div>
                    {{# if(d.type=='1' || d.type=='8'){}}
                    <div class="layui-form-item">
                        <label class="layui-form-label">选项个数</label>
                        <div class="layui-input-block">
                            <select id="optionSelect" name="option" lay-filter="option">
                                {{# for(var i=2;i<=26;i++){ }}
                                <option {{# if(d.option==i){}} selected="selected"{{# }}} value="{{i}}">{{i}}</option>
                                {{# }}}
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">正确答案</label>
                        <div class="layui-input-block">
                            <input type="text"  placeholder="请逐题输入正确选项" lay-verify="required" value="{{d.answers}}" autocomplete="off" class="layui-input ques-answer">
                        </div>
                    </div>
                    {{# }}}
                </form>
            {{# if(d.type=='8'){}}
                <p style="padding-left: 20px;color:#ff0000">请按题目顺序填写答案，如ABCD</p>
            {{# }}}
            {{# if(d.type=='1'){}}
            <p style="padding-left: 20px;color:#ff0000">每题答案中间用,间隔，如AB,BCD</p>
            {{# }}}
        </div>
        <div class="set-bottom">
            <a class="yes-set" data-bqid="{{d.bqid}}" data-qtype="{{d.type}}">确定</a>
            <a class="no-set" onclick="CutHtml.closeChooseSetDiv()">取消</a>
        </div></script><script id="choice-view-tpl" type="text/html"><div class="set-top">
            选项设置
        </div>
        <div class="set-middle">
            <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">显示方式</label>
                <div class="layui-input-block">
                    <select id="option-select" name="option" lay-filter="option">
                        <option {{# if(d.opttype=="1"){}} selected="selected"{{# }}} value="1">1行1列</option>
                        <option {{# if(d.opttype=="2"){}} selected="selected"{{# }}} value="2">1行2列</option>
                        <option {{# if(d.opttype=="3"){}} selected="selected"{{# }}} value="3">1行4列</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">选项个数</label>
                <div class="layui-input-block">
                    <select id="option-select-count" name="option" lay-filter="option">
                        {{# for(var i=2;i<=26;i++){ }}
                        <option {{# if(4==i){}} selected="selected"{{# }}} value="{{i}}">{{i}}</option>
                        {{# }}}
                    </select>
                </div>
            </div>
        </form>
        <!-- <p style="padding-left: 20px;forn-size:12px; color:#ff0000">仅在大屏展示时生效</p> -->
        </div>
        <div class="set-bottom">
            <a class="yes-set-option" data-qid="{{d.id}}">确定</a>
            <a class="no-set" onclick="CutHtml.closeChooseSetDiv()">取消</a>
        </div></script><script type="text/html" id="sortSettingHtml"><div class="set-que-win">
            <div class="set-que-top">
                <span class="icon"></span>
                <span class="txt">题号排序</span>
                <span class="close-icon" onclick="CutHtml.closeTopicSetting()">×</span>
            </div>
            <div class="set-que-con">
                <div class="new-gray-area">
                    <div >
                        　　<input id="paper-sort-add" type="radio" name ="paper-sort" value="1" {{# if(d.orderType=="" || d.orderType=="1"){}}checked{{#}}}><label for="paper-sort-add"
                                                                                          style="font-size:15px">试卷整体递增</label>
                        　　<input id="ques-sort-add" type="radio" name ="paper-sort"  value="2" {{# if(d.orderType=="2"){}}checked{{#}}}><label for="ques-sort-add"
                                                                                   style="font-size:15px">大题内部自增</label>
                    </div>
                </div>
            </div>
            <div class="btn-box">
                <input class="yes" type="button" value="确定" onclick="CutHtml.sortSettingSave()">
                <input class="cancel" type="button" value="取消" onclick="CutHtml.closeTopicSetting()">
            </div>
        </div></script></div><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script src="js/util/common.js"></script><script src="js/util/domHelp.js"></script><script src="js/util/ajax.js"></script><script src="js/thirdLib/layui/layui.js"></script><script src="js/thirdLib/layui/layui-config.js"></script><script src="js/thirdLib/avalon/avalon.js"></script><script src="js/thirdLib/avalon/component.js"></script><script src="js/thirdLib/ckeditor/ckeditor.js"></script><script src="js/testbank/matchQuesStyle.js"></script><script src="js/testbank/matchQuesPreview.js"></script><script src="js/testbank/matchQuesIdentify.js"></script><script src="js/testbank/matchQuesAuto.js"></script><script src="js/testbank/matchQues.js"></script><script src="js/thirdLib/disable-devtool.js"></script></body><script>DisableDevtool({md5:"27365a67be2217aa30a5e690d6937273",tkName:"mk"})</script>