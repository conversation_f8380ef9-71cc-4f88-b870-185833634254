<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/protocol/framer.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">lib/protocol/</a> framer.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">88.56% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>418/472</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">71.43% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>140/196</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">93.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>42/45</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">88.56% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>418/472</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721
722
723
724
725
726
727
728
729
730
731
732
733
734
735
736
737
738
739
740
741
742
743
744
745
746
747
748
749
750
751
752
753
754
755
756
757
758
759
760
761
762
763
764
765
766
767
768
769
770
771
772
773
774
775
776
777
778
779
780
781
782
783
784
785
786
787
788
789
790
791
792
793
794
795
796
797
798
799
800
801
802
803
804
805
806
807
808
809
810
811
812
813
814
815
816
817
818
819
820
821
822
823
824
825
826
827
828
829
830
831
832
833
834
835
836
837
838
839
840
841
842
843
844
845
846
847
848
849
850
851
852
853
854
855
856
857
858
859
860
861
862
863
864
865
866
867
868
869
870
871
872
873
874
875
876
877
878
879
880
881
882
883
884
885
886
887
888
889
890
891
892
893
894
895
896
897
898
899
900
901
902
903
904
905
906
907
908
909
910
911
912
913
914
915
916
917
918
919
920
921
922
923
924
925
926
927
928
929
930
931
932
933
934
935
936
937
938
939
940
941
942
943
944
945
946
947
948
949
950
951
952
953
954
955
956
957
958
959
960
961
962
963
964
965
966
967
968
969
970
971
972
973
974
975
976
977
978
979
980
981
982
983
984
985
986
987
988
989
990
991
992
993
994
995
996
997
998
999
1000
1001
1002
1003
1004
1005
1006
1007
1008
1009
1010
1011
1012
1013
1014
1015
1016
1017
1018
1019
1020
1021
1022
1023
1024
1025
1026
1027
1028
1029
1030
1031
1032
1033
1034
1035
1036
1037
1038
1039
1040
1041
1042
1043
1044
1045
1046
1047
1048
1049
1050
1051
1052
1053
1054
1055
1056
1057
1058
1059
1060
1061
1062
1063
1064
1065
1066
1067
1068
1069
1070
1071
1072
1073
1074
1075
1076
1077
1078
1079
1080
1081
1082
1083
1084
1085
1086
1087
1088
1089
1090
1091
1092
1093
1094
1095
1096
1097
1098
1099
1100
1101
1102
1103
1104
1105
1106
1107
1108
1109
1110
1111
1112
1113
1114
1115
1116
1117
1118
1119
1120
1121
1122
1123
1124
1125
1126
1127
1128
1129
1130
1131
1132
1133
1134
1135
1136
1137
1138
1139
1140
1141
1142
1143
1144
1145
1146
1147
1148
1149
1150
1151
1152
1153
1154
1155
1156
1157
1158
1159
1160
1161
1162
1163
1164
1165
1166
1167
1168
1169
1170
1171
1172
1173
1174</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-yes">1154×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1154×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">573×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1184×</span>
<span class="cline-any cline-yes">1184×</span>
<span class="cline-any cline-yes">1184×</span>
<span class="cline-any cline-yes">1184×</span>
<span class="cline-any cline-yes">610×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1094×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1094×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1094×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1128×</span>
<span class="cline-any cline-yes">1128×</span>
<span class="cline-any cline-yes">1128×</span>
<span class="cline-any cline-yes">1128×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1128×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1128×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1094×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">597×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">618×</span>
<span class="cline-any cline-yes">618×</span>
<span class="cline-any cline-yes">618×</span>
<span class="cline-any cline-yes">527×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">589×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-yes">2141×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">595×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">125×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">127×</span>
<span class="cline-any cline-yes">127×</span>
<span class="cline-any cline-yes">127×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">127×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">125×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">251×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">251×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">250×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">68×</span>
<span class="cline-any cline-yes">68×</span>
<span class="cline-any cline-yes">340×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">68×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">68×</span>
<span class="cline-any cline-yes">68×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">68×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">65×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">65×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">65×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">65×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">328×</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">312×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">184×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">128×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// The framer consists of two [Transform Stream][1] subclasses that operate in [object mode][2]:
// the Serializer and the Deserializer
// [1]: https://nodejs.org/api/stream.html#stream_class_stream_transform
// [2]: https://nodejs.org/api/stream.html#stream_new_stream_readable_options
var assert = require('assert');
&nbsp;
var Transform = require('stream').Transform;
&nbsp;
exports.Serializer = Serializer;
exports.Deserializer = Deserializer;
&nbsp;
var logData = (process !== 'undefined' &amp;&amp; process.env !== 'undefined' &amp;&amp; process.env.HTTP2_LOG_DATA);
&nbsp;
var MAX_PAYLOAD_SIZE = 16384;
var WINDOW_UPDATE_PAYLOAD_SIZE = 4;
&nbsp;
// Serializer
// ----------
//
//     Frame Objects
//     * * * * * * * --+---------------------------
//                     |                          |
//                     v                          v           Buffers
//      [] -----&gt; Payload Ser. --[buffers]--&gt; Header Ser. --&gt; * * * *
//     empty      adds payload                adds header
//     array        buffers                     buffer
&nbsp;
function Serializer(log) {
  this._log = log.child({ component: 'serializer' });
  Transform.call(this, { objectMode: true });
}
Serializer.prototype = Object.create(Transform.prototype, { constructor: { value: Serializer } });
&nbsp;
// When there's an incoming frame object, it first generates the frame type specific part of the
// frame (payload), and then then adds the header part which holds fields that are common to all
// frame types (like the length of the payload).
Serializer.prototype._transform = function _transform(frame, encoding, done) {
  this._log.trace({ frame: frame }, 'Outgoing frame');
&nbsp;
  assert(frame.type in Serializer, 'Unknown frame type: ' + frame.type);
&nbsp;
  var buffers = [];
  Serializer[frame.type](frame, buffers);
  var length = Serializer.commonHeader(frame, buffers);
&nbsp;
  assert(length &lt;= MAX_PAYLOAD_SIZE, 'Frame too large!');
&nbsp;
  for (var i = 0; i &lt; buffers.length; i++) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (logData) {
<span class="cstat-no" title="statement not covered" >      this._log.trace({ data: buffers[i] }, 'Outgoing data');</span>
    }
    this.push(buffers[i]);
  }
&nbsp;
  done();
};
&nbsp;
// Deserializer
// ------------
//
//     Buffers
//     * * * * --------+-------------------------
//                     |                        |
//                     v                        v           Frame Objects
//      {} -----&gt; Header Des. --{frame}--&gt; Payload Des. --&gt; * * * * * * *
//     empty      adds parsed              adds parsed
//     object  header properties        payload properties
&nbsp;
function Deserializer(log, role) {
  this._role = role;
  this._log = log.child({ component: 'deserializer' });
  Transform.call(this, { objectMode: true });
  this._next(COMMON_HEADER_SIZE);
}
Deserializer.prototype = Object.create(Transform.prototype, { constructor: { value: Deserializer } });
&nbsp;
// The Deserializer is stateful, and it's two main alternating states are: *waiting for header* and
// *waiting for payload*. The state is stored in the boolean property `_waitingForHeader`.
//
// When entering a new state, a `_buffer` is created that will hold the accumulated data (header or
// payload). The `_cursor` is used to track the progress.
Deserializer.prototype._next = function(size) {
  this._cursor = 0;
  this._buffer = new Buffer(size);
  this._waitingForHeader = !this._waitingForHeader;
  if (this._waitingForHeader) {
    this._frame = {};
  }
};
&nbsp;
// Parsing an incoming buffer is an iterative process because it can hold multiple frames if it's
// large enough. A `cursor` is used to track the progress in parsing the incoming `chunk`.
Deserializer.prototype._transform = function _transform(chunk, encoding, done) {
  var cursor = 0;
&nbsp;
  <span class="missing-if-branch" title="if path not taken" >I</span>if (logData) {
<span class="cstat-no" title="statement not covered" >    this._log.trace({ data: chunk }, 'Incoming data');</span>
  }
&nbsp;
  while(cursor &lt; chunk.length) {
    // The content of an incoming buffer is first copied to `_buffer`. If it can't hold the full
    // chunk, then only a part of it is copied.
    var toCopy = Math.min(chunk.length - cursor, this._buffer.length - this._cursor);
    chunk.copy(this._buffer, this._cursor, cursor, cursor + toCopy);
    this._cursor += toCopy;
    cursor += toCopy;
&nbsp;
    // When `_buffer` is full, it's content gets parsed either as header or payload depending on
    // the actual state.
&nbsp;
    // If it's header then the parsed data is stored in a temporary variable and then the
    // deserializer waits for the specified length payload.
    if ((this._cursor === this._buffer.length) &amp;&amp; this._waitingForHeader) {
      var payloadSize = Deserializer.commonHeader(this._buffer, this._frame);
      <span class="missing-if-branch" title="else path not taken" >E</span>if (payloadSize &lt;= MAX_PAYLOAD_SIZE) {
        this._next(payloadSize);
      } else {
<span class="cstat-no" title="statement not covered" >        this.emit('error', 'FRAME_SIZE_ERROR');</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
      }
    }
&nbsp;
    // If it's payload then the the frame object is finalized and then gets pushed out.
    // Unknown frame types are ignored.
    //
    // Note: If we just finished the parsing of a header and the payload length is 0, this branch
    // will also run.
    if ((this._cursor === this._buffer.length) &amp;&amp; !this._waitingForHeader) {
      <span class="missing-if-branch" title="else path not taken" >E</span>if (this._frame.type) {
        var error = Deserializer[this._frame.type](this._buffer, this._frame, this._role);
        <span class="missing-if-branch" title="if path not taken" >I</span>if (error) {
<span class="cstat-no" title="statement not covered" >          this._log.error('Incoming frame parsing error: ' + error);</span>
<span class="cstat-no" title="statement not covered" >          this.emit('error', error);</span>
        } else {
          this._log.trace({ frame: this._frame }, 'Incoming frame');
          this.push(this._frame);
        }
      } else {
<span class="cstat-no" title="statement not covered" >        this._log.error('Unknown type incoming frame');</span>
        // Ignore it other than logging
      }
      this._next(COMMON_HEADER_SIZE);
    }
  }
&nbsp;
  done();
};
&nbsp;
// [Frame Header](https://tools.ietf.org/html/rfc7540#section-4.1)
// --------------------------------------------------------------
//
// HTTP/2 frames share a common base format consisting of a 9-byte header followed by 0 to 2^24 - 1
// bytes of data.
//
// Additional size limits can be set by specific application uses. HTTP limits the frame size to
// 16,384 octets by default, though this can be increased by a receiver.
//
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |                 Length (24)                   |
//     +---------------+---------------+---------------+
//     |   Type (8)    |   Flags (8)   |
//     +-+-----------------------------+---------------+---------------+
//     |R|                 Stream Identifier (31)                      |
//     +-+-------------------------------------------------------------+
//     |                     Frame Data (0...)                       ...
//     +---------------------------------------------------------------+
//
// The fields of the frame header are defined as:
//
// * Length:
//   The length of the frame data expressed as an unsigned 24-bit integer. The 9 bytes of the frame
//   header are not included in this value.
//
// * Type:
//   The 8-bit type of the frame. The frame type determines how the remainder of the frame header
//   and data are interpreted. Implementations MUST ignore unsupported and unrecognized frame types.
//
// * Flags:
//   An 8-bit field reserved for frame-type specific boolean flags.
//
//   Flags are assigned semantics specific to the indicated frame type. Flags that have no defined
//   semantics for a particular frame type MUST be ignored, and MUST be left unset (0) when sending.
//
// * R:
//   A reserved 1-bit field. The semantics of this bit are undefined and the bit MUST remain unset
//   (0) when sending and MUST be ignored when receiving.
//
// * Stream Identifier:
//   A 31-bit stream identifier. The value 0 is reserved for frames that are associated with the
//   connection as a whole as opposed to an individual stream.
//
// The structure and content of the remaining frame data is dependent entirely on the frame type.
&nbsp;
var COMMON_HEADER_SIZE = 9;
&nbsp;
var frameTypes = [];
&nbsp;
var frameFlags = {};
&nbsp;
var genericAttributes = ['type', 'flags', 'stream'];
&nbsp;
var typeSpecificAttributes = {};
&nbsp;
Serializer.commonHeader = function writeCommonHeader(frame, buffers) {
  var headerBuffer = new Buffer(COMMON_HEADER_SIZE);
&nbsp;
  var size = 0;
  for (var i = 0; i &lt; buffers.length; i++) {
    size += buffers[i].length;
  }
  headerBuffer.writeUInt8(0, 0);
  headerBuffer.writeUInt16BE(size, 1);
&nbsp;
  var typeId = frameTypes.indexOf(frame.type);  // If we are here then the type is valid for sure
  headerBuffer.writeUInt8(typeId, 3);
&nbsp;
  var flagByte = 0;
  for (var flag in frame.flags) {
    var position = frameFlags[frame.type].indexOf(flag);
    assert(position !== -1, 'Unknown flag for frame type ' + frame.type + ': ' + flag);
    if (frame.flags[flag]) {
      flagByte |= (1 &lt;&lt; position);
    }
  }
  headerBuffer.writeUInt8(flagByte, 4);
&nbsp;
  assert((0 &lt;= frame.stream) &amp;&amp; (frame.stream &lt; 0x7fffffff), frame.stream);
  headerBuffer.writeUInt32BE(frame.stream || 0, 5);
&nbsp;
  buffers.unshift(headerBuffer);
&nbsp;
  return size;
};
&nbsp;
Deserializer.commonHeader = function readCommonHeader(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 9) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
&nbsp;
  var totallyWastedByte = buffer.readUInt8(0);
  var length = buffer.readUInt16BE(1);
  // We do this just for sanity checking later on, to make sure no one sent us a
  // frame that's super large.
  length += totallyWastedByte &lt;&lt; 16;
&nbsp;
  frame.type = frameTypes[buffer.readUInt8(3)];
  <span class="missing-if-branch" title="if path not taken" >I</span>if (!frame.type) {
    // We are required to ignore unknown frame types
<span class="cstat-no" title="statement not covered" >    return length;</span>
  }
&nbsp;
  frame.flags = {};
  var flagByte = buffer.readUInt8(4);
  var definedFlags = frameFlags[frame.type];
  for (var i = 0; i &lt; definedFlags.length; i++) {
    frame.flags[definedFlags[i]] = Boolean(flagByte &amp; (1 &lt;&lt; i));
  }
&nbsp;
  frame.stream = buffer.readUInt32BE(5) &amp; 0x7fffffff;
&nbsp;
  return length;
};
&nbsp;
// Frame types
// ===========
&nbsp;
// Every frame type is registered in the following places:
//
// * `frameTypes`: a register of frame type codes (used by `commonHeader()`)
// * `frameFlags`: a register of valid flags for frame types (used by `commonHeader()`)
// * `typeSpecificAttributes`: a register of frame specific frame object attributes (used by
//   logging code and also serves as documentation for frame objects)
&nbsp;
// [DATA Frames](https://tools.ietf.org/html/rfc7540#section-6.1)
// ------------------------------------------------------------
//
// DATA frames (type=0x0) convey arbitrary, variable-length sequences of octets associated with a
// stream.
//
// The DATA frame defines the following flags:
//
// * END_STREAM (0x1):
//   Bit 1 being set indicates that this frame is the last that the endpoint will send for the
//   identified stream.
// * PADDED (0x08):
//   Bit 4 being set indicates that the Pad Length field is present.
&nbsp;
frameTypes[0x0] = 'DATA';
&nbsp;
frameFlags.DATA = ['END_STREAM', 'RESERVED2', 'RESERVED4', 'PADDED'];
&nbsp;
typeSpecificAttributes.DATA = ['data'];
&nbsp;
Serializer.DATA = function writeData(frame, buffers) {
  buffers.push(frame.data);
};
&nbsp;
Deserializer.DATA = function readData(buffer, frame) {
  var dataOffset = 0;
  var paddingLength = 0;
  if (frame.flags.PADDED) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 1) {
      // We must have at least one byte for padding control, but we don't. Bad peer!
<span class="cstat-no" title="statement not covered" >      return 'FRAME_SIZE_ERROR';</span>
    }
    paddingLength = (buffer.readUInt8(dataOffset) &amp; 0xff);
    dataOffset = 1;
  }
&nbsp;
  if (paddingLength) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (paddingLength &gt;= (buffer.length - 1)) {
      // We don't have enough room for the padding advertised - bad peer!
<span class="cstat-no" title="statement not covered" >      return 'FRAME_SIZE_ERROR';</span>
    }
    frame.data = buffer.slice(dataOffset, -1 * paddingLength);
  } else {
    frame.data = buffer.slice(dataOffset);
  }
};
&nbsp;
// [HEADERS](https://tools.ietf.org/html/rfc7540#section-6.2)
// --------------------------------------------------------------
//
// The HEADERS frame (type=0x1) allows the sender to create a stream.
//
// The HEADERS frame defines the following flags:
//
// * END_STREAM (0x1):
//   Bit 1 being set indicates that this frame is the last that the endpoint will send for the
//   identified stream.
// * END_HEADERS (0x4):
//   The END_HEADERS bit indicates that this frame contains the entire payload necessary to provide
//   a complete set of headers.
// * PADDED (0x08):
//   Bit 4 being set indicates that the Pad Length field is present.
// * PRIORITY (0x20):
//   Bit 6 being set indicates that the Exlusive Flag (E), Stream Dependency, and Weight fields are
//   present.
&nbsp;
frameTypes[0x1] = 'HEADERS';
&nbsp;
frameFlags.HEADERS = ['END_STREAM', 'RESERVED2', 'END_HEADERS', 'PADDED', 'RESERVED5', 'PRIORITY'];
&nbsp;
typeSpecificAttributes.HEADERS = ['priorityDependency', 'priorityWeight', 'exclusiveDependency', 'headers', 'data'];
&nbsp;
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |Pad Length? (8)|
//     +-+-------------+---------------+-------------------------------+
//     |E|                 Stream Dependency? (31)                     |
//     +-+-------------+-----------------------------------------------+
//     |  Weight? (8)  |
//     +-+-------------+-----------------------------------------------+
//     |                   Header Block Fragment (*)                 ...
//     +---------------------------------------------------------------+
//     |                           Padding (*)                       ...
//     +---------------------------------------------------------------+
//
// The payload of a HEADERS frame contains a Headers Block
&nbsp;
Serializer.HEADERS = function writeHeadersPriority(frame, buffers) {
  if (frame.flags.PRIORITY) {
    var buffer = new Buffer(5);
    assert((0 &lt;= frame.priorityDependency) &amp;&amp; (frame.priorityDependency &lt;= 0x7fffffff), frame.priorityDependency);
    buffer.writeUInt32BE(frame.priorityDependency, 0);
    if (frame.exclusiveDependency) {
      buffer[0] |= 0x80;
    }
    assert((0 &lt;= frame.priorityWeight) &amp;&amp; (frame.priorityWeight &lt;= 0xff), frame.priorityWeight);
    buffer.writeUInt8(frame.priorityWeight, 4);
    buffers.push(buffer);
  }
  buffers.push(frame.data);
};
&nbsp;
Deserializer.HEADERS = function readHeadersPriority(buffer, frame) {
  var minFrameLength = 0;
  if (frame.flags.PADDED) {
    minFrameLength += 1;
  }
  if (frame.flags.PRIORITY) {
    minFrameLength += 5;
  }
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; minFrameLength) {
    // Peer didn't send enough data - bad peer!
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
&nbsp;
  var dataOffset = 0;
  var paddingLength = 0;
  if (frame.flags.PADDED) {
    paddingLength = (buffer.readUInt8(dataOffset) &amp; 0xff);
    dataOffset = 1;
  }
&nbsp;
  if (frame.flags.PRIORITY) {
    var dependencyData = new Buffer(4);
    buffer.copy(dependencyData, 0, dataOffset, dataOffset + 4);
    dataOffset += 4;
    frame.exclusiveDependency = !!(dependencyData[0] &amp; 0x80);
    dependencyData[0] &amp;= 0x7f;
    frame.priorityDependency = dependencyData.readUInt32BE(0);
    frame.priorityWeight = buffer.readUInt8(dataOffset);
    dataOffset += 1;
  }
&nbsp;
  if (paddingLength) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if ((buffer.length - dataOffset) &lt; paddingLength) {
      // Not enough data left to satisfy the advertised padding - bad peer!
<span class="cstat-no" title="statement not covered" >      return 'FRAME_SIZE_ERROR';</span>
    }
    frame.data = buffer.slice(dataOffset, -1 * paddingLength);
  } else {
    frame.data = buffer.slice(dataOffset);
  }
};
&nbsp;
// [PRIORITY](https://tools.ietf.org/html/rfc7540#section-6.3)
// -------------------------------------------------------
//
// The PRIORITY frame (type=0x2) specifies the sender-advised priority of a stream.
//
// The PRIORITY frame does not define any flags.
&nbsp;
frameTypes[0x2] = 'PRIORITY';
&nbsp;
frameFlags.PRIORITY = [];
&nbsp;
typeSpecificAttributes.PRIORITY = ['priorityDependency', 'priorityWeight', 'exclusiveDependency'];
&nbsp;
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |E|                 Stream Dependency? (31)                     |
//     +-+-------------+-----------------------------------------------+
//     |  Weight? (8)  |
//     +-+-------------+
//
// The payload of a PRIORITY frame contains an exclusive bit, a 31-bit dependency, and an 8-bit weight
&nbsp;
Serializer.PRIORITY = function writePriority(frame, buffers) {
  var buffer = new Buffer(5);
  assert((0 &lt;= frame.priorityDependency) &amp;&amp; (frame.priorityDependency &lt;= 0x7fffffff), frame.priorityDependency);
  buffer.writeUInt32BE(frame.priorityDependency, 0);
  if (frame.exclusiveDependency) {
    buffer[0] |= 0x80;
  }
  assert((0 &lt;= frame.priorityWeight) &amp;&amp; (frame.priorityWeight &lt;= 0xff), frame.priorityWeight);
  buffer.writeUInt8(frame.priorityWeight, 4);
&nbsp;
  buffers.push(buffer);
};
&nbsp;
Deserializer.PRIORITY = function readPriority(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 5) {
    // PRIORITY frames are 5 bytes long. Bad peer!
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  var dependencyData = new Buffer(4);
  buffer.copy(dependencyData, 0, 0, 4);
  frame.exclusiveDependency = !!(dependencyData[0] &amp; 0x80);
  dependencyData[0] &amp;= 0x7f;
  frame.priorityDependency = dependencyData.readUInt32BE(0);
  frame.priorityWeight = buffer.readUInt8(4);
};
&nbsp;
// [RST_STREAM](https://tools.ietf.org/html/rfc7540#section-6.4)
// -----------------------------------------------------------
//
// The RST_STREAM frame (type=0x3) allows for abnormal termination of a stream.
//
// No type-flags are defined.
&nbsp;
frameTypes[0x3] = 'RST_STREAM';
&nbsp;
frameFlags.RST_STREAM = [];
&nbsp;
typeSpecificAttributes.RST_STREAM = ['error'];
&nbsp;
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |                         Error Code (32)                       |
//     +---------------------------------------------------------------+
//
// The RST_STREAM frame contains a single unsigned, 32-bit integer identifying the error
// code (see Error Codes). The error code indicates why the stream is being terminated.
&nbsp;
Serializer.RST_STREAM = function writeRstStream(frame, buffers) {
  var buffer = new Buffer(4);
  var code = errorCodes.indexOf(frame.error);
  assert((0 &lt;= code) &amp;&amp; (code &lt;= 0xffffffff), code);
  buffer.writeUInt32BE(code, 0);
  buffers.push(buffer);
};
&nbsp;
Deserializer.RST_STREAM = function readRstStream(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 4) {
    // RST_STREAM is 4 bytes long. Bad peer!
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  frame.error = errorCodes[buffer.readUInt32BE(0)];
  <span class="missing-if-branch" title="if path not taken" >I</span>if (!frame.error) {
    // Unknown error codes are considered equivalent to INTERNAL_ERROR
<span class="cstat-no" title="statement not covered" >    frame.error = 'INTERNAL_ERROR';</span>
  }
};
&nbsp;
// [SETTINGS](https://tools.ietf.org/html/rfc7540#section-6.5)
// -------------------------------------------------------
//
// The SETTINGS frame (type=0x4) conveys configuration parameters that affect how endpoints
// communicate.
//
// The SETTINGS frame defines the following flag:
&nbsp;
// * ACK (0x1):
//   Bit 1 being set indicates that this frame acknowledges receipt and application of the peer's
//   SETTINGS frame.
frameTypes[0x4] = 'SETTINGS';
&nbsp;
frameFlags.SETTINGS = ['ACK'];
&nbsp;
typeSpecificAttributes.SETTINGS = ['settings'];
&nbsp;
// The payload of a SETTINGS frame consists of zero or more settings. Each setting consists of a
// 16-bit identifier, and an unsigned 32-bit value.
//
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |         Identifier(16)          |        Value (32)           |
//     +-----------------+---------------------------------------------+
//     ...Value                          |
//     +---------------------------------+
//
// Each setting in a SETTINGS frame replaces the existing value for that setting.  Settings are
// processed in the order in which they appear, and a receiver of a SETTINGS frame does not need to
// maintain any state other than the current value of settings.  Therefore, the value of a setting
// is the last value that is seen by a receiver. This permits the inclusion of the same settings
// multiple times in the same SETTINGS frame, though doing so does nothing other than waste
// connection capacity.
&nbsp;
Serializer.SETTINGS = function writeSettings(frame, buffers) {
  var settings = [], settingsLeft = Object.keys(frame.settings);
  definedSettings.forEach(function(setting, id) {
    if (setting.name in frame.settings) {
      settingsLeft.splice(settingsLeft.indexOf(setting.name), 1);
      var value = frame.settings[setting.name];
      settings.push({ id: id, value: setting.flag ? Boolean(value) : value });
    }
  });
  assert(settingsLeft.length === 0, 'Unknown settings: ' + settingsLeft.join(', '));
&nbsp;
  var buffer = new Buffer(settings.length * 6);
  for (var i = 0; i &lt; settings.length; i++) {
    buffer.writeUInt16BE(settings[i].id &amp; 0xffff, i*6);
    buffer.writeUInt32BE(settings[i].value, i*6 + 2);
  }
&nbsp;
  buffers.push(buffer);
};
&nbsp;
Deserializer.SETTINGS = function readSettings(buffer, frame, role) {
  frame.settings = {};
&nbsp;
  // Receipt of a SETTINGS frame with the ACK flag set and a length
  // field value other than 0 MUST be treated as a connection error
  // (Section 5.4.1) of type FRAME_SIZE_ERROR.
  <span class="missing-if-branch" title="if path not taken" >I</span>if(frame.flags.ACK &amp;&amp; buffer.length != 0) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
&nbsp;
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length % 6 !== 0) {
<span class="cstat-no" title="statement not covered" >    return 'PROTOCOL_ERROR';</span>
  }
  for (var i = 0; i &lt; buffer.length / 6; i++) {
    var id = buffer.readUInt16BE(i*6) &amp; 0xffff;
    var setting = definedSettings[id];
    <span class="missing-if-branch" title="else path not taken" >E</span>if (setting) {
      <span class="missing-if-branch" title="if path not taken" >I</span>if (role == 'CLIENT' &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >setting.name == 'SETTINGS_ENABLE_PUSH')</span> {
<span class="cstat-no" title="statement not covered" >        return 'SETTINGS frame on client got SETTINGS_ENABLE_PUSH';</span>
      }
      var value = buffer.readUInt32BE(i*6 + 2);
      frame.settings[setting.name] = setting.flag ? Boolean(value &amp; 0x1) : value;
    }
  }
};
&nbsp;
// The following settings are defined:
var definedSettings = [];
&nbsp;
// * SETTINGS_HEADER_TABLE_SIZE (1):
//   Allows the sender to inform the remote endpoint of the size of the header compression table
//   used to decode header blocks.
definedSettings[1] = { name: 'SETTINGS_HEADER_TABLE_SIZE', flag: false };
&nbsp;
// * SETTINGS_ENABLE_PUSH (2):
//   This setting can be use to disable server push. An endpoint MUST NOT send a PUSH_PROMISE frame
//   if it receives this setting set to a value of 0. The default value is 1, which indicates that
//   push is permitted.
definedSettings[2] = { name: 'SETTINGS_ENABLE_PUSH', flag: true };
&nbsp;
// * SETTINGS_MAX_CONCURRENT_STREAMS (3):
//   indicates the maximum number of concurrent streams that the sender will allow.
definedSettings[3] = { name: 'SETTINGS_MAX_CONCURRENT_STREAMS', flag: false };
&nbsp;
// * SETTINGS_INITIAL_WINDOW_SIZE (4):
//   indicates the sender's initial stream window size (in bytes) for new streams.
definedSettings[4] = { name: 'SETTINGS_INITIAL_WINDOW_SIZE', flag: false };
&nbsp;
// * SETTINGS_MAX_FRAME_SIZE (5):
//   indicates the maximum size of a frame the receiver will allow.
definedSettings[5] = { name: 'SETTINGS_MAX_FRAME_SIZE', flag: false };
&nbsp;
// [PUSH_PROMISE](https://tools.ietf.org/html/rfc7540#section-6.6)
// ---------------------------------------------------------------
//
// The PUSH_PROMISE frame (type=0x5) is used to notify the peer endpoint in advance of streams the
// sender intends to initiate.
//
// The PUSH_PROMISE frame defines the following flags:
//
// * END_PUSH_PROMISE (0x4):
//   The END_PUSH_PROMISE bit indicates that this frame contains the entire payload necessary to
//   provide a complete set of headers.
&nbsp;
frameTypes[0x5] = 'PUSH_PROMISE';
&nbsp;
frameFlags.PUSH_PROMISE = ['RESERVED1', 'RESERVED2', 'END_PUSH_PROMISE', 'PADDED'];
&nbsp;
typeSpecificAttributes.PUSH_PROMISE = ['promised_stream', 'headers', 'data'];
&nbsp;
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |Pad Length? (8)|
//     +-+-------------+-----------------------------------------------+
//     |X|                Promised-Stream-ID (31)                      |
//     +-+-------------------------------------------------------------+
//     |                 Header Block Fragment (*)                   ...
//     +---------------------------------------------------------------+
//     |                         Padding (*)                         ...
//     +---------------------------------------------------------------+
//
// The PUSH_PROMISE frame includes the unsigned 31-bit identifier of
// the stream the endpoint plans to create along with a minimal set of headers that provide
// additional context for the stream.
&nbsp;
Serializer.PUSH_PROMISE = function writePushPromise(frame, buffers) {
  var buffer = new Buffer(4);
&nbsp;
  var promised_stream = frame.promised_stream;
  assert((0 &lt;= promised_stream) &amp;&amp; (promised_stream &lt;= 0x7fffffff), promised_stream);
  buffer.writeUInt32BE(promised_stream, 0);
&nbsp;
  buffers.push(buffer);
  buffers.push(frame.data);
};
&nbsp;
Deserializer.PUSH_PROMISE = function readPushPromise(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 4) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  var dataOffset = 0;
  var paddingLength = 0;
  if (frame.flags.PADDED) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 5) {
<span class="cstat-no" title="statement not covered" >      return 'FRAME_SIZE_ERROR';</span>
    }
    paddingLength = (buffer.readUInt8(dataOffset) &amp; 0xff);
    dataOffset = 1;
  }
  frame.promised_stream = buffer.readUInt32BE(dataOffset) &amp; 0x7fffffff;
  dataOffset += 4;
  if (paddingLength) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if ((buffer.length - dataOffset) &lt; paddingLength) {
<span class="cstat-no" title="statement not covered" >      return 'FRAME_SIZE_ERROR';</span>
    }
    frame.data = buffer.slice(dataOffset, -1 * paddingLength);
  } else {
    frame.data = buffer.slice(dataOffset);
  }
};
&nbsp;
// [PING](https://tools.ietf.org/html/rfc7540#section-6.7)
// -----------------------------------------------
//
// The PING frame (type=0x6) is a mechanism for measuring a minimal round-trip time from the
// sender, as well as determining whether an idle connection is still functional.
//
// The PING frame defines one type-specific flag:
//
// * ACK (0x1):
//   Bit 1 being set indicates that this PING frame is a PING response.
&nbsp;
frameTypes[0x6] = 'PING';
&nbsp;
frameFlags.PING = ['ACK'];
&nbsp;
typeSpecificAttributes.PING = ['data'];
&nbsp;
// In addition to the frame header, PING frames MUST contain 8 additional octets of opaque data.
&nbsp;
Serializer.PING = function writePing(frame, buffers) {
  buffers.push(frame.data);
};
&nbsp;
Deserializer.PING = function readPing(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length !== 8) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  frame.data = buffer;
};
&nbsp;
// [GOAWAY](https://tools.ietf.org/html/rfc7540#section-6.8)
// ---------------------------------------------------
//
// The GOAWAY frame (type=0x7) informs the remote peer to stop creating streams on this connection.
//
// The GOAWAY frame does not define any flags.
&nbsp;
frameTypes[0x7] = 'GOAWAY';
&nbsp;
frameFlags.GOAWAY = [];
&nbsp;
typeSpecificAttributes.GOAWAY = ['last_stream', 'error'];
&nbsp;
//      0                   1                   2                   3
//      0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//     +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//     |X|                  Last-Stream-ID (31)                        |
//     +-+-------------------------------------------------------------+
//     |                      Error Code (32)                          |
//     +---------------------------------------------------------------+
//     |                  Additional Debug Data (*)                    |
//     +---------------------------------------------------------------+
//
// The last stream identifier in the GOAWAY frame contains the highest numbered stream identifier
// for which the sender of the GOAWAY frame has received frames on and might have taken some action
// on.
//
// The GOAWAY frame also contains a 32-bit error code (see Error Codes) that contains the reason for
// closing the connection.
&nbsp;
Serializer.GOAWAY = function writeGoaway(frame, buffers) {
  var buffer = new Buffer(8);
&nbsp;
  var last_stream = frame.last_stream;
  assert((0 &lt;= last_stream) &amp;&amp; (last_stream &lt;= 0x7fffffff), last_stream);
  buffer.writeUInt32BE(last_stream, 0);
&nbsp;
  var code = errorCodes.indexOf(frame.error);
  assert((0 &lt;= code) &amp;&amp; (code &lt;= 0xffffffff), code);
  buffer.writeUInt32BE(code, 4);
&nbsp;
  buffers.push(buffer);
};
&nbsp;
Deserializer.GOAWAY = function readGoaway(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 8) {
    // GOAWAY must have at least 8 bytes
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  frame.last_stream = buffer.readUInt32BE(0) &amp; 0x7fffffff;
  frame.error = errorCodes[buffer.readUInt32BE(4)];
  <span class="missing-if-branch" title="if path not taken" >I</span>if (!frame.error) {
    // Unknown error types are to be considered equivalent to INTERNAL ERROR
<span class="cstat-no" title="statement not covered" >    frame.error = 'INTERNAL_ERROR';</span>
  }
  // Read remaining data into "debug_data"
  // https://http2.github.io/http2-spec/#GOAWAY
  //   Endpoints MAY append opaque data to the payload of any GOAWAY frame
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &gt; 8) {
<span class="cstat-no" title="statement not covered" >    frame.debug_data = buffer.slice(8);</span>
  }
};
&nbsp;
// [WINDOW_UPDATE](https://tools.ietf.org/html/rfc7540#section-6.9)
// -----------------------------------------------------------------
//
// The WINDOW_UPDATE frame (type=0x8) is used to implement flow control.
//
// The WINDOW_UPDATE frame does not define any flags.
&nbsp;
frameTypes[0x8] = 'WINDOW_UPDATE';
&nbsp;
frameFlags.WINDOW_UPDATE = [];
&nbsp;
typeSpecificAttributes.WINDOW_UPDATE = ['window_size'];
&nbsp;
// The payload of a WINDOW_UPDATE frame is a 32-bit value indicating the additional number of bytes
// that the sender can transmit in addition to the existing flow control window. The legal range
// for this field is 1 to 2^31 - 1 (0x7fffffff) bytes; the most significant bit of this value is
// reserved.
&nbsp;
Serializer.WINDOW_UPDATE = function writeWindowUpdate(frame, buffers) {
  var buffer = new Buffer(4);
&nbsp;
  var window_size = frame.window_size;
  assert((0 &lt; window_size) &amp;&amp; (window_size &lt;= 0x7fffffff), window_size);
  buffer.writeUInt32BE(window_size, 0);
&nbsp;
  buffers.push(buffer);
};
&nbsp;
Deserializer.WINDOW_UPDATE = function readWindowUpdate(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length !== WINDOW_UPDATE_PAYLOAD_SIZE) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  frame.window_size = buffer.readUInt32BE(0) &amp; 0x7fffffff;
  <span class="missing-if-branch" title="if path not taken" >I</span>if (frame.window_size === 0) {
<span class="cstat-no" title="statement not covered" >    return 'PROTOCOL_ERROR';</span>
  }
};
&nbsp;
// [CONTINUATION](https://tools.ietf.org/html/rfc7540#section-6.10)
// ------------------------------------------------------------
//
// The CONTINUATION frame (type=0x9) is used to continue a sequence of header block fragments.
//
// The CONTINUATION frame defines the following flag:
//
// * END_HEADERS (0x4):
//   The END_HEADERS bit indicates that this frame ends the sequence of header block fragments
//   necessary to provide a complete set of headers.
&nbsp;
frameTypes[0x9] = 'CONTINUATION';
&nbsp;
frameFlags.CONTINUATION = ['RESERVED1', 'RESERVED2', 'END_HEADERS'];
&nbsp;
typeSpecificAttributes.CONTINUATION = ['headers', 'data'];
&nbsp;
Serializer.CONTINUATION = function writeContinuation(frame, buffers) {
  buffers.push(frame.data);
};
&nbsp;
Deserializer.CONTINUATION = function readContinuation(buffer, frame) {
  frame.data = buffer;
};
&nbsp;
// [ALTSVC](https://tools.ietf.org/html/rfc7838#section-4)
// ------------------------------------------------------------
//
// The ALTSVC frame (type=0xA) advertises the availability of an alternative service to the client.
//
// The ALTSVC frame does not define any flags.
&nbsp;
frameTypes[0xA] = 'ALTSVC';
&nbsp;
frameFlags.ALTSVC = [];
&nbsp;
//     0                   1                   2                   3
//     0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//    |         Origin-Len (16)       | Origin? (*)                 ...
//    +-------------------------------+----------------+--------------+
//    |                   Alt-Svc-Field-Value (*)                   ...
//    +---------------------------------------------------------------+
//
// The ALTSVC frame contains the following fields:
//
// Origin-Len: An unsigned, 16-bit integer indicating the length, in
//    octets, of the Origin field.
//
// Origin: An OPTIONAL sequence of characters containing ASCII
//    serialisation of an origin ([RFC6454](https://tools.ietf.org/html/rfc6454),
//    Section 6.2) that the alternate service is applicable to.
//
// Alt-Svc-Field-Value: A sequence of octets (length determined by
//    subtracting the length of all preceding fields from the frame
//    length) containing a value identical to the Alt-Svc field value
//    defined in (Section 3)[https://tools.ietf.org/html/rfc7838#section-3]
//    (ABNF production "Alt-Svc").
&nbsp;
typeSpecificAttributes.ALTSVC = ['maxAge', 'port', 'protocolID', 'host',
                                 'origin'];
&nbsp;
function istchar(c) {
  return ('!#$&amp;\'*+-.^_`|~1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.indexOf(c) &gt; -1);
}
&nbsp;
function hexencode(s) {
  var t = '';
  for (var i = 0; i &lt; s.length; i++) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!istchar(s[i])) {
<span class="cstat-no" title="statement not covered" >      t += '%';</span>
<span class="cstat-no" title="statement not covered" >      t += new Buffer(s[i]).toString('hex');</span>
    } else {
      t += s[i];
    }
  }
  return t;
}
&nbsp;
Serializer.ALTSVC = function writeAltSvc(frame, buffers) {
  var buffer = new Buffer(2);
  buffer.writeUInt16BE(frame.origin.length, 0);
  buffers.push(buffer);
  buffers.push(new Buffer(frame.origin, 'ascii'));
&nbsp;
  var fieldValue = hexencode(frame.protocolID) + '="' + frame.host + ':' + frame.port + '"';
  <span class="missing-if-branch" title="else path not taken" >E</span>if (frame.maxAge !== 86400) { // 86400 is the default
    fieldValue += "; ma=" + frame.maxAge;
  }
&nbsp;
  buffers.push(new Buffer(fieldValue, 'ascii'));
};
&nbsp;
function stripquotes(s) {
  var start = 0;
  var end = s.length;
  while ((start &lt; end) &amp;&amp; (s[start] === '"')) {
    start++;
  }
  while ((end &gt; start) &amp;&amp; (s[end - 1] === '"')) {
    end--;
  }
  <span class="missing-if-branch" title="if path not taken" >I</span>if (start &gt;= end) {
<span class="cstat-no" title="statement not covered" >    return "";</span>
  }
  return s.substring(start, end);
}
&nbsp;
function splitNameValue(nvpair) {
  var eq = -1;
  var inQuotes = false;
&nbsp;
  for (var i = 0; i &lt; nvpair.length; i++) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (nvpair[i] === '"') {
<span class="cstat-no" title="statement not covered" >      inQuotes = !inQuotes;</span>
<span class="cstat-no" title="statement not covered" >      continue;</span>
    }
    <span class="missing-if-branch" title="if path not taken" >I</span>if (inQuotes) {
<span class="cstat-no" title="statement not covered" >      continue;</span>
    }
    if (nvpair[i] === '=') {
      eq = i;
      break;
    }
  }
&nbsp;
  <span class="missing-if-branch" title="if path not taken" >I</span>if (eq === -1) {
<span class="cstat-no" title="statement not covered" >    return {'name': nvpair, 'value': null};</span>
  }
&nbsp;
  var name = stripquotes(nvpair.substring(0, eq).trim());
  var value = stripquotes(nvpair.substring(eq + 1).trim());
  return {'name': name, 'value': value};
}
&nbsp;
function splitHeaderParameters(hv) {
  return parseHeaderValue(hv, ';', splitNameValue);
}
&nbsp;
function parseHeaderValue(hv, separator, callback) {
  var start = 0;
  var inQuotes = false;
  var values = [];
&nbsp;
  for (var i = 0; i &lt; hv.length; i++) {
    if (hv[i] === '"') {
      inQuotes = !inQuotes;
      continue;
    }
    if (inQuotes) {
      // Just skip this
      continue;
    }
    if (hv[i] === separator) {
      var newValue = hv.substring(start, i).trim();
      <span class="missing-if-branch" title="else path not taken" >E</span>if (newValue.length &gt; 0) {
        newValue = callback(newValue);
        values.push(newValue);
      }
      start = i + 1;
    }
  }
&nbsp;
  var newValue = hv.substring(start).trim();
  <span class="missing-if-branch" title="else path not taken" >E</span>if (newValue.length &gt; 0) {
    newValue = callback(newValue);
    values.push(newValue);
  }
&nbsp;
  return values;
}
&nbsp;
function rsplit(s, delim, count) {
  var nsplits = 0;
  var end = s.length;
  var rval = [];
  for (var i = s.length - 1; i &gt;= 0; i--) {
    if (s[i] === delim) {
      var t = s.substring(i + 1, end);
      end = i;
      rval.unshift(t);
      nsplits++;
      <span class="missing-if-branch" title="else path not taken" >E</span>if (nsplits === count) {
        break;
      }
    }
  }
  <span class="missing-if-branch" title="else path not taken" >E</span>if (end !== 0) {
    rval.unshift(s.substring(0, end));
  }
  return rval;
}
&nbsp;
<span class="fstat-no" title="function not covered" >function ishex(c) {</span>
<span class="cstat-no" title="statement not covered" >  return ('0123456789ABCDEFabcdef'.indexOf(c) &gt; -1);</span>
}
&nbsp;
function unescape(s) {
  var i = 0;
  var t = '';
  while (i &lt; s.length) {
    <span class="missing-if-branch" title="else path not taken" >E</span>if (s[i] != '%' || <span class="branch-1 cbranch-no" title="branch not covered" >!ishex(s[i + 1]) </span>|| <span class="branch-2 cbranch-no" title="branch not covered" >!ishex(s[i + 2]))</span> {
      t += s[i];
    } else {
<span class="cstat-no" title="statement not covered" >      ++i;</span>
<span class="cstat-no" title="statement not covered" >      var hexvalue = '';</span>
<span class="cstat-no" title="statement not covered" >      if (i &lt; s.length) {</span>
<span class="cstat-no" title="statement not covered" >        hexvalue += s[i];</span>
<span class="cstat-no" title="statement not covered" >        ++i;</span>
      }
<span class="cstat-no" title="statement not covered" >      if (i &lt; s.length) {</span>
<span class="cstat-no" title="statement not covered" >        hexvalue += s[i];</span>
      }
<span class="cstat-no" title="statement not covered" >      if (hexvalue.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        t += new Buffer(hexvalue, 'hex').toString();</span>
      } else {
<span class="cstat-no" title="statement not covered" >        t += '%';</span>
      }
    }
&nbsp;
    ++i;
  }
  return t;
}
&nbsp;
Deserializer.ALTSVC = function readAltSvc(buffer, frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (buffer.length &lt; 2) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  var originLength = buffer.readUInt16BE(0);
  <span class="missing-if-branch" title="if path not taken" >I</span>if ((buffer.length - 2) &lt; originLength) {
<span class="cstat-no" title="statement not covered" >    return 'FRAME_SIZE_ERROR';</span>
  }
  frame.origin = buffer.toString('ascii', 2, 2 + originLength);
  var fieldValue = buffer.toString('ascii', 2 + originLength);
  var values = parseHeaderValue(fieldValue, ',', splitHeaderParameters);
  <span class="missing-if-branch" title="if path not taken" >I</span>if (values.length &gt; 1) {
    // TODO - warn that we only use one here
  }
  <span class="missing-if-branch" title="if path not taken" >I</span>if (values.length === 0) {
    // Well that's a malformed frame. Just ignore it.
<span class="cstat-no" title="statement not covered" >    return;</span>
  }
&nbsp;
  var chosenAltSvc = values[0];
  frame.maxAge = 86400; // Default
  for (var i = 0; i &lt; chosenAltSvc.length; i++) {
    if (i === 0) {
      // This corresponds to the protocolID="&lt;host&gt;:&lt;port&gt;" item
      frame.protocolID = unescape(chosenAltSvc[i].name);
      var hostport = rsplit(chosenAltSvc[i].value, ':', 1);
      frame.host = hostport[0];
      frame.port = parseInt(hostport[1], 10);
    } else <span class="missing-if-branch" title="else path not taken" >E</span>if (chosenAltSvc[i].name == 'ma') {
      frame.maxAge = parseInt(chosenAltSvc[i].value, 10);
    }
    // Otherwise, we just ignore this
  }
};
&nbsp;
// BLOCKED
// ------------------------------------------------------------
//
// The BLOCKED frame (type=0xB) indicates that the sender is unable to send data
// due to a closed flow control window.
//
// The BLOCKED frame does not define any flags and contains no payload.
&nbsp;
frameTypes[0xB] = 'BLOCKED';
&nbsp;
frameFlags.BLOCKED = [];
&nbsp;
typeSpecificAttributes.BLOCKED = [];
&nbsp;
Serializer.BLOCKED = function writeBlocked(frame, buffers) {
};
&nbsp;
Deserializer.BLOCKED = function readBlocked(buffer, frame) {
};
&nbsp;
// [Error Codes](https://tools.ietf.org/html/rfc7540#section-7)
// ------------------------------------------------------------
&nbsp;
var errorCodes = [
  'NO_ERROR',
  'PROTOCOL_ERROR',
  'INTERNAL_ERROR',
  'FLOW_CONTROL_ERROR',
  'SETTINGS_TIMEOUT',
  'STREAM_CLOSED',
  'FRAME_SIZE_ERROR',
  'REFUSED_STREAM',
  'CANCEL',
  'COMPRESSION_ERROR',
  'CONNECT_ERROR',
  'ENHANCE_YOUR_CALM',
  'INADEQUATE_SECURITY',
  'HTTP_1_1_REQUIRED'
];
&nbsp;
// Logging
// -------
&nbsp;
// [Bunyan serializers](https://github.com/trentm/node-bunyan#serializers) to improve logging output
// for debug messages emitted in this component.
exports.serializers = {};
&nbsp;
// * `frame` serializer: it transforms data attributes from Buffers to hex strings and filters out
//   flags that are not present.
var frameCounter = 0;
exports.serializers.frame = function(frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (!frame) {
<span class="cstat-no" title="statement not covered" >    return null;</span>
  }
&nbsp;
  if ('id' in frame) {
    return frame.id;
  }
&nbsp;
  frame.id = frameCounter;
  frameCounter += 1;
&nbsp;
  var logEntry = { id: frame.id };
  genericAttributes.concat(typeSpecificAttributes[frame.type]).forEach(function(name) {
    logEntry[name] = frame[name];
  });
&nbsp;
  if (frame.data instanceof Buffer) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (logEntry.data.length &gt; 50) {
<span class="cstat-no" title="statement not covered" >      logEntry.data = frame.data.slice(0, 47).toString('hex') + '...';</span>
    } else {
      logEntry.data = frame.data.toString('hex');
    }
&nbsp;
    <span class="missing-if-branch" title="else path not taken" >E</span>if (!('length' in logEntry)) {
      logEntry.length = frame.data.length;
    }
  }
&nbsp;
  <span class="missing-if-branch" title="if path not taken" >I</span>if (frame.promised_stream instanceof Object) {
<span class="cstat-no" title="statement not covered" >    logEntry.promised_stream = 'stream-' + frame.promised_stream.id;</span>
  }
&nbsp;
  logEntry.flags = Object.keys(frame.flags || {}).filter(<span class="fstat-no" title="function not covered" >function(name) {</span>
<span class="cstat-no" title="statement not covered" >    return frame.flags[name] === true;</span>
  });
&nbsp;
  return logEntry;
};
&nbsp;
// * `data` serializer: it simply transforms a buffer to a hex string.
exports.serializers.data = <span class="fstat-no" title="function not covered" >function(data) {</span>
<span class="cstat-no" title="statement not covered" >  return data.toString('hex');</span>
};
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Aug 23 2017 13:12:39 GMT-0700 (PDT)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
