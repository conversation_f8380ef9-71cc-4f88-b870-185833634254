/**
 * 枚举
 */
var pageState;//页面状态枚举
(function (pageState) {
    pageState[pageState["loading"] = 1] = "loading";
    pageState[pageState["noData"] = 2] = "noData";
    pageState[pageState["fail"] = 3] = "fail";
})(pageState || (pageState = {}));


/**
 * 加载状态动画管理
 */
avalon.component('ms-page-state', {
    template: '<div ms-visible="!!state">' +
    '<div class="noresource-bg loading" ms-visible="state==pageState.loading" ms-text="loadingText"></div>' +
    '<div class="noresource-bg noData" ms-visible="state==pageState.noData" ms-text="noDataText"></div>' +
    '<div class="noresource-bg fail" ms-visible="state==pageState.fail" ms-text="failText"></div>' +
    '</div>',
    defaults: {
        pageState: pageState,
        state: pageState.loading,//1为loading 2为暂无数据 3为加载失败 false不显示
        loadingText:"正在加载中,请稍等！",
        noDataText:"暂无数据！",
        failText:"加载失败！"
    },
    soleSlot: "text"
});