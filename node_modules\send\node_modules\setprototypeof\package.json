{"_from": "setprototypeof@1.2.0", "_id": "setprototypeof@1.2.0", "_inBundle": false, "_integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "_location": "/send/setprototypeof", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "setprototypeof@1.2.0", "name": "setprot<PERSON>of", "escapedName": "setprot<PERSON>of", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/send/http-errors"], "_resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "_shasum": "66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424", "_spec": "setprototypeof@1.2.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\send\\node_modules\\http-errors", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/wesleytodd/setprototypeof/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A small polyfill for Object.setprototypeof", "devDependencies": {"mocha": "^6.1.4", "standard": "^13.0.2"}, "homepage": "https://github.com/wesleytodd/setprototypeof", "keywords": ["polyfill", "object", "setprot<PERSON>of"], "license": "ISC", "main": "index.js", "name": "setprot<PERSON>of", "repository": {"type": "git", "url": "git+https://github.com/wesleytodd/setprototypeof.git"}, "scripts": {"node010": "NODE_VER=0.10 MOCHA_VER=3 npm run testversion", "node11": "NODE_VER=11 npm run testversion", "node4": "NODE_VER=4 npm run testversion", "node6": "NODE_VER=6 npm run testversion", "node9": "NODE_VER=9 npm run testversion", "postpublish": "git push origin && git push origin --tags", "prepublishOnly": "npm t", "test": "standard && mocha", "testallversions": "npm run node010 && npm run node4 && npm run node6 && npm run node9 && npm run node11", "testversion": "docker run -it --rm -v $(PWD):/usr/src/app -w /usr/src/app node:${NODE_VER} npm install mocha@${MOCHA_VER:-latest} && npm t"}, "typings": "index.d.ts", "version": "1.2.0"}