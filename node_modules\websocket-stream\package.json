{"_from": "websocket-stream@^5.0.1", "_id": "websocket-stream@5.5.2", "_inBundle": false, "_integrity": "sha512-8z49MKIHbGk3C4HtuHWDtYX8mYej1wWabjthC/RupM9ngeukU4IWoM46dgth1UOS/T4/IqgEdCDJuMe2039OQQ==", "_location": "/websocket-stream", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "websocket-stream@^5.0.1", "name": "websocket-stream", "escapedName": "websocket-stream", "rawSpec": "^5.0.1", "saveSpec": null, "fetchSpec": "^5.0.1"}, "_requiredBy": ["/node-http2"], "_resolved": "https://registry.npmjs.org/websocket-stream/-/websocket-stream-5.5.2.tgz", "_shasum": "49d87083d96839f0648f5513bbddd581f496b8a2", "_spec": "websocket-stream@^5.0.1", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\node-http2", "author": "", "browser": {"./echo-server.js": "./fake-server.js", "./index.js": "./stream.js", "ws": "./ws-fallback.js"}, "bugs": {"url": "https://github.com/maxogden/websocket-stream/issues"}, "bundleDependencies": false, "dependencies": {"duplexify": "^3.5.1", "inherits": "^2.0.1", "readable-stream": "^2.3.3", "safe-buffer": "^5.1.2", "ws": "^3.2.0", "xtend": "^4.0.0"}, "deprecated": false, "description": "Use websockets with the node streams API. Works in browser and node", "devDependencies": {"@types/node": "^11.13.4", "@types/ws": "^6.0.1", "beefy": "^2.1.8", "browserify": "^16.2.3", "concat-stream": "^1.6.2", "tape": "^4.9.1", "typescript": "^3.4.3"}, "homepage": "https://github.com/maxogden/websocket-stream#readme", "keywords": ["websocket", "websockets", "stream", "streams", "realtime"], "license": "BSD-2-<PERSON><PERSON>", "main": "index.js", "name": "websocket-stream", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+ssh://**************/maxogden/websocket-stream.git"}, "scripts": {"start": "beefy test-client.js", "test": "node test.js"}, "version": "5.5.2"}