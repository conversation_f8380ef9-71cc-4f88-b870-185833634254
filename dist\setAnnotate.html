<!DOCTYPE HTML><html><head><title>设置标注</title><link rel="stylesheet" href="css/base.css"><link rel="stylesheet" href="css/testbank/common.css"><link rel="stylesheet" href="css/testbank/testbank.css"><link rel="stylesheet" href="js/thirdLib/layui/css/layui.css"><link rel="stylesheet" href="css/avalonComponent.css"><link rel="stylesheet" href="css/zTreeStyle.css"><link rel="stylesheet" href="css/testbank/setAnnotate.css"></head><body><div id="container"><div class="set-line"><label>已选择{{selectQues.length}}道题</label></div><div class="set-main"><div class="set-main-left"><div class="set-selected-know"><div class="set-selected-operation"><span>已选中&nbsp;<span style="color:#3e73f6">{{pointIdandName.length}}</span>&nbsp;个知识点</span><div class="set-selected-delete" v-show="pointIdandName.length > 0" @click="deleteKnowlwdge('big','','')"></div></div><div class="set-point"><div v-for="(point,pIndex) in pointIdandName" :key="pIndex"><ul class="set-point-list"><li class="set-li"><span>{{point.name}}</span></li><span class="set-point-delete" @click="deleteKnowlwdge('small',pIndex,point.id)"></span></ul></div></div></div><div class="set-selected-annotate"><div class="set-selected-operation"><span>已选中&nbsp;<span style="color:#3e73f6">{{qualityIdandName.length}}</span>&nbsp;个核心素养</span><div class="set-selected-delete" v-show="qualityIdandName.length > 0" @click="deleteQuality('big','','')"></div></div><div class="set-quality"><div v-for="(quality,qIndex) in qualityIdandName" :key="qIndex"><ul class="set-point-list"><li class="set-li">{{quality.name}}</li><span class="set-point-delete" @click="deleteQuality('small',qIndex,quality.id)"></span></ul></div></div></div></div><div class="set-main-right"><div class="set-right-top"><div :class="['set-change-knowledge',{ 'knowledge': selected == 'knowledge' }]" @click="changeKnowledge"><span>知识点</span></div><div :class="['set-change-annotate',{ 'quality': selected == 'quality' }]" @click="changeQuality"><span>核心素养</span></div></div><div class="search-input" style="width:50%" v-if="selected === 'knowledge'"><input type="text" type="text" id="knowledgeKey" value="" maxlength="100" placeholder="请输入关键词搜索" class="search-input-inner"> <span class="input-suffix"><span class="input-suffix-inner"><div class="search-btn search-icon" @click="searchKnowledge"></div></span></span></div><ul id="tree" class="ztree" style="max-height:327px;overflow-y:auto"></ul></div></div><div class="set-operation"><a class="cancel" @click="close">取消</a> <a class="set-operation-ok" @click="saveQualityKnowledge">确定</a></div></div></body></html><script type="text/javascript" src="js/thirdLib/vue.js"></script><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script src="js/util/common.js"></script><script src="js/util/ajax.js"></script><script src="js/thirdLib/layui/layui.js"></script><script src="js/thirdLib/layui/layui-config.js"></script><script src="js/thirdLib/avalon/avalon.js"></script><script src="js/thirdLib/avalon/component.js"></script><script src="js/testbank/setAnnotate.js"></script><script src="js/thirdLib/ztree/jquery.ztree.all.min.js"></script><script src="js/thirdLib/ztree/jquery.ztree.exhide.js"></script><script src="js/thirdLib/ztree/fuzzysearch.js"></script><script src="js/thirdLib/ztree/tree.js"></script>