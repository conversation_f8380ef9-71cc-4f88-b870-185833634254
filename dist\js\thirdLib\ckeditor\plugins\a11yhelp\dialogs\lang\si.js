"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang("a11yhelp", "si", {
  title: "ළඟා වියහැකි ",
  contents: "උදව් සඳහා අන්තර්ගතය.නික්මයෙමට ESC බොත්තම ඔබන්න",
  legend: [{
    name: "පොදු කරුණු",
    items: [{
      name: "සංස්කරණ මෙවලම් ",
      legend: "ඔබන්න ${මෙවලම් තීරු අවධානය} මෙවලම් තීරුවේ එහා මෙහා යෑමට.ඉදිරියට යෑමට හා ආපසු යෑමට මෙවලම් තීරුකාණ්ඩය හා TAB හා SHIFT+TAB .ඉදිරියට යෑමට හා ආපසු යෑමට මෙවලම් තීරු බොත්තම සමග RIGHT ARROW හෝ LEFT ARROW.මෙවලම් තීරු බොත්තම සක්‍රිය කර ගැනීමට SPACE හෝ ENTER බොත්තම ඔබන්න."
    }, {
      name: "සංස්කරණ ",
      legend: "Inside a dialog, press TAB to navigate to the next dialog element, press SHIFT+TAB to move to the previous dialog element, press ENTER to submit the dialog, press ESC to cancel the dialog. When a dialog has multiple tabs, the tab list can be reached either with ALT+F10 or with TAB as part of the dialog tabbing order. With tab list focused, move to the next and previous tab with RIGHT and LEFT ARROW, respectively. Press ESC to discard changes and close the dialog. The focus will be moved back to the editing area upon leaving the dialog."
    }, {
      name: "සංස්කරණ අඩංගුවට ",
      legend: "ඔබන්න ${අන්තර්ගත මෙනුව} හෝ  APPLICATION KEY  අන්තර්ගත-මෙනුව විවුරතකිරීමට. ඊළඟ මෙනුව-ව්කල්පයන්ට යෑමට TAB හෝ DOWN ARROW බොත්තම ද, පෙර විකල්පයන්ටයෑමට SHIFT+TAB හෝ  UP ARROW බොත්තම ද, මෙනුව-ව්කල්පයන් තේරීමට SPACE හෝ ENTER බොත්තම ද,  දැනට විවුර්තව ඇති උප-මෙනුවක වීකල්ප තේරීමට SPACE හෝ ENTER හෝ RIGHT ARROW ද, නැවත පෙර ප්‍රධාන මෙනුවට යෑමට  ESC හෝ LEFT ARROW බොත්තම ද.  අන්තර්ගත-මෙනුව වැසීමට  ESC බොත්තම ද ඔබන්න."
    }, {
      name: "සංස්කරණ තේරුම් ",
      legend: "තේරුම් කොටුව තුළ , ඊළඟ අයිතමයට යෑමට TAB හෝ DOWN ARROW , පෙර අයිතමයට යෑමට SHIFT+TAB හෝ UP ARROW . අයිතම විකල්පයන් තේරීමට SPACE හෝ ENTER ,තේරුම් කොටුව වැසීමට ESC බොත්තම් ද ඔබන්න."
    }, {
      name: "සංස්කරණ අංග සහිත ",
      legend: "ඔබන්න ${මෙවලම් තීරු අවධානය} මෙවලම් තීරුවේ එහා මෙහා යෑමට.ඉදිරියට යෑමට හා ආපසු යෑමට මෙවලම් තීරුකාණ්ඩය හා TAB හා SHIFT+TAB .ඉදිරියට යෑමට හා ආපසු යෑමට මෙවලම් තීරු බොත්තම සමග RIGHT ARROW හෝ LEFT ARROW.මෙවලම් තීරු බොත්තම සක්‍රිය කර ගැනීමට SPACE හෝ ENTER බොත්තම ඔබන්න."
    }]
  }, {
    name: "විධාන",
    items: [{
      name: "විධානය වෙනස් ",
      legend: "ඔබන්න ${වෙනස් කිරීම}"
    }, {
      name: "විධාන නැවත් පෙර පරිදිම වෙනස්කර ගැනීම.",
      legend: "ඔබන්න ${නැවත් පෙර පරිදිම වෙනස්කර ගැනීම}"
    }, {
      name: "තද අකුරින් විධාන",
      legend: "ඔබන්න ${තද }"
    }, {
      name: "බැධී අකුරු විධාන",
      legend: "ඔබන්න ${බැධී අකුරු }"
    }, {
      name: "යටින් ඉරි ඇද ඇති විධාන.",
      legend: "ඔබන්න ${යටින් ඉරි ඇද ඇති}"
    }, {
      name: "සම්බන්ධිත විධාන",
      legend: "ඔබන්න ${සම්බන්ධ }"
    }, {
      name: "මෙවලම් තීරු හැකුලුම් විධාන",
      legend: "ඔබන්න ${මෙවලම් තීරු හැකුලුම් }"
    }, {
      name: "යොමුවීමට පෙර  වැදගත්  විධාන",
      legend: "ඔබන්න ${යොමුවීමට ඊළඟ }"
    }, {
      name: "යොමුවීමට ඊළග වැදගත්  විධාන",
      legend: "ඔබන්න ${යොමුවීමට ඊළඟ }"
    }, {
      name: "ප්‍රවේශ ",
      legend: "ඔබන්න  ${a11y }"
    }, {
      name: " Paste as plain text",
      legend: "Press ${pastetext}",
      legendEdge: "Press ${pastetext}, followed by ${paste}"
    }]
  }],
  tab: "Tab",
  pause: "Pause",
  capslock: "Caps Lock",
  escape: "Escape",
  pageUp: "Page Up",
  pageDown: "Page Down",
  leftArrow: "Left Arrow",
  upArrow: "Up Arrow",
  rightArrow: "Right Arrow",
  downArrow: "Down Arrow",
  insert: "Insert",
  leftWindowKey: "Left Windows key",
  rightWindowKey: "Right Windows key",
  selectKey: "Select key",
  numpad0: "Numpad 0",
  numpad1: "Numpad 1",
  numpad2: "Numpad 2",
  numpad3: "Numpad 3",
  numpad4: "Numpad 4",
  numpad5: "Numpad 5",
  numpad6: "Numpad 6",
  numpad7: "Numpad 7",
  numpad8: "Numpad 8",
  numpad9: "Numpad 9",
  multiply: "Multiply",
  add: "Add",
  subtract: "Subtract",
  decimalPoint: "Decimal Point",
  divide: "Divide",
  f1: "F1",
  f2: "F2",
  f3: "F3",
  f4: "F4",
  f5: "F5",
  f6: "F6",
  f7: "F7",
  f8: "F8",
  f9: "F9",
  f10: "F10",
  f11: "F11",
  f12: "F12",
  numLock: "Num Lock",
  scrollLock: "Scroll Lock",
  semiColon: "Semicolon",
  equalSign: "Equal Sign",
  comma: "Comma",
  dash: "Dash",
  period: "Period",
  forwardSlash: "Forward Slash",
  graveAccent: "Grave Accent",
  openBracket: "Open Bracket",
  backSlash: "Backslash",
  closeBracket: "Close Bracket",
  singleQuote: "Single Quote"
});
