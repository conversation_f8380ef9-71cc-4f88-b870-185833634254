"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
/*
 * JQuery zTree core v3.5.28
 * http://treejs.cn/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2017-01-20
 */
(function (_$) {
  var settings = {},
    roots = {},
    caches = {},
    //default consts of core
    _consts = {
      className: {
        BUTTON: "button",
        LEVEL: "level",
        ICO_LOADING: "ico_loading",
        SWITCH: "switch",
        NAME: 'node_name'
      },
      event: {
        NODECREATED: "ztree_nodeCreated",
        CLICK: "ztree_click",
        EXPAND: "ztree_expand",
        COLLAPSE: "ztree_collapse",
        ASYNC_SUCCESS: "ztree_async_success",
        ASYNC_ERROR: "ztree_async_error",
        REMOVE: "ztree_remove",
        SELECTED: "ztree_selected",
        UNSELECTED: "ztree_unselected"
      },
      id: {
        A: "_a",
        ICON: "_ico",
        SPAN: "_span",
        SWITCH: "_switch",
        UL: "_ul"
      },
      line: {
        ROOT: "root",
        ROOTS: "roots",
        CENTER: "center",
        BOTTOM: "bottom",
        NOLINE: "noline",
        LINE: "line"
      },
      folder: {
        OPEN: "open",
        CLOSE: "close",
        DOCU: "docu"
      },
      node: {
        CURSELECTED: "curSelectedNode"
      }
    },
    //default setting of core
    _setting = {
      treeId: "",
      treeObj: null,
      view: {
        addDiyDom: null,
        autoCancelSelected: true,
        dblClickExpand: true,
        expandSpeed: "fast",
        fontCss: {},
        nameIsHTML: false,
        selectedMulti: true,
        showIcon: true,
        showLine: true,
        showTitle: true,
        txtSelectedEnable: false
      },
      data: {
        key: {
          children: "children",
          name: "name",
          title: "",
          url: "url",
          icon: "icon"
        },
        simpleData: {
          enable: false,
          idKey: "id",
          pIdKey: "pId",
          rootPId: null
        },
        keep: {
          parent: false,
          leaf: false
        }
      },
      async: {
        enable: false,
        contentType: "application/x-www-form-urlencoded",
        type: "post",
        dataType: "text",
        url: "",
        autoParam: [],
        otherParam: [],
        dataFilter: null
      },
      callback: {
        beforeAsync: null,
        beforeClick: null,
        beforeDblClick: null,
        beforeRightClick: null,
        beforeMouseDown: null,
        beforeMouseUp: null,
        beforeExpand: null,
        beforeCollapse: null,
        beforeRemove: null,
        onAsyncError: null,
        onAsyncSuccess: null,
        onNodeCreated: null,
        onClick: null,
        onDblClick: null,
        onRightClick: null,
        onMouseDown: null,
        onMouseUp: null,
        onExpand: null,
        onCollapse: null,
        onRemove: null
      }
    },
    //default root of core
    //zTree use root to save full data
    _initRoot = function _initRoot(setting) {
      var r = data.getRoot(setting);
      if (!r) {
        r = {};
        data.setRoot(setting, r);
      }
      r[setting.data.key.children] = [];
      r.expandTriggerFlag = false;
      r.curSelectedList = [];
      r.noSelection = true;
      r.createdNodes = [];
      r.zId = 0;
      r._ver = new Date().getTime();
    },
    //default cache of core
    _initCache = function _initCache(setting) {
      var c = data.getCache(setting);
      if (!c) {
        c = {};
        data.setCache(setting, c);
      }
      c.nodes = [];
      c.doms = [];
    },
    //default bindEvent of core
    _bindEvent = function _bindEvent(setting) {
      var o = setting.treeObj,
        c = consts.event;
      o.bind(c.NODECREATED, function (event, treeId, node) {
        tools.apply(setting.callback.onNodeCreated, [event, treeId, node]);
      });
      o.bind(c.CLICK, function (event, srcEvent, treeId, node, clickFlag) {
        tools.apply(setting.callback.onClick, [srcEvent, treeId, node, clickFlag]);
      });
      o.bind(c.EXPAND, function (event, treeId, node) {
        tools.apply(setting.callback.onExpand, [event, treeId, node]);
      });
      o.bind(c.COLLAPSE, function (event, treeId, node) {
        tools.apply(setting.callback.onCollapse, [event, treeId, node]);
      });
      o.bind(c.ASYNC_SUCCESS, function (event, treeId, node, msg) {
        tools.apply(setting.callback.onAsyncSuccess, [event, treeId, node, msg]);
      });
      o.bind(c.ASYNC_ERROR, function (event, treeId, node, XMLHttpRequest, textStatus, errorThrown) {
        tools.apply(setting.callback.onAsyncError, [event, treeId, node, XMLHttpRequest, textStatus, errorThrown]);
      });
      o.bind(c.REMOVE, function (event, treeId, treeNode) {
        tools.apply(setting.callback.onRemove, [event, treeId, treeNode]);
      });
      o.bind(c.SELECTED, function (event, treeId, node) {
        tools.apply(setting.callback.onSelected, [treeId, node]);
      });
      o.bind(c.UNSELECTED, function (event, treeId, node) {
        tools.apply(setting.callback.onUnSelected, [treeId, node]);
      });
    },
    _unbindEvent = function _unbindEvent(setting) {
      var o = setting.treeObj,
        c = consts.event;
      o.unbind(c.NODECREATED).unbind(c.CLICK).unbind(c.EXPAND).unbind(c.COLLAPSE).unbind(c.ASYNC_SUCCESS).unbind(c.ASYNC_ERROR).unbind(c.REMOVE).unbind(c.SELECTED).unbind(c.UNSELECTED);
    },
    //default event proxy of core
    _eventProxy = function _eventProxy(event) {
      var target = event.target,
        setting = data.getSetting(event.data.treeId),
        tId = "",
        node = null,
        nodeEventType = "",
        treeEventType = "",
        nodeEventCallback = null,
        treeEventCallback = null,
        tmp = null;
      if (tools.eqs(event.type, "mousedown")) {
        treeEventType = "mousedown";
      } else if (tools.eqs(event.type, "mouseup")) {
        treeEventType = "mouseup";
      } else if (tools.eqs(event.type, "contextmenu")) {
        treeEventType = "contextmenu";
      } else if (tools.eqs(event.type, "click")) {
        if (tools.eqs(target.tagName, "span") && target.getAttribute("treeNode" + consts.id.SWITCH) !== null) {
          tId = tools.getNodeMainDom(target).id;
          nodeEventType = "switchNode";
        } else {
          tmp = tools.getMDom(setting, target, [{
            tagName: "a",
            attrName: "treeNode" + consts.id.A
          }]);
          if (tmp) {
            tId = tools.getNodeMainDom(tmp).id;
            nodeEventType = "clickNode";
          }
        }
      } else if (tools.eqs(event.type, "dblclick")) {
        treeEventType = "dblclick";
        tmp = tools.getMDom(setting, target, [{
          tagName: "a",
          attrName: "treeNode" + consts.id.A
        }]);
        if (tmp) {
          tId = tools.getNodeMainDom(tmp).id;
          nodeEventType = "switchNode";
        }
      }
      if (treeEventType.length > 0 && tId.length == 0) {
        tmp = tools.getMDom(setting, target, [{
          tagName: "a",
          attrName: "treeNode" + consts.id.A
        }]);
        if (tmp) {
          tId = tools.getNodeMainDom(tmp).id;
        }
      }
      // event to node
      if (tId.length > 0) {
        node = data.getNodeCache(setting, tId);
        switch (nodeEventType) {
          case "switchNode":
            if (!node.isParent) {
              nodeEventType = "";
            } else if (tools.eqs(event.type, "click") || tools.eqs(event.type, "dblclick") && tools.apply(setting.view.dblClickExpand, [setting.treeId, node], setting.view.dblClickExpand)) {
              nodeEventCallback = handler.onSwitchNode;
            } else {
              nodeEventType = "";
            }
            break;
          case "clickNode":
            nodeEventCallback = handler.onClickNode;
            break;
        }
      }
      // event to zTree
      switch (treeEventType) {
        case "mousedown":
          treeEventCallback = handler.onZTreeMousedown;
          break;
        case "mouseup":
          treeEventCallback = handler.onZTreeMouseup;
          break;
        case "dblclick":
          treeEventCallback = handler.onZTreeDblclick;
          break;
        case "contextmenu":
          treeEventCallback = handler.onZTreeContextmenu;
          break;
      }
      var proxyResult = {
        stop: false,
        node: node,
        nodeEventType: nodeEventType,
        nodeEventCallback: nodeEventCallback,
        treeEventType: treeEventType,
        treeEventCallback: treeEventCallback
      };
      return proxyResult;
    },
    //default init node of core
    _initNode = function _initNode(setting, level, n, parentNode, isFirstNode, isLastNode, openFlag) {
      if (!n) return;
      var r = data.getRoot(setting),
        childKey = setting.data.key.children;
      n.level = level;
      n.tId = setting.treeId + "_" + ++r.zId;
      n.parentTId = parentNode ? parentNode.tId : null;
      n.open = typeof n.open == "string" ? tools.eqs(n.open, "true") : !!n.open;
      if (n[childKey] && n[childKey].length > 0) {
        n.isParent = true;
        n.zAsync = true;
      } else {
        n.isParent = typeof n.isParent == "string" ? tools.eqs(n.isParent, "true") : !!n.isParent;
        n.open = n.isParent && !setting.async.enable ? n.open : false;
        n.zAsync = !n.isParent;
      }
      n.isFirstNode = isFirstNode;
      n.isLastNode = isLastNode;
      n.getParentNode = function () {
        return data.getNodeCache(setting, n.parentTId);
      };
      n.getPreNode = function () {
        return data.getPreNode(setting, n);
      };
      n.getNextNode = function () {
        return data.getNextNode(setting, n);
      };
      n.getIndex = function () {
        return data.getNodeIndex(setting, n);
      };
      n.getPath = function () {
        return data.getNodePath(setting, n);
      };
      n.isAjaxing = false;
      data.fixPIdKeyValue(setting, n);
    },
    _init = {
      bind: [_bindEvent],
      unbind: [_unbindEvent],
      caches: [_initCache],
      nodes: [_initNode],
      proxys: [_eventProxy],
      roots: [_initRoot],
      beforeA: [],
      afterA: [],
      innerBeforeA: [],
      innerAfterA: [],
      zTreeTools: []
    },
    //method of operate data
    data = {
      addNodeCache: function addNodeCache(setting, node) {
        data.getCache(setting).nodes[data.getNodeCacheId(node.tId)] = node;
      },
      getNodeCacheId: function getNodeCacheId(tId) {
        return tId.substring(tId.lastIndexOf("_") + 1);
      },
      addAfterA: function addAfterA(afterA) {
        _init.afterA.push(afterA);
      },
      addBeforeA: function addBeforeA(beforeA) {
        _init.beforeA.push(beforeA);
      },
      addInnerAfterA: function addInnerAfterA(innerAfterA) {
        _init.innerAfterA.push(innerAfterA);
      },
      addInnerBeforeA: function addInnerBeforeA(innerBeforeA) {
        _init.innerBeforeA.push(innerBeforeA);
      },
      addInitBind: function addInitBind(bindEvent) {
        _init.bind.push(bindEvent);
      },
      addInitUnBind: function addInitUnBind(unbindEvent) {
        _init.unbind.push(unbindEvent);
      },
      addInitCache: function addInitCache(initCache) {
        _init.caches.push(initCache);
      },
      addInitNode: function addInitNode(initNode) {
        _init.nodes.push(initNode);
      },
      addInitProxy: function addInitProxy(initProxy, isFirst) {
        if (!!isFirst) {
          _init.proxys.splice(0, 0, initProxy);
        } else {
          _init.proxys.push(initProxy);
        }
      },
      addInitRoot: function addInitRoot(initRoot) {
        _init.roots.push(initRoot);
      },
      addNodesData: function addNodesData(setting, parentNode, index, nodes) {
        var childKey = setting.data.key.children,
          params;
        if (!parentNode[childKey]) {
          parentNode[childKey] = [];
          index = -1;
        } else if (index >= parentNode[childKey].length) {
          index = -1;
        }
        if (parentNode[childKey].length > 0 && index === 0) {
          parentNode[childKey][0].isFirstNode = false;
          view.setNodeLineIcos(setting, parentNode[childKey][0]);
        } else if (parentNode[childKey].length > 0 && index < 0) {
          parentNode[childKey][parentNode[childKey].length - 1].isLastNode = false;
          view.setNodeLineIcos(setting, parentNode[childKey][parentNode[childKey].length - 1]);
        }
        parentNode.isParent = true;
        if (index < 0) {
          parentNode[childKey] = parentNode[childKey].concat(nodes);
        } else {
          params = [index, 0].concat(nodes);
          parentNode[childKey].splice.apply(parentNode[childKey], params);
        }
      },
      addSelectedNode: function addSelectedNode(setting, node) {
        var root = data.getRoot(setting);
        if (!data.isSelectedNode(setting, node)) {
          root.curSelectedList.push(node);
        }
      },
      addCreatedNode: function addCreatedNode(setting, node) {
        if (!!setting.callback.onNodeCreated || !!setting.view.addDiyDom) {
          var root = data.getRoot(setting);
          root.createdNodes.push(node);
        }
      },
      addZTreeTools: function addZTreeTools(zTreeTools) {
        _init.zTreeTools.push(zTreeTools);
      },
      exSetting: function exSetting(s) {
        _$.extend(true, _setting, s);
      },
      fixPIdKeyValue: function fixPIdKeyValue(setting, node) {
        if (setting.data.simpleData.enable) {
          node[setting.data.simpleData.pIdKey] = node.parentTId ? node.getParentNode()[setting.data.simpleData.idKey] : setting.data.simpleData.rootPId;
        }
      },
      getAfterA: function getAfterA(setting, node, array) {
        for (var i = 0, j = _init.afterA.length; i < j; i++) {
          _init.afterA[i].apply(this, arguments);
        }
      },
      getBeforeA: function getBeforeA(setting, node, array) {
        for (var i = 0, j = _init.beforeA.length; i < j; i++) {
          _init.beforeA[i].apply(this, arguments);
        }
      },
      getInnerAfterA: function getInnerAfterA(setting, node, array) {
        for (var i = 0, j = _init.innerAfterA.length; i < j; i++) {
          _init.innerAfterA[i].apply(this, arguments);
        }
      },
      getInnerBeforeA: function getInnerBeforeA(setting, node, array) {
        for (var i = 0, j = _init.innerBeforeA.length; i < j; i++) {
          _init.innerBeforeA[i].apply(this, arguments);
        }
      },
      getCache: function getCache(setting) {
        return caches[setting.treeId];
      },
      getNodeIndex: function getNodeIndex(setting, node) {
        if (!node) return null;
        var childKey = setting.data.key.children,
          p = node.parentTId ? node.getParentNode() : data.getRoot(setting);
        for (var i = 0, l = p[childKey].length - 1; i <= l; i++) {
          if (p[childKey][i] === node) {
            return i;
          }
        }
        return -1;
      },
      getNextNode: function getNextNode(setting, node) {
        if (!node) return null;
        var childKey = setting.data.key.children,
          p = node.parentTId ? node.getParentNode() : data.getRoot(setting);
        for (var i = 0, l = p[childKey].length - 1; i <= l; i++) {
          if (p[childKey][i] === node) {
            return i == l ? null : p[childKey][i + 1];
          }
        }
        return null;
      },
      getNodeByParam: function getNodeByParam(setting, nodes, key, value) {
        if (!nodes || !key) return null;
        var childKey = setting.data.key.children;
        for (var i = 0, l = nodes.length; i < l; i++) {
          if (nodes[i][key] == value) {
            return nodes[i];
          }
          var tmp = data.getNodeByParam(setting, nodes[i][childKey], key, value);
          if (tmp) return tmp;
        }
        return null;
      },
      getNodeCache: function getNodeCache(setting, tId) {
        if (!tId) return null;
        var n = caches[setting.treeId].nodes[data.getNodeCacheId(tId)];
        return n ? n : null;
      },
      getNodeName: function getNodeName(setting, node) {
        var nameKey = setting.data.key.name;
        return "" + node[nameKey];
      },
      getNodePath: function getNodePath(setting, node) {
        if (!node) return null;
        var path;
        if (node.parentTId) {
          path = node.getParentNode().getPath();
        } else {
          path = [];
        }
        if (path) {
          path.push(node);
        }
        return path;
      },
      getNodeTitle: function getNodeTitle(setting, node) {
        var t = setting.data.key.title === "" ? setting.data.key.name : setting.data.key.title;
        return "" + node[t];
      },
      getNodes: function getNodes(setting) {
        return data.getRoot(setting)[setting.data.key.children];
      },
      getNodesByParam: function getNodesByParam(setting, nodes, key, value) {
        if (!nodes || !key) return [];
        var childKey = setting.data.key.children,
          result = [];
        for (var i = 0, l = nodes.length; i < l; i++) {
          if (nodes[i][key] == value) {
            result.push(nodes[i]);
          }
          result = result.concat(data.getNodesByParam(setting, nodes[i][childKey], key, value));
        }
        return result;
      },
      getNodesByParamFuzzy: function getNodesByParamFuzzy(setting, nodes, key, value) {
        if (!nodes || !key) return [];
        var childKey = setting.data.key.children,
          result = [];
        value = value.toLowerCase();
        for (var i = 0, l = nodes.length; i < l; i++) {
          if (typeof nodes[i][key] == "string" && nodes[i][key].toLowerCase().indexOf(value) > -1) {
            result.push(nodes[i]);
          }
          result = result.concat(data.getNodesByParamFuzzy(setting, nodes[i][childKey], key, value));
        }
        return result;
      },
      getNodesByFilter: function getNodesByFilter(setting, nodes, filter, isSingle, invokeParam) {
        if (!nodes) return isSingle ? null : [];
        var childKey = setting.data.key.children,
          result = isSingle ? null : [];
        for (var i = 0, l = nodes.length; i < l; i++) {
          if (tools.apply(filter, [nodes[i], invokeParam], false)) {
            if (isSingle) {
              return nodes[i];
            }
            result.push(nodes[i]);
          }
          var tmpResult = data.getNodesByFilter(setting, nodes[i][childKey], filter, isSingle, invokeParam);
          if (isSingle && !!tmpResult) {
            return tmpResult;
          }
          result = isSingle ? tmpResult : result.concat(tmpResult);
        }
        return result;
      },
      getPreNode: function getPreNode(setting, node) {
        if (!node) return null;
        var childKey = setting.data.key.children,
          p = node.parentTId ? node.getParentNode() : data.getRoot(setting);
        for (var i = 0, l = p[childKey].length; i < l; i++) {
          if (p[childKey][i] === node) {
            return i == 0 ? null : p[childKey][i - 1];
          }
        }
        return null;
      },
      getRoot: function getRoot(setting) {
        return setting ? roots[setting.treeId] : null;
      },
      getRoots: function getRoots() {
        return roots;
      },
      getSetting: function getSetting(treeId) {
        return settings[treeId];
      },
      getSettings: function getSettings() {
        return settings;
      },
      getZTreeTools: function getZTreeTools(treeId) {
        var r = this.getRoot(this.getSetting(treeId));
        return r ? r.treeTools : null;
      },
      initCache: function initCache(setting) {
        for (var i = 0, j = _init.caches.length; i < j; i++) {
          _init.caches[i].apply(this, arguments);
        }
      },
      initNode: function initNode(setting, level, node, parentNode, preNode, nextNode) {
        for (var i = 0, j = _init.nodes.length; i < j; i++) {
          _init.nodes[i].apply(this, arguments);
        }
      },
      initRoot: function initRoot(setting) {
        for (var i = 0, j = _init.roots.length; i < j; i++) {
          _init.roots[i].apply(this, arguments);
        }
      },
      isSelectedNode: function isSelectedNode(setting, node) {
        var root = data.getRoot(setting);
        for (var i = 0, j = root.curSelectedList.length; i < j; i++) {
          if (node === root.curSelectedList[i]) return true;
        }
        return false;
      },
      removeNodeCache: function removeNodeCache(setting, node) {
        var childKey = setting.data.key.children;
        if (node[childKey]) {
          for (var i = 0, l = node[childKey].length; i < l; i++) {
            data.removeNodeCache(setting, node[childKey][i]);
          }
        }
        data.getCache(setting).nodes[data.getNodeCacheId(node.tId)] = null;
      },
      removeSelectedNode: function removeSelectedNode(setting, node) {
        var root = data.getRoot(setting);
        for (var i = 0, j = root.curSelectedList.length; i < j; i++) {
          if (node === root.curSelectedList[i] || !data.getNodeCache(setting, root.curSelectedList[i].tId)) {
            root.curSelectedList.splice(i, 1);
            setting.treeObj.trigger(consts.event.UNSELECTED, [setting.treeId, node]);
            i--;
            j--;
          }
        }
      },
      setCache: function setCache(setting, cache) {
        caches[setting.treeId] = cache;
      },
      setRoot: function setRoot(setting, root) {
        roots[setting.treeId] = root;
      },
      setZTreeTools: function setZTreeTools(setting, zTreeTools) {
        for (var i = 0, j = _init.zTreeTools.length; i < j; i++) {
          _init.zTreeTools[i].apply(this, arguments);
        }
      },
      transformToArrayFormat: function transformToArrayFormat(setting, nodes) {
        if (!nodes) return [];
        var childKey = setting.data.key.children,
          r = [];
        if (tools.isArray(nodes)) {
          for (var i = 0, l = nodes.length; i < l; i++) {
            r.push(nodes[i]);
            if (nodes[i][childKey]) r = r.concat(data.transformToArrayFormat(setting, nodes[i][childKey]));
          }
        } else {
          r.push(nodes);
          if (nodes[childKey]) r = r.concat(data.transformToArrayFormat(setting, nodes[childKey]));
        }
        return r;
      },
      transformTozTreeFormat: function transformTozTreeFormat(setting, sNodes) {
        var i,
          l,
          key = setting.data.simpleData.idKey,
          parentKey = setting.data.simpleData.pIdKey,
          childKey = setting.data.key.children;
        if (!key || key == "" || !sNodes) return [];
        if (tools.isArray(sNodes)) {
          var r = [];
          var tmpMap = {};
          for (i = 0, l = sNodes.length; i < l; i++) {
            tmpMap[sNodes[i][key]] = sNodes[i];
          }
          for (i = 0, l = sNodes.length; i < l; i++) {
            if (tmpMap[sNodes[i][parentKey]] && sNodes[i][key] != sNodes[i][parentKey]) {
              if (!tmpMap[sNodes[i][parentKey]][childKey]) tmpMap[sNodes[i][parentKey]][childKey] = [];
              tmpMap[sNodes[i][parentKey]][childKey].push(sNodes[i]);
            } else {
              r.push(sNodes[i]);
            }
          }
          return r;
        } else {
          return [sNodes];
        }
      }
    },
    //method of event proxy
    event = {
      bindEvent: function bindEvent(setting) {
        for (var i = 0, j = _init.bind.length; i < j; i++) {
          _init.bind[i].apply(this, arguments);
        }
      },
      unbindEvent: function unbindEvent(setting) {
        for (var i = 0, j = _init.unbind.length; i < j; i++) {
          _init.unbind[i].apply(this, arguments);
        }
      },
      bindTree: function bindTree(setting) {
        var eventParam = {
            treeId: setting.treeId
          },
          o = setting.treeObj;
        if (!setting.view.txtSelectedEnable) {
          // for can't select text
          o.bind('selectstart', handler.onSelectStart).css({
            "-moz-user-select": "-moz-none"
          });
        }
        o.bind('click', eventParam, event.proxy);
        o.bind('dblclick', eventParam, event.proxy);
        o.bind('mouseover', eventParam, event.proxy);
        o.bind('mouseout', eventParam, event.proxy);
        o.bind('mousedown', eventParam, event.proxy);
        o.bind('mouseup', eventParam, event.proxy);
        o.bind('contextmenu', eventParam, event.proxy);
      },
      unbindTree: function unbindTree(setting) {
        var o = setting.treeObj;
        o.unbind('selectstart', handler.onSelectStart).unbind('click', event.proxy).unbind('dblclick', event.proxy).unbind('mouseover', event.proxy).unbind('mouseout', event.proxy).unbind('mousedown', event.proxy).unbind('mouseup', event.proxy).unbind('contextmenu', event.proxy);
      },
      doProxy: function doProxy(e) {
        var results = [];
        for (var i = 0, j = _init.proxys.length; i < j; i++) {
          var proxyResult = _init.proxys[i].apply(this, arguments);
          results.push(proxyResult);
          if (proxyResult.stop) {
            break;
          }
        }
        return results;
      },
      proxy: function proxy(e) {
        var setting = data.getSetting(e.data.treeId);
        if (!tools.uCanDo(setting, e)) return true;
        var results = event.doProxy(e),
          r = true,
          x = false;
        for (var i = 0, l = results.length; i < l; i++) {
          var proxyResult = results[i];
          if (proxyResult.nodeEventCallback) {
            x = true;
            r = proxyResult.nodeEventCallback.apply(proxyResult, [e, proxyResult.node]) && r;
          }
          if (proxyResult.treeEventCallback) {
            x = true;
            r = proxyResult.treeEventCallback.apply(proxyResult, [e, proxyResult.node]) && r;
          }
        }
        return r;
      }
    },
    //method of event handler
    handler = {
      onSwitchNode: function onSwitchNode(event, node) {
        var setting = data.getSetting(event.data.treeId);
        if (node.open) {
          if (tools.apply(setting.callback.beforeCollapse, [setting.treeId, node], true) == false) return true;
          data.getRoot(setting).expandTriggerFlag = true;
          view.switchNode(setting, node);
        } else {
          if (tools.apply(setting.callback.beforeExpand, [setting.treeId, node], true) == false) return true;
          data.getRoot(setting).expandTriggerFlag = true;
          view.switchNode(setting, node);
        }
        return true;
      },
      onClickNode: function onClickNode(event, node) {
        var setting = data.getSetting(event.data.treeId),
          clickFlag = setting.view.autoCancelSelected && (event.ctrlKey || event.metaKey) && data.isSelectedNode(setting, node) ? 0 : setting.view.autoCancelSelected && (event.ctrlKey || event.metaKey) && setting.view.selectedMulti ? 2 : 1;
        if (tools.apply(setting.callback.beforeClick, [setting.treeId, node, clickFlag], true) == false) return true;
        if (clickFlag === 0) {
          view.cancelPreSelectedNode(setting, node);
        } else {
          view.selectNode(setting, node, clickFlag === 2);
        }
        setting.treeObj.trigger(consts.event.CLICK, [event, setting.treeId, node, clickFlag]);
        return true;
      },
      onZTreeMousedown: function onZTreeMousedown(event, node) {
        var setting = data.getSetting(event.data.treeId);
        if (tools.apply(setting.callback.beforeMouseDown, [setting.treeId, node], true)) {
          tools.apply(setting.callback.onMouseDown, [event, setting.treeId, node]);
        }
        return true;
      },
      onZTreeMouseup: function onZTreeMouseup(event, node) {
        var setting = data.getSetting(event.data.treeId);
        if (tools.apply(setting.callback.beforeMouseUp, [setting.treeId, node], true)) {
          tools.apply(setting.callback.onMouseUp, [event, setting.treeId, node]);
        }
        return true;
      },
      onZTreeDblclick: function onZTreeDblclick(event, node) {
        var setting = data.getSetting(event.data.treeId);
        if (tools.apply(setting.callback.beforeDblClick, [setting.treeId, node], true)) {
          tools.apply(setting.callback.onDblClick, [event, setting.treeId, node]);
        }
        return true;
      },
      onZTreeContextmenu: function onZTreeContextmenu(event, node) {
        var setting = data.getSetting(event.data.treeId);
        if (tools.apply(setting.callback.beforeRightClick, [setting.treeId, node], true)) {
          tools.apply(setting.callback.onRightClick, [event, setting.treeId, node]);
        }
        return typeof setting.callback.onRightClick != "function";
      },
      onSelectStart: function onSelectStart(e) {
        var n = e.originalEvent.srcElement.nodeName.toLowerCase();
        return n === "input" || n === "textarea";
      }
    },
    //method of tools for zTree
    tools = {
      apply: function apply(fun, param, defaultValue) {
        if (typeof fun == "function") {
          return fun.apply(zt, param ? param : []);
        }
        return defaultValue;
      },
      canAsync: function canAsync(setting, node) {
        var childKey = setting.data.key.children;
        return setting.async.enable && node && node.isParent && !(node.zAsync || node[childKey] && node[childKey].length > 0);
      },
      clone: function clone(obj) {
        if (obj === null) return null;
        var o = tools.isArray(obj) ? [] : {};
        for (var i in obj) {
          o[i] = obj[i] instanceof Date ? new Date(obj[i].getTime()) : _typeof(obj[i]) === "object" ? tools.clone(obj[i]) : obj[i];
        }
        return o;
      },
      eqs: function eqs(str1, str2) {
        return str1.toLowerCase() === str2.toLowerCase();
      },
      isArray: function isArray(arr) {
        return Object.prototype.toString.apply(arr) === "[object Array]";
      },
      isElement: function isElement(o) {
        return (typeof HTMLElement === "undefined" ? "undefined" : _typeof(HTMLElement)) === "object" ? o instanceof HTMLElement :
        //DOM2
        o && _typeof(o) === "object" && o !== null && o.nodeType === 1 && typeof o.nodeName === "string";
      },
      $: function $(node, exp, setting) {
        if (!!exp && typeof exp != "string") {
          setting = exp;
          exp = "";
        }
        if (typeof node == "string") {
          return _$(node, setting ? setting.treeObj.get(0).ownerDocument : null);
        } else {
          return _$("#" + node.tId + exp, setting ? setting.treeObj : null);
        }
      },
      getMDom: function getMDom(setting, curDom, targetExpr) {
        if (!curDom) return null;
        while (curDom && curDom.id !== setting.treeId) {
          for (var i = 0, l = targetExpr.length; curDom.tagName && i < l; i++) {
            if (tools.eqs(curDom.tagName, targetExpr[i].tagName) && curDom.getAttribute(targetExpr[i].attrName) !== null) {
              return curDom;
            }
          }
          curDom = curDom.parentNode;
        }
        return null;
      },
      getNodeMainDom: function getNodeMainDom(target) {
        return _$(target).parent("li").get(0) || _$(target).parentsUntil("li").parent().get(0);
      },
      isChildOrSelf: function isChildOrSelf(dom, parentId) {
        return _$(dom).closest("#" + parentId).length > 0;
      },
      uCanDo: function uCanDo(setting, e) {
        return true;
      }
    },
    //method of operate ztree dom
    view = {
      addNodes: function addNodes(setting, parentNode, index, newNodes, isSilent) {
        if (setting.data.keep.leaf && parentNode && !parentNode.isParent) {
          return;
        }
        if (!tools.isArray(newNodes)) {
          newNodes = [newNodes];
        }
        if (setting.data.simpleData.enable) {
          newNodes = data.transformTozTreeFormat(setting, newNodes);
        }
        if (parentNode) {
          var target_switchObj = $$(parentNode, consts.id.SWITCH, setting),
            target_icoObj = $$(parentNode, consts.id.ICON, setting),
            target_ulObj = $$(parentNode, consts.id.UL, setting);
          if (!parentNode.open) {
            view.replaceSwitchClass(parentNode, target_switchObj, consts.folder.CLOSE);
            view.replaceIcoClass(parentNode, target_icoObj, consts.folder.CLOSE);
            parentNode.open = false;
            target_ulObj.css({
              "display": "none"
            });
          }
          data.addNodesData(setting, parentNode, index, newNodes);
          view.createNodes(setting, parentNode.level + 1, newNodes, parentNode, index);
          if (!isSilent) {
            view.expandCollapseParentNode(setting, parentNode, true);
          }
        } else {
          data.addNodesData(setting, data.getRoot(setting), index, newNodes);
          view.createNodes(setting, 0, newNodes, null, index);
        }
      },
      appendNodes: function appendNodes(setting, level, nodes, parentNode, index, initFlag, openFlag) {
        if (!nodes) return [];
        var html = [],
          childKey = setting.data.key.children;
        var tmpPNode = parentNode ? parentNode : data.getRoot(setting),
          tmpPChild = tmpPNode[childKey],
          isFirstNode,
          isLastNode;
        if (!tmpPChild || index >= tmpPChild.length - nodes.length) {
          index = -1;
        }
        for (var i = 0, l = nodes.length; i < l; i++) {
          var node = nodes[i];
          if (initFlag) {
            isFirstNode = (index === 0 || tmpPChild.length == nodes.length) && i == 0;
            isLastNode = index < 0 && i == nodes.length - 1;
            data.initNode(setting, level, node, parentNode, isFirstNode, isLastNode, openFlag);
            data.addNodeCache(setting, node);
          }
          var childHtml = [];
          if (node[childKey] && node[childKey].length > 0) {
            //make child html first, because checkType
            childHtml = view.appendNodes(setting, level + 1, node[childKey], node, -1, initFlag, openFlag && node.open);
          }
          if (openFlag) {
            view.makeDOMNodeMainBefore(html, setting, node);
            view.makeDOMNodeLine(html, setting, node);
            data.getBeforeA(setting, node, html);
            view.makeDOMNodeNameBefore(html, setting, node);
            data.getInnerBeforeA(setting, node, html);
            view.makeDOMNodeIcon(html, setting, node);
            data.getInnerAfterA(setting, node, html);
            view.makeDOMNodeNameAfter(html, setting, node);
            data.getAfterA(setting, node, html);
            if (node.isParent && node.open) {
              view.makeUlHtml(setting, node, html, childHtml.join(''));
            }
            view.makeDOMNodeMainAfter(html, setting, node);
            data.addCreatedNode(setting, node);
          }
        }
        return html;
      },
      appendParentULDom: function appendParentULDom(setting, node) {
        var html = [],
          nObj = $$(node, setting);
        if (!nObj.get(0) && !!node.parentTId) {
          view.appendParentULDom(setting, node.getParentNode());
          nObj = $$(node, setting);
        }
        var ulObj = $$(node, consts.id.UL, setting);
        if (ulObj.get(0)) {
          ulObj.remove();
        }
        var childKey = setting.data.key.children,
          childHtml = view.appendNodes(setting, node.level + 1, node[childKey], node, -1, false, true);
        view.makeUlHtml(setting, node, html, childHtml.join(''));
        nObj.append(html.join(''));
      },
      asyncNode: function asyncNode(setting, node, isSilent, callback) {
        var i, l;
        if (node && !node.isParent) {
          tools.apply(callback);
          return false;
        } else if (node && node.isAjaxing) {
          return false;
        } else if (tools.apply(setting.callback.beforeAsync, [setting.treeId, node], true) == false) {
          tools.apply(callback);
          return false;
        }
        if (node) {
          node.isAjaxing = true;
          var icoObj = $$(node, consts.id.ICON, setting);
          icoObj.attr({
            "style": "",
            "class": consts.className.BUTTON + " " + consts.className.ICO_LOADING
          });
        }
        var tmpParam = {};
        for (i = 0, l = setting.async.autoParam.length; node && i < l; i++) {
          var pKey = setting.async.autoParam[i].split("="),
            spKey = pKey;
          if (pKey.length > 1) {
            spKey = pKey[1];
            pKey = pKey[0];
          }
          tmpParam[spKey] = node[pKey];
        }
        if (tools.isArray(setting.async.otherParam)) {
          for (i = 0, l = setting.async.otherParam.length; i < l; i += 2) {
            tmpParam[setting.async.otherParam[i]] = setting.async.otherParam[i + 1];
          }
        } else {
          for (var p in setting.async.otherParam) {
            tmpParam[p] = setting.async.otherParam[p];
          }
        }
        var _tmpV = data.getRoot(setting)._ver;
        _$.ajax({
          contentType: setting.async.contentType,
          cache: false,
          type: setting.async.type,
          url: tools.apply(setting.async.url, [setting.treeId, node], setting.async.url),
          data: setting.async.contentType.indexOf('application/json') > -1 ? JSON.stringify(tmpParam) : tmpParam,
          dataType: setting.async.dataType,
          success: function success(msg) {
            if (_tmpV != data.getRoot(setting)._ver) {
              return;
            }
            var newNodes = [];
            try {
              if (!msg || msg.length == 0) {
                newNodes = [];
              } else if (typeof msg == "string") {
                newNodes = eval("(" + msg + ")");
              } else {
                newNodes = msg;
              }
            } catch (err) {
              newNodes = msg;
            }
            if (node) {
              node.isAjaxing = null;
              node.zAsync = true;
            }
            view.setNodeLineIcos(setting, node);
            if (newNodes && newNodes !== "") {
              newNodes = tools.apply(setting.async.dataFilter, [setting.treeId, node, newNodes], newNodes);
              view.addNodes(setting, node, -1, !!newNodes ? tools.clone(newNodes) : [], !!isSilent);
            } else {
              view.addNodes(setting, node, -1, [], !!isSilent);
            }
            setting.treeObj.trigger(consts.event.ASYNC_SUCCESS, [setting.treeId, node, msg]);
            tools.apply(callback);
          },
          error: function error(XMLHttpRequest, textStatus, errorThrown) {
            if (_tmpV != data.getRoot(setting)._ver) {
              return;
            }
            if (node) node.isAjaxing = null;
            view.setNodeLineIcos(setting, node);
            setting.treeObj.trigger(consts.event.ASYNC_ERROR, [setting.treeId, node, XMLHttpRequest, textStatus, errorThrown]);
          }
        });
        return true;
      },
      cancelPreSelectedNode: function cancelPreSelectedNode(setting, node, excludeNode) {
        var list = data.getRoot(setting).curSelectedList,
          i,
          n;
        for (i = list.length - 1; i >= 0; i--) {
          n = list[i];
          if (node === n || !node && (!excludeNode || excludeNode !== n)) {
            $$(n, consts.id.A, setting).removeClass(consts.node.CURSELECTED);
            if (node) {
              data.removeSelectedNode(setting, node);
              break;
            } else {
              list.splice(i, 1);
              setting.treeObj.trigger(consts.event.UNSELECTED, [setting.treeId, n]);
            }
          }
        }
      },
      createNodeCallback: function createNodeCallback(setting) {
        if (!!setting.callback.onNodeCreated || !!setting.view.addDiyDom) {
          var root = data.getRoot(setting);
          while (root.createdNodes.length > 0) {
            var node = root.createdNodes.shift();
            tools.apply(setting.view.addDiyDom, [setting.treeId, node]);
            if (!!setting.callback.onNodeCreated) {
              setting.treeObj.trigger(consts.event.NODECREATED, [setting.treeId, node]);
            }
          }
        }
      },
      createNodes: function createNodes(setting, level, nodes, parentNode, index) {
        if (!nodes || nodes.length == 0) return;
        var root = data.getRoot(setting),
          childKey = setting.data.key.children,
          openFlag = !parentNode || parentNode.open || !!$$(parentNode[childKey][0], setting).get(0);
        root.createdNodes = [];
        var zTreeHtml = view.appendNodes(setting, level, nodes, parentNode, index, true, openFlag),
          parentObj,
          nextObj;
        if (!parentNode) {
          parentObj = setting.treeObj;
          //setting.treeObj.append(zTreeHtml.join(''));
        } else {
          var ulObj = $$(parentNode, consts.id.UL, setting);
          if (ulObj.get(0)) {
            parentObj = ulObj;
            //ulObj.append(zTreeHtml.join(''));
          }
        }
        if (parentObj) {
          if (index >= 0) {
            nextObj = parentObj.children()[index];
          }
          if (index >= 0 && nextObj) {
            _$(nextObj).before(zTreeHtml.join(''));
          } else {
            parentObj.append(zTreeHtml.join(''));
          }
        }
        view.createNodeCallback(setting);
      },
      destroy: function destroy(setting) {
        if (!setting) return;
        data.initCache(setting);
        data.initRoot(setting);
        event.unbindTree(setting);
        event.unbindEvent(setting);
        setting.treeObj.empty();
        delete settings[setting.treeId];
      },
      expandCollapseNode: function expandCollapseNode(setting, node, expandFlag, animateFlag, callback) {
        var root = data.getRoot(setting),
          childKey = setting.data.key.children;
        var tmpCb, _callback;
        if (!node) {
          tools.apply(callback, []);
          return;
        }
        if (root.expandTriggerFlag) {
          _callback = callback;
          tmpCb = function tmpCb() {
            if (_callback) _callback();
            if (node.open) {
              setting.treeObj.trigger(consts.event.EXPAND, [setting.treeId, node]);
            } else {
              setting.treeObj.trigger(consts.event.COLLAPSE, [setting.treeId, node]);
            }
          };
          callback = tmpCb;
          root.expandTriggerFlag = false;
        }
        if (!node.open && node.isParent && (!$$(node, consts.id.UL, setting).get(0) || node[childKey] && node[childKey].length > 0 && !$$(node[childKey][0], setting).get(0))) {
          view.appendParentULDom(setting, node);
          view.createNodeCallback(setting);
        }
        if (node.open == expandFlag) {
          tools.apply(callback, []);
          return;
        }
        var ulObj = $$(node, consts.id.UL, setting),
          switchObj = $$(node, consts.id.SWITCH, setting),
          icoObj = $$(node, consts.id.ICON, setting);
        if (node.isParent) {
          node.open = !node.open;
          if (node.iconOpen && node.iconClose) {
            icoObj.attr("style", view.makeNodeIcoStyle(setting, node));
          }
          if (node.open) {
            view.replaceSwitchClass(node, switchObj, consts.folder.OPEN);
            view.replaceIcoClass(node, icoObj, consts.folder.OPEN);
            if (animateFlag == false || setting.view.expandSpeed == "") {
              ulObj.show();
              tools.apply(callback, []);
            } else {
              if (node[childKey] && node[childKey].length > 0) {
                ulObj.slideDown(setting.view.expandSpeed, callback);
              } else {
                ulObj.show();
                tools.apply(callback, []);
              }
            }
          } else {
            view.replaceSwitchClass(node, switchObj, consts.folder.CLOSE);
            view.replaceIcoClass(node, icoObj, consts.folder.CLOSE);
            if (animateFlag == false || setting.view.expandSpeed == "" || !(node[childKey] && node[childKey].length > 0)) {
              ulObj.hide();
              tools.apply(callback, []);
            } else {
              ulObj.slideUp(setting.view.expandSpeed, callback);
            }
          }
        } else {
          tools.apply(callback, []);
        }
      },
      expandCollapseParentNode: function expandCollapseParentNode(setting, node, expandFlag, animateFlag, callback) {
        if (!node) return;
        if (!node.parentTId) {
          view.expandCollapseNode(setting, node, expandFlag, animateFlag, callback);
          return;
        } else {
          view.expandCollapseNode(setting, node, expandFlag, animateFlag);
        }
        if (node.parentTId) {
          view.expandCollapseParentNode(setting, node.getParentNode(), expandFlag, animateFlag, callback);
        }
      },
      expandCollapseSonNode: function expandCollapseSonNode(setting, node, expandFlag, animateFlag, callback) {
        var root = data.getRoot(setting),
          childKey = setting.data.key.children,
          treeNodes = node ? node[childKey] : root[childKey],
          selfAnimateSign = node ? false : animateFlag,
          expandTriggerFlag = data.getRoot(setting).expandTriggerFlag;
        data.getRoot(setting).expandTriggerFlag = false;
        if (treeNodes) {
          for (var i = 0, l = treeNodes.length; i < l; i++) {
            if (treeNodes[i]) view.expandCollapseSonNode(setting, treeNodes[i], expandFlag, selfAnimateSign);
          }
        }
        data.getRoot(setting).expandTriggerFlag = expandTriggerFlag;
        view.expandCollapseNode(setting, node, expandFlag, animateFlag, callback);
      },
      isSelectedNode: function isSelectedNode(setting, node) {
        if (!node) {
          return false;
        }
        var list = data.getRoot(setting).curSelectedList,
          i;
        for (i = list.length - 1; i >= 0; i--) {
          if (node === list[i]) {
            return true;
          }
        }
        return false;
      },
      makeDOMNodeIcon: function makeDOMNodeIcon(html, setting, node) {
        var nameStr = data.getNodeName(setting, node),
          name = setting.view.nameIsHTML ? nameStr : nameStr.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        html.push("<span id='", node.tId, consts.id.ICON, "' title='' treeNode", consts.id.ICON, " class='", view.makeNodeIcoClass(setting, node), "' style='", view.makeNodeIcoStyle(setting, node), "'></span><span id='", node.tId, consts.id.SPAN, "' class='", consts.className.NAME, "'>", name, "</span>");
      },
      makeDOMNodeLine: function makeDOMNodeLine(html, setting, node) {
        html.push("<span id='", node.tId, consts.id.SWITCH, "' title='' class='", view.makeNodeLineClass(setting, node), "' treeNode", consts.id.SWITCH, "></span>");
      },
      makeDOMNodeMainAfter: function makeDOMNodeMainAfter(html, setting, node) {
        html.push("</li>");
      },
      makeDOMNodeMainBefore: function makeDOMNodeMainBefore(html, setting, node) {
        html.push("<li id='", node.tId, "' class='", consts.className.LEVEL, node.level, "' tabindex='0' hidefocus='true' treenode>");
      },
      makeDOMNodeNameAfter: function makeDOMNodeNameAfter(html, setting, node) {
        html.push("</a>");
      },
      makeDOMNodeNameBefore: function makeDOMNodeNameBefore(html, setting, node) {
        var title = data.getNodeTitle(setting, node),
          url = view.makeNodeUrl(setting, node),
          fontcss = view.makeNodeFontCss(setting, node),
          fontStyle = [];
        for (var f in fontcss) {
          fontStyle.push(f, ":", fontcss[f], ";");
        }
        html.push("<a id='", node.tId, consts.id.A, "' class='", consts.className.LEVEL, node.level, "' treeNode", consts.id.A, " onclick=\"", node.click || '', "\" ", url != null && url.length > 0 ? "href='" + url + "'" : "", " target='", view.makeNodeTarget(node), "' style='", fontStyle.join(''), "'");
        if (tools.apply(setting.view.showTitle, [setting.treeId, node], setting.view.showTitle) && title) {
          html.push("title='", title.replace(/'/g, "&#39;").replace(/</g, '&lt;').replace(/>/g, '&gt;'), "'");
        }
        html.push(">");
      },
      makeNodeFontCss: function makeNodeFontCss(setting, node) {
        var fontCss = tools.apply(setting.view.fontCss, [setting.treeId, node], setting.view.fontCss);
        return fontCss && typeof fontCss != "function" ? fontCss : {};
      },
      makeNodeIcoClass: function makeNodeIcoClass(setting, node) {
        var icoCss = ["ico"];
        if (!node.isAjaxing) {
          icoCss[0] = (node.iconSkin ? node.iconSkin + "_" : "") + icoCss[0];
          if (node.isParent) {
            icoCss.push(node.open ? consts.folder.OPEN : consts.folder.CLOSE);
          } else {
            icoCss.push(consts.folder.DOCU);
          }
        }
        return consts.className.BUTTON + " " + icoCss.join('_');
      },
      makeNodeIcoStyle: function makeNodeIcoStyle(setting, node) {
        var icoStyle = [];
        if (!node.isAjaxing) {
          var icon = node.isParent && node.iconOpen && node.iconClose ? node.open ? node.iconOpen : node.iconClose : node[setting.data.key.icon];
          if (icon) icoStyle.push("background:url(", icon, ") 0 0 no-repeat;");
          if (setting.view.showIcon == false || !tools.apply(setting.view.showIcon, [setting.treeId, node], true)) {
            icoStyle.push("width:0px;height:0px;");
          }
        }
        return icoStyle.join('');
      },
      makeNodeLineClass: function makeNodeLineClass(setting, node) {
        var lineClass = [];
        if (setting.view.showLine) {
          if (node.level == 0 && node.isFirstNode && node.isLastNode) {
            lineClass.push(consts.line.ROOT);
          } else if (node.level == 0 && node.isFirstNode) {
            lineClass.push(consts.line.ROOTS);
          } else if (node.isLastNode) {
            lineClass.push(consts.line.BOTTOM);
          } else {
            lineClass.push(consts.line.CENTER);
          }
        } else {
          lineClass.push(consts.line.NOLINE);
        }
        if (node.isParent) {
          lineClass.push(node.open ? consts.folder.OPEN : consts.folder.CLOSE);
        } else {
          lineClass.push(consts.folder.DOCU);
        }
        return view.makeNodeLineClassEx(node) + lineClass.join('_');
      },
      makeNodeLineClassEx: function makeNodeLineClassEx(node) {
        return consts.className.BUTTON + " " + consts.className.LEVEL + node.level + " " + consts.className.SWITCH + " ";
      },
      makeNodeTarget: function makeNodeTarget(node) {
        return node.target || "_blank";
      },
      makeNodeUrl: function makeNodeUrl(setting, node) {
        var urlKey = setting.data.key.url;
        return node[urlKey] ? node[urlKey] : null;
      },
      makeUlHtml: function makeUlHtml(setting, node, html, content) {
        html.push("<ul id='", node.tId, consts.id.UL, "' class='", consts.className.LEVEL, node.level, " ", view.makeUlLineClass(setting, node), "' style='display:", node.open ? "block" : "none", "'>");
        html.push(content);
        html.push("</ul>");
      },
      makeUlLineClass: function makeUlLineClass(setting, node) {
        return setting.view.showLine && !node.isLastNode ? consts.line.LINE : "";
      },
      removeChildNodes: function removeChildNodes(setting, node) {
        if (!node) return;
        var childKey = setting.data.key.children,
          nodes = node[childKey];
        if (!nodes) return;
        for (var i = 0, l = nodes.length; i < l; i++) {
          data.removeNodeCache(setting, nodes[i]);
        }
        data.removeSelectedNode(setting);
        delete node[childKey];
        if (!setting.data.keep.parent) {
          node.isParent = false;
          node.open = false;
          var tmp_switchObj = $$(node, consts.id.SWITCH, setting),
            tmp_icoObj = $$(node, consts.id.ICON, setting);
          view.replaceSwitchClass(node, tmp_switchObj, consts.folder.DOCU);
          view.replaceIcoClass(node, tmp_icoObj, consts.folder.DOCU);
          $$(node, consts.id.UL, setting).remove();
        } else {
          $$(node, consts.id.UL, setting).empty();
        }
      },
      scrollIntoView: function scrollIntoView(dom) {
        if (!dom) {
          return;
        }
        // code src: http://jsfiddle.net/08u6cxwj/
        if (!Element.prototype.scrollIntoViewIfNeeded) {
          Element.prototype.scrollIntoViewIfNeeded = function (centerIfNeeded) {
            function withinBounds(value, min, max, extent) {
              if (false === centerIfNeeded || max <= value + extent && value <= min + extent) {
                return Math.min(max, Math.max(min, value));
              } else {
                return (min + max) / 2;
              }
            }
            function makeArea(left, top, width, height) {
              return {
                "left": left,
                "top": top,
                "width": width,
                "height": height,
                "right": left + width,
                "bottom": top + height,
                "translate": function translate(x, y) {
                  return makeArea(x + left, y + top, width, height);
                },
                "relativeFromTo": function relativeFromTo(lhs, rhs) {
                  var newLeft = left,
                    newTop = top;
                  lhs = lhs.offsetParent;
                  rhs = rhs.offsetParent;
                  if (lhs === rhs) {
                    return area;
                  }
                  for (; lhs; lhs = lhs.offsetParent) {
                    newLeft += lhs.offsetLeft + lhs.clientLeft;
                    newTop += lhs.offsetTop + lhs.clientTop;
                  }
                  for (; rhs; rhs = rhs.offsetParent) {
                    newLeft -= rhs.offsetLeft + rhs.clientLeft;
                    newTop -= rhs.offsetTop + rhs.clientTop;
                  }
                  return makeArea(newLeft, newTop, width, height);
                }
              };
            }
            var parent,
              elem = this,
              area = makeArea(this.offsetLeft, this.offsetTop, this.offsetWidth, this.offsetHeight);
            while (tools.isElement(parent = elem.parentNode)) {
              var clientLeft = parent.offsetLeft + parent.clientLeft;
              var clientTop = parent.offsetTop + parent.clientTop;

              // Make area relative to parent's client area.
              area = area.relativeFromTo(elem, parent).translate(-clientLeft, -clientTop);
              parent.scrollLeft = withinBounds(parent.scrollLeft, area.right - parent.clientWidth, area.left, parent.clientWidth);
              parent.scrollTop = withinBounds(parent.scrollTop, area.bottom - parent.clientHeight, area.top, parent.clientHeight);

              // Determine actual scroll amount by reading back scroll properties.
              area = area.translate(clientLeft - parent.scrollLeft, clientTop - parent.scrollTop);
              elem = parent;
            }
          };
        }
        dom.scrollIntoViewIfNeeded();
      },
      setFirstNode: function setFirstNode(setting, parentNode) {
        var childKey = setting.data.key.children,
          childLength = parentNode[childKey].length;
        if (childLength > 0) {
          parentNode[childKey][0].isFirstNode = true;
        }
      },
      setLastNode: function setLastNode(setting, parentNode) {
        var childKey = setting.data.key.children,
          childLength = parentNode[childKey].length;
        if (childLength > 0) {
          parentNode[childKey][childLength - 1].isLastNode = true;
        }
      },
      removeNode: function removeNode(setting, node) {
        var root = data.getRoot(setting),
          childKey = setting.data.key.children,
          parentNode = node.parentTId ? node.getParentNode() : root;
        node.isFirstNode = false;
        node.isLastNode = false;
        node.getPreNode = function () {
          return null;
        };
        node.getNextNode = function () {
          return null;
        };
        if (!data.getNodeCache(setting, node.tId)) {
          return;
        }
        $$(node, setting).remove();
        data.removeNodeCache(setting, node);
        data.removeSelectedNode(setting, node);
        for (var i = 0, l = parentNode[childKey].length; i < l; i++) {
          if (parentNode[childKey][i].tId == node.tId) {
            parentNode[childKey].splice(i, 1);
            break;
          }
        }
        view.setFirstNode(setting, parentNode);
        view.setLastNode(setting, parentNode);
        var tmp_ulObj,
          tmp_switchObj,
          tmp_icoObj,
          childLength = parentNode[childKey].length;

        //repair nodes old parent
        if (!setting.data.keep.parent && childLength == 0) {
          //old parentNode has no child nodes
          parentNode.isParent = false;
          parentNode.open = false;
          tmp_ulObj = $$(parentNode, consts.id.UL, setting);
          tmp_switchObj = $$(parentNode, consts.id.SWITCH, setting);
          tmp_icoObj = $$(parentNode, consts.id.ICON, setting);
          view.replaceSwitchClass(parentNode, tmp_switchObj, consts.folder.DOCU);
          view.replaceIcoClass(parentNode, tmp_icoObj, consts.folder.DOCU);
          tmp_ulObj.css("display", "none");
        } else if (setting.view.showLine && childLength > 0) {
          //old parentNode has child nodes
          var newLast = parentNode[childKey][childLength - 1];
          tmp_ulObj = $$(newLast, consts.id.UL, setting);
          tmp_switchObj = $$(newLast, consts.id.SWITCH, setting);
          tmp_icoObj = $$(newLast, consts.id.ICON, setting);
          if (parentNode == root) {
            if (parentNode[childKey].length == 1) {
              //node was root, and ztree has only one root after move node
              view.replaceSwitchClass(newLast, tmp_switchObj, consts.line.ROOT);
            } else {
              var tmp_first_switchObj = $$(parentNode[childKey][0], consts.id.SWITCH, setting);
              view.replaceSwitchClass(parentNode[childKey][0], tmp_first_switchObj, consts.line.ROOTS);
              view.replaceSwitchClass(newLast, tmp_switchObj, consts.line.BOTTOM);
            }
          } else {
            view.replaceSwitchClass(newLast, tmp_switchObj, consts.line.BOTTOM);
          }
          tmp_ulObj.removeClass(consts.line.LINE);
        }
      },
      replaceIcoClass: function replaceIcoClass(node, obj, newName) {
        if (!obj || node.isAjaxing) return;
        var tmpName = obj.attr("class");
        if (tmpName == undefined) return;
        var tmpList = tmpName.split("_");
        switch (newName) {
          case consts.folder.OPEN:
          case consts.folder.CLOSE:
          case consts.folder.DOCU:
            tmpList[tmpList.length - 1] = newName;
            break;
        }
        obj.attr("class", tmpList.join("_"));
      },
      replaceSwitchClass: function replaceSwitchClass(node, obj, newName) {
        if (!obj) return;
        var tmpName = obj.attr("class");
        if (tmpName == undefined) return;
        var tmpList = tmpName.split("_");
        switch (newName) {
          case consts.line.ROOT:
          case consts.line.ROOTS:
          case consts.line.CENTER:
          case consts.line.BOTTOM:
          case consts.line.NOLINE:
            tmpList[0] = view.makeNodeLineClassEx(node) + newName;
            break;
          case consts.folder.OPEN:
          case consts.folder.CLOSE:
          case consts.folder.DOCU:
            tmpList[1] = newName;
            break;
        }
        obj.attr("class", tmpList.join("_"));
        if (newName !== consts.folder.DOCU) {
          obj.removeAttr("disabled");
        } else {
          obj.attr("disabled", "disabled");
        }
      },
      selectNode: function selectNode(setting, node, addFlag) {
        if (!addFlag) {
          view.cancelPreSelectedNode(setting, null, node);
        }
        $$(node, consts.id.A, setting).addClass(consts.node.CURSELECTED);
        data.addSelectedNode(setting, node);
        setting.treeObj.trigger(consts.event.SELECTED, [setting.treeId, node]);
      },
      setNodeFontCss: function setNodeFontCss(setting, treeNode) {
        var aObj = $$(treeNode, consts.id.A, setting),
          fontCss = view.makeNodeFontCss(setting, treeNode);
        if (fontCss) {
          aObj.css(fontCss);
        }
      },
      setNodeLineIcos: function setNodeLineIcos(setting, node) {
        if (!node) return;
        var switchObj = $$(node, consts.id.SWITCH, setting),
          ulObj = $$(node, consts.id.UL, setting),
          icoObj = $$(node, consts.id.ICON, setting),
          ulLine = view.makeUlLineClass(setting, node);
        if (ulLine.length == 0) {
          ulObj.removeClass(consts.line.LINE);
        } else {
          ulObj.addClass(ulLine);
        }
        switchObj.attr("class", view.makeNodeLineClass(setting, node));
        if (node.isParent) {
          switchObj.removeAttr("disabled");
        } else {
          switchObj.attr("disabled", "disabled");
        }
        icoObj.removeAttr("style");
        icoObj.attr("style", view.makeNodeIcoStyle(setting, node));
        icoObj.attr("class", view.makeNodeIcoClass(setting, node));
      },
      setNodeName: function setNodeName(setting, node) {
        var title = data.getNodeTitle(setting, node),
          nObj = $$(node, consts.id.SPAN, setting);
        nObj.empty();
        if (setting.view.nameIsHTML) {
          nObj.html(data.getNodeName(setting, node));
        } else {
          nObj.text(data.getNodeName(setting, node));
        }
        if (tools.apply(setting.view.showTitle, [setting.treeId, node], setting.view.showTitle)) {
          var aObj = $$(node, consts.id.A, setting);
          aObj.attr("title", !title ? "" : title);
        }
      },
      setNodeTarget: function setNodeTarget(setting, node) {
        var aObj = $$(node, consts.id.A, setting);
        aObj.attr("target", view.makeNodeTarget(node));
      },
      setNodeUrl: function setNodeUrl(setting, node) {
        var aObj = $$(node, consts.id.A, setting),
          url = view.makeNodeUrl(setting, node);
        if (url == null || url.length == 0) {
          aObj.removeAttr("href");
        } else {
          aObj.attr("href", url);
        }
      },
      switchNode: function switchNode(setting, node) {
        if (node.open || !tools.canAsync(setting, node)) {
          view.expandCollapseNode(setting, node, !node.open);
        } else if (setting.async.enable) {
          if (!view.asyncNode(setting, node)) {
            view.expandCollapseNode(setting, node, !node.open);
            return;
          }
        } else if (node) {
          view.expandCollapseNode(setting, node, !node.open);
        }
      }
    };
  // zTree defind
  _$.fn.zTree = {
    consts: _consts,
    _z: {
      tools: tools,
      view: view,
      event: event,
      data: data
    },
    getZTreeObj: function getZTreeObj(treeId) {
      var o = data.getZTreeTools(treeId);
      return o ? o : null;
    },
    destroy: function destroy(treeId) {
      if (!!treeId && treeId.length > 0) {
        view.destroy(data.getSetting(treeId));
      } else {
        for (var s in settings) {
          view.destroy(settings[s]);
        }
      }
    },
    init: function init(obj, zSetting, zNodes) {
      var setting = tools.clone(_setting);
      _$.extend(true, setting, zSetting);
      setting.treeId = obj.attr("id");
      setting.treeObj = obj;
      setting.treeObj.empty();
      settings[setting.treeId] = setting;
      //For some older browser,(e.g., ie6)
      if (typeof document.body.style.maxHeight === "undefined") {
        setting.view.expandSpeed = "";
      }
      data.initRoot(setting);
      var root = data.getRoot(setting),
        childKey = setting.data.key.children;
      zNodes = zNodes ? tools.clone(tools.isArray(zNodes) ? zNodes : [zNodes]) : [];
      if (setting.data.simpleData.enable) {
        root[childKey] = data.transformTozTreeFormat(setting, zNodes);
      } else {
        root[childKey] = zNodes;
      }
      data.initCache(setting);
      event.unbindTree(setting);
      event.bindTree(setting);
      event.unbindEvent(setting);
      event.bindEvent(setting);
      var zTreeTools = {
        setting: setting,
        addNodes: function addNodes(parentNode, index, newNodes, isSilent) {
          if (!parentNode) parentNode = null;
          if (parentNode && !parentNode.isParent && setting.data.keep.leaf) return null;
          var i = parseInt(index, 10);
          if (isNaN(i)) {
            isSilent = !!newNodes;
            newNodes = index;
            index = -1;
          } else {
            index = i;
          }
          if (!newNodes) return null;
          var xNewNodes = tools.clone(tools.isArray(newNodes) ? newNodes : [newNodes]);
          function addCallback() {
            view.addNodes(setting, parentNode, index, xNewNodes, isSilent == true);
          }
          if (tools.canAsync(setting, parentNode)) {
            view.asyncNode(setting, parentNode, isSilent, addCallback);
          } else {
            addCallback();
          }
          return xNewNodes;
        },
        cancelSelectedNode: function cancelSelectedNode(node) {
          view.cancelPreSelectedNode(setting, node);
        },
        destroy: function destroy() {
          view.destroy(setting);
        },
        expandAll: function expandAll(expandFlag) {
          expandFlag = !!expandFlag;
          view.expandCollapseSonNode(setting, null, expandFlag, true);
          return expandFlag;
        },
        expandNode: function expandNode(node, expandFlag, sonSign, focus, callbackFlag) {
          if (!node || !node.isParent) return null;
          if (expandFlag !== true && expandFlag !== false) {
            expandFlag = !node.open;
          }
          callbackFlag = !!callbackFlag;
          if (callbackFlag && expandFlag && tools.apply(setting.callback.beforeExpand, [setting.treeId, node], true) == false) {
            return null;
          } else if (callbackFlag && !expandFlag && tools.apply(setting.callback.beforeCollapse, [setting.treeId, node], true) == false) {
            return null;
          }
          if (expandFlag && node.parentTId) {
            view.expandCollapseParentNode(setting, node.getParentNode(), expandFlag, false);
          }
          if (expandFlag === node.open && !sonSign) {
            return null;
          }
          data.getRoot(setting).expandTriggerFlag = callbackFlag;
          if (!tools.canAsync(setting, node) && sonSign) {
            view.expandCollapseSonNode(setting, node, expandFlag, true, showNodeFocus);
          } else {
            node.open = !expandFlag;
            view.switchNode(this.setting, node);
            showNodeFocus();
          }
          return expandFlag;
          function showNodeFocus() {
            var a = $$(node, setting).get(0);
            if (a && focus !== false) {
              view.scrollIntoView(a);
            }
          }
        },
        getNodes: function getNodes() {
          return data.getNodes(setting);
        },
        getNodeByParam: function getNodeByParam(key, value, parentNode) {
          if (!key) return null;
          return data.getNodeByParam(setting, parentNode ? parentNode[setting.data.key.children] : data.getNodes(setting), key, value);
        },
        getNodeByTId: function getNodeByTId(tId) {
          return data.getNodeCache(setting, tId);
        },
        getNodesByParam: function getNodesByParam(key, value, parentNode) {
          if (!key) return null;
          return data.getNodesByParam(setting, parentNode ? parentNode[setting.data.key.children] : data.getNodes(setting), key, value);
        },
        getNodesByParamFuzzy: function getNodesByParamFuzzy(key, value, parentNode) {
          if (!key) return null;
          return data.getNodesByParamFuzzy(setting, parentNode ? parentNode[setting.data.key.children] : data.getNodes(setting), key, value);
        },
        getNodesByFilter: function getNodesByFilter(filter, isSingle, parentNode, invokeParam) {
          isSingle = !!isSingle;
          if (!filter || typeof filter != "function") return isSingle ? null : [];
          return data.getNodesByFilter(setting, parentNode ? parentNode[setting.data.key.children] : data.getNodes(setting), filter, isSingle, invokeParam);
        },
        getNodeIndex: function getNodeIndex(node) {
          if (!node) return null;
          var childKey = setting.data.key.children,
            parentNode = node.parentTId ? node.getParentNode() : data.getRoot(setting);
          for (var i = 0, l = parentNode[childKey].length; i < l; i++) {
            if (parentNode[childKey][i] == node) return i;
          }
          return -1;
        },
        getSelectedNodes: function getSelectedNodes() {
          var r = [],
            list = data.getRoot(setting).curSelectedList;
          for (var i = 0, l = list.length; i < l; i++) {
            r.push(list[i]);
          }
          return r;
        },
        isSelectedNode: function isSelectedNode(node) {
          return data.isSelectedNode(setting, node);
        },
        reAsyncChildNodes: function reAsyncChildNodes(parentNode, reloadType, isSilent) {
          if (!this.setting.async.enable) return;
          var isRoot = !parentNode;
          if (isRoot) {
            parentNode = data.getRoot(setting);
          }
          if (reloadType == "refresh") {
            var childKey = this.setting.data.key.children;
            for (var i = 0, l = parentNode[childKey] ? parentNode[childKey].length : 0; i < l; i++) {
              data.removeNodeCache(setting, parentNode[childKey][i]);
            }
            data.removeSelectedNode(setting);
            parentNode[childKey] = [];
            if (isRoot) {
              this.setting.treeObj.empty();
            } else {
              var ulObj = $$(parentNode, consts.id.UL, setting);
              ulObj.empty();
            }
          }
          view.asyncNode(this.setting, isRoot ? null : parentNode, !!isSilent);
        },
        refresh: function refresh() {
          this.setting.treeObj.empty();
          var root = data.getRoot(setting),
            nodes = root[setting.data.key.children];
          data.initRoot(setting);
          root[setting.data.key.children] = nodes;
          data.initCache(setting);
          view.createNodes(setting, 0, root[setting.data.key.children], null, -1);
        },
        removeChildNodes: function removeChildNodes(node) {
          if (!node) return null;
          var childKey = setting.data.key.children,
            nodes = node[childKey];
          view.removeChildNodes(setting, node);
          return nodes ? nodes : null;
        },
        removeNode: function removeNode(node, callbackFlag) {
          if (!node) return;
          callbackFlag = !!callbackFlag;
          if (callbackFlag && tools.apply(setting.callback.beforeRemove, [setting.treeId, node], true) == false) return;
          view.removeNode(setting, node);
          if (callbackFlag) {
            this.setting.treeObj.trigger(consts.event.REMOVE, [setting.treeId, node]);
          }
        },
        selectNode: function selectNode(node, addFlag, isSilent) {
          if (!node) return;
          if (tools.uCanDo(setting)) {
            addFlag = setting.view.selectedMulti && addFlag;
            if (node.parentTId) {
              view.expandCollapseParentNode(setting, node.getParentNode(), true, false, showNodeFocus);
            } else if (!isSilent) {
              try {
                $$(node, setting).focus().blur();
              } catch (e) {}
            }
            view.selectNode(setting, node, addFlag);
          }
          function showNodeFocus() {
            if (isSilent) {
              return;
            }
            var a = $$(node, setting).get(0);
            view.scrollIntoView(a);
          }
        },
        transformTozTreeNodes: function transformTozTreeNodes(simpleNodes) {
          return data.transformTozTreeFormat(setting, simpleNodes);
        },
        transformToArray: function transformToArray(nodes) {
          return data.transformToArrayFormat(setting, nodes);
        },
        updateNode: function updateNode(node, checkTypeFlag) {
          if (!node) return;
          var nObj = $$(node, setting);
          if (nObj.get(0) && tools.uCanDo(setting)) {
            view.setNodeName(setting, node);
            view.setNodeTarget(setting, node);
            view.setNodeUrl(setting, node);
            view.setNodeLineIcos(setting, node);
            view.setNodeFontCss(setting, node);
          }
        }
      };
      root.treeTools = zTreeTools;
      data.setZTreeTools(setting, zTreeTools);
      if (root[childKey] && root[childKey].length > 0) {
        view.createNodes(setting, 0, root[childKey], null, -1);
      } else if (setting.async.enable && setting.async.url && setting.async.url !== '') {
        view.asyncNode(setting);
      }
      return zTreeTools;
    }
  };
  var zt = _$.fn.zTree,
    $$ = tools.$,
    consts = zt.consts;
})(jQuery);
