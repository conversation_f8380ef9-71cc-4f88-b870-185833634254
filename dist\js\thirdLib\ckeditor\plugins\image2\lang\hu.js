"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang('image2', 'hu', {
  alt: 'Alternatív szöveg',
  btnUpload: '<PERSON>üldés a szerverre',
  captioned: '<PERSON><PERSON><PERSON><PERSON><PERSON> kép',
  captionPlaceholder: 'K<PERSON>pfelirat',
  infoTab: 'Alaptulajdonságok',
  lockRatio: '<PERSON>r<PERSON><PERSON> megtartása',
  menu: 'Kép tulajdon<PERSON>gai',
  pathName: 'kép',
  pathNameCaption: 'felirat',
  resetSize: '<PERSON><PERSON><PERSON> méret',
  resizer: '<PERSON><PERSON>tson és húzza az átméretezéshez',
  title: 'Kép tulajdons<PERSON>gai',
  uploadTab: 'Feltöltés',
  urlMissing: 'Hi<PERSON>yzik a kép URL-je',
  altMissing: 'Az alternatív szöveg hiányzik.'
});
