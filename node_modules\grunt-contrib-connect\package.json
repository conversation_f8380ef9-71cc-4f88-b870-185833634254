{"_from": "grunt-contrib-connect@^4.0.0", "_id": "grunt-contrib-connect@4.0.0", "_inBundle": false, "_integrity": "sha512-VR2/+ailwTClAXrvI7bK78roCZzfY1C48vmpdRldohx8P1VXcb51NmBNhukBvG2RKFChNheEcKEcM+wSb/5nYA==", "_location": "/grunt-contrib-connect", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "grunt-contrib-connect@^4.0.0", "name": "grunt-contrib-connect", "escapedName": "grunt-contrib-connect", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/grunt-contrib-connect/-/grunt-contrib-connect-4.0.0.tgz", "_shasum": "b0ccc748ef3c1f51dd2ae21aceea0666456211a5", "_spec": "grunt-contrib-connect@^4.0.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper", "author": {"name": "Grunt Team", "url": "https://gruntjs.com/"}, "bugs": {"url": "https://github.com/gruntjs/grunt-contrib-connect/issues"}, "bundleDependencies": false, "dependencies": {"async": "^3.2.0", "connect": "^3.7.0", "connect-livereload": "^0.6.1", "morgan": "^1.10.0", "node-http2": "^4.0.1", "open": "^8.0.0", "portscanner": "^2.2.0", "serve-index": "^1.9.1", "serve-static": "^1.14.1"}, "deprecated": false, "description": "Start a connect web server", "devDependencies": {"grunt": "^1.6.1", "grunt-contrib-internal": "^8.0.0", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-nodeunit": "^5.0.0"}, "engines": {"node": ">=16"}, "files": ["tasks"], "homepage": "https://github.com/gruntjs/grunt-contrib-connect#readme", "keywords": ["gruntplugin", "server", "connect", "http"], "license": "MIT", "main": "tasks/connect.js", "name": "grunt-contrib-connect", "repository": {"type": "git", "url": "git+https://github.com/gruntjs/grunt-contrib-connect.git"}, "scripts": {"test": "grunt test"}, "version": "4.0.0"}