/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'es-mx', {
	alt: 'Texto alternativo',
	border: 'Borde',
	btnUpload: 'Enviar al servidor',
	button2Img: '¿Desea transformar el botón de imagen seleccionado en una imagen simple?',
	hSpace: 'Espacio horizontal',
	img2Button: '¿Desea transformar la imagen seleccionada en un botón de imagen?',
	infoTab: 'Información de imagen',
	linkTab: 'Enlace',
	lockRatio: 'Bloquear aspecto',
	menu: 'Propiedades de la imagen',
	resetSize: 'Reiniciar tamaño',
	title: 'Propiedades de la imagen',
	titleButton: 'Propiedades del botón de imagen',
	upload: 'Cargar',
	urlMissing: 'Falta la URL de origen de la imagen.',
	vSpace: 'Espacio vertical',
	validateBorder: 'El borde debe ser un número entero.',
	validateHSpace: 'El espacio horizontal debe ser un número entero.',
	validateVSpace: 'El espacio vertical debe ser un número entero.'
} );
