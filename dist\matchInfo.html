<!DOCTYPE html><head><meta charset="UTF-8"><title>课堂3.0-制卷</title><link rel="stylesheet" href="css/base.css"><link rel="stylesheet" href="css/testbank/common.css"><link rel="stylesheet" href="css/testbank/testbank.css"><link rel="stylesheet" href="js/thirdLib/layui/css/layui.css"><link rel="stylesheet" href="css/avalonComponent.css"><link rel="stylesheet" href="css/zTreeStyle.css"><link rel="stylesheet" href="css/testbank/matchInfo.css"></head><body><div class="header-mask" id="header-mask"><div class="loading-cover"></div><div class="loading-box"><div class="img"></div><div class="loading-txt"></div></div></div><div class="main ms-controller" ms-controller="app"><div class="cur-opr-wrap"><div class="cur-opr-box"><div class="btn-box-top"><a class="info-tip">● 题目标注后可智能推送举一反三巩固练习</a><div class="siblin"><span class="yellowButton" ms-click="batchSetAnnotate"><span class="txt">批量标注</span></span><span id="aiKnowledge" style="display:none" class="yellowButton select" ms-click="batchAISetKnow"><span class="txt">智能标注</span> </span><span ms-click="openUrl()" class="topButton">完成制题</span></div><div ms-click="setQuesScore" class="skip" style="float:left"><span class="txt">设置分值</span></div><div ms-click="upStep" class="skip" style="float:left"><span class="txt">上一步</span></div></div></div></div><div class="preview-main"><div class="preview-con-wrap"><div class="preview-con"><div class="preview-con-top"><span class="line-h" style="float:right"><span class="Iselect" ms-click="selectAll" ms-class="selectQuesIds.length>0 && selectQuesIds.length==quesCount ? 'select' :''">选入所有题目</span></span></div><h1>{{testBankInfo.Name}}</h1><div id="quesAttr" class="preview-win-tk"><div class="preview-win-tit"><div class="blue-tip-line"></div><span>题目详情</span></div><div class="main"><div class="label lable-que">答案</div><div class="con-box"><span id="answerHtml" class="answer"></span></div><div class="label lable-serv">解析</div><div id="parsHtml" class="con-box"></div><div class="label lable-chara">知识点</div><div class="con-box"><span class="answer">{{quesAttributeObj.knowTitle}}</span></div><div class="label lable-quality">核心素养</div><div class="con-box"><span class="answer">{{quesAttributeObj.qualityName}}</span></div><div class="label lable-dif">难度</div><div class="con-box"><span class="answer">{{quesAttributeObj.diffLeve}}</span></div></div><div class="foot"><div class="leftinfo" style="width:100px;overflow:initial"><span class="number">1</span> <font style="color:#b0cae4">/</font> <span style="color:#b0cae4">-</span></div><div class="rightinfo"><span ms-click="LastOrNext('last')" class="pre" style="width:97px"><section>上一题</section></span><span style="font-size:16px" ms-click="LastOrNext('next')" class="next">下一题</span></div></div></div><div id="batchDiffAndGrasp" class="preview-win-tk preview-win-step"><div class="preview-win-tit" style="text-align:center"><span ms-click="currentWinState=3" ms-class="[currentWinState==3?'cur':'']">批量设置难度</span> <span ms-click="currentWinState=4" ms-class="[currentWinState==4?'cur':'']">批量设置掌握度</span> <i class="delete"></i></div><div ms-visible="currentWinState==3" class="warm">请先在左边勾选题目再选择对应的难度</div><div ms-visible="currentWinState==4" class="warm">请先在左边勾选题目再选择对应的掌握度</div><div ms-visible="currentWinState==3" class="main degree"><div ms-for="($index,item) in diffLeve | orderBy('dCode', -1)" ms-click="batchSetDiffOrGrasp('diffCode',item)" class="step-tips"><span>{{item.dName}}</span> <i ms-for="item in [1,2,3,4,5,6,7,8,9,10] | limitBy($index+1)"></i></div></div><div ms-visible="currentWinState==4" class="main degree"><div ms-for="($index,item) in graspLeve" ms-click="batchSetDiffOrGrasp('graspCode',item)" class="stap-tips"><span>{{item.dName}}</span> <i ms-for="item in [1,2,3,4,5,6,7,8,9,10] | limitBy($index+1)"></i></div></div><div class="foot"><div class="leftinfo" style="display:none;text-align:center;float:none;width:auto"><span class="number">1</span> <font style="color:#b0cae4">/</font> <span style="color:#b0cae4">-</span></div><div class="rightinfo"></div></div></div><ms-page-state ms-widget="{state:@dataState}"></ms-page-state><div ms-for="($index,item) in quesList"><div class="preview-big-tit-box"><h2>{{item.name}}</h2><a class="chkbox" ms-class="item.isAllSelect ? 'select' : ''" ms-click="selectBig(item,$event)">全选该大题</a></div><div class="big_html html" ms-if="item.cutHtmlUrl" ms-attr="{'data-htmlUrl':item.cutHtmlUrl,'data-qid':item.id}"></div><div class="rd-area" ms-for="($sindex,sitem) in item.questions" ms-class="[sitem.id==currentLocationQuesId ? 'hover' : '']" ms-attr="{sid:sitem.id,typeCode:sitem.typeCode,typeName:sitem.typeName,catalogName:sitem.knowTitle,qualityName:sitem.qualityName,diff:sitem.diffLeve,grasp:sitem.graspLeve,answerHtmlUrl:sitem.answerHtmlUrl,answer:sitem.answer,parsHtmlUrl:sitem.parsHtmlUrl}"><span ms-class="[selectQuesIds.indexOf(sitem.id)!=-1||sitem.id==currentLocationQuesId?'bgObj':'']" class="sort">{{sitem.number}}</span><div class="content" ms-attr="{'data-htmlurl':sitem.cutHtmlUrl,'data-qid':sitem.id}" ms-on-click-1="currentWinState=1" ms-on-click-2="quesAttributeObjSet(sitem.id,sitem.typeCode,sitem.typeName,sitem.knowTitle,sitem.qualityName,sitem.diffLeve,sitem.graspLeve,sitem.answerHtmlUrl,sitem.answer,sitem.parsHtmlUrl)"></div><div class="info-box"><div class="info-t"><div class="info-t-c"><span class="label">难度：</span><ul class="diff-ul"><li ms-for="(d,diff) in @diffLeve | orderBy('dCode', 1)" ms-attr="{title:diff.dName,'data-code':diff.dCode}" ms-class="sitem.diffLeve>=diff.dCode&&sitem.diffLeve!=null?'hl':''" ms-click="setDiffOrGrasp('diff',sitem.id,diff.dCode,$index,$sindex,diff.dName)"></li></ul></div><div class="info-t-c"><span class="label">核心素养：</span><div class="knowledge-path"><span class="text" ms-if="sitem.qualityList.length === 0">略</span><div ms-attr="{title:ssitem.name} | strEval" class="btn" ms-for="($ssindex,ssitem) in sitem.qualityList">{{ssitem.name }}<div class="del" ms-click="delItem(ssitem,'qualityList',$index,$sindex,sitem.id)"></div></div></div></div></div><div class="knowledge"><span class="knowledge-l">知识点：</span><div class="knowledge-path"><span class="text" ms-if="sitem.knowList.length === 0">略</span><div ms-attr="{title:ssitem.name} | strEval" class="btn" ms-for="($ssindex,ssitem) in sitem.knowList">{{ssitem.name }}<div class="del" ms-click="delItem(ssitem,'knowledge',$index,$sindex,sitem.id)"></div></div></div><span ms-click="setAnnotate(sitem,item,'single')" class="enter-check">设置标注</span></div></div><div class="select-board" ms-click="pushOrRemoveQuesId(sitem,item)" ms-class="[selectQuesIds.indexOf(sitem.id)!=-1?'selected':'']"></div></div></div></div><p style="text-align:center;line-height:60px"></p></div></div></div><script defer="defer" type="text/javascript" src="https://fs.iclass30.com/aliba/plug/mathjax/MathJax.js"></script><script type="text/x-mathjax-config">MathJax.Hub.Register.StartupHook("TeX Jax Ready", function () {
            var TEX = MathJax.InputJax.TeX;
            // 注册预处理钩子，在处理 TeX 公式前执行
            TEX.prefilterHooks.Add(function (data) {
                // 判断是否是行内公式（非 display 模式）
                if (!data.display) {
                    // 给行内公式前面添加 \displaystyle 
                    data.math = '\\displaystyle ' + data.math;
                }
                return data;
            });
        });
        MathJax.Hub.Config({
            showProcessingMessages: false,
            messageStyle: "none",
            menuSettings: {
              context: "Browser"
            },
            "HTML-CSS": {
              webFont: "STIX-Web",
              availableFonts: ["STIX-Web"],
              preferredFont: "STIX-Web",
              styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
              undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
              scale:110
            },
            "CommonHTML": {
              webFont: "STIX-Web",
              availableFonts: ["STIX-Web"],
              preferredFont: "STIX-Web",
              styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
              undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
              scale:110
            },
            tex2jax: {
              inlineMath: [['$$','$$'],["\\(","\\)"],["\\[", "\\]"]],
              processEscapes: true,
              skipTags: ['script', 'style',  'pre', 'code']
            },
            MMLorHTML: {prefer: "HTML"},
            jax: ["input/TeX","output/CommonHTML"],
            extensions: ["tex2jax.js","mml2jax.js","MathMenu.js","MathZoom.js", "fast-preview.js", "AssistiveMML.js"],
            TeX: {
                extensions: ["extpfeil.js","AMSmath.js","AMSsymbols.js","noErrors.js","noUndefined.js","cancel.js","mhchem.js","autoload-all.js"]
            },
            font:"STIX-Web",
      
            SVG: {
              scale: 110,
              linebreaks: {
                   automatic: true
              },
              addMMLclasses: false
            }
          });</script><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script src="js/util/common.js"></script><script src="js/util/ajax.js"></script><script src="js/thirdLib/layui/layui.js"></script><script src="js/thirdLib/layui/layui-config.js"></script><script src="js/thirdLib/avalon/avalon.js"></script><script src="js/thirdLib/avalon/component.js"></script><script src="js/testbank/matchInfo.js"></script><script src="js/thirdLib/ztree/jquery.ztree.all.min.js"></script><script src="js/thirdLib/ztree/tree.js"></script></body><script src="js/thirdLib/disable-devtool.js"></script><script>DisableDevtool({md5:"27365a67be2217aa30a5e690d6937273",tkName:"mk"})</script>