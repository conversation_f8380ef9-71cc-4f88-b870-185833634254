"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var DEFAULT_CONFIG = {
  METHOD: "POST",
  DATA_TYPE: "json",
  ASYNC: !0
};
function getQueryString(t) {
  t = new RegExp("(^|&)" + t + "=([^&]*)(&|$)", "i"), t = window.location.search.substr(1).match(t);
  return t ? decodeURIComponent(t[2]) : null;
}
function checkUrl(t) {
  return !(!t || "" === t) && /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(t);
}
function processUrl(t) {
  var r;
  return !checkUrl(t) && (r = getQueryString("url")) ? r + t : t;
}
function getParamsWithToken() {
  var t = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var r = getQueryString("token");
  return r ? _objectSpread(_objectSpread({}, t), {}, {
    token: r
  }) : t;
}
function ajax(t) {
  var r = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var e = arguments.length > 2 ? arguments[2] : undefined;
  var n = arguments.length > 3 ? arguments[3] : undefined;
  var a = arguments.length > 4 ? arguments[4] : undefined;
  var o = arguments.length > 5 ? arguments[5] : undefined;
  t = processUrl(t), r = getParamsWithToken(r), t = {
    url: t,
    data: r,
    async: !1 !== e,
    type: n || DEFAULT_CONFIG.METHOD,
    dataType: a || DEFAULT_CONFIG.DATA_TYPE
  };
  return "raw" === o && (t.contentType = "application/json", t.data = JSON.stringify(r)), $.ajax(t);
}
function ajaxPro(t) {
  var r = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var e = arguments.length > 2 ? arguments[2] : undefined;
  var n = arguments.length > 3 ? arguments[3] : undefined;
  var a = arguments.length > 4 ? arguments[4] : undefined;
  var o = processUrl(t);
  t = getParamsWithToken(r);
  var c = n || DEFAULT_CONFIG.METHOD;
  return $.ajax({
    url: o,
    data: t,
    async: !1 !== e,
    type: c,
    dataType: a || DEFAULT_CONFIG.DATA_TYPE
  }).then(function (t) {
    return 1 === t.code ? t.data : -1 === t.code ? $.Deferred().reject(t) : t;
  }, function (t) {
    return console.error("ajaxPro ".concat(c, ":").concat(o, ", status:") + t.status), $.Deferred().reject(t);
  });
}
window.ajax = ajax, window.ajaxPro = window.ajaxPro || ajaxPro;
