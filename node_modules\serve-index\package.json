{"_from": "serve-index@^1.9.1", "_id": "serve-index@1.9.1", "_inBundle": false, "_integrity": "sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==", "_location": "/serve-index", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "serve-index@^1.9.1", "name": "serve-index", "escapedName": "serve-index", "rawSpec": "^1.9.1", "saveSpec": null, "fetchSpec": "^1.9.1"}, "_requiredBy": ["/grunt-contrib-connect"], "_resolved": "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz", "_shasum": "d3768d69b1e7d82e5ce050fff5b453bea12a9239", "_spec": "serve-index@^1.9.1", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\grunt-contrib-connect", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/expressjs/serve-index/issues"}, "bundleDependencies": false, "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "deprecated": false, "description": "Serve directory listings", "devDependencies": {"after": "0.8.2", "istanbul": "0.4.5", "mocha": "2.5.3", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8.0"}, "files": ["public/", "LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/serve-index#readme", "license": "MIT", "name": "serve-index", "repository": {"type": "git", "url": "git+https://github.com/expressjs/serve-index.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.9.1"}