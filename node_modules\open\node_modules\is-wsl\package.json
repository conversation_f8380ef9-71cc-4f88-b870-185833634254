{"_from": "is-wsl@^2.2.0", "_id": "is-wsl@2.2.0", "_inBundle": false, "_integrity": "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==", "_location": "/open/is-wsl", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-wsl@^2.2.0", "name": "is-wsl", "escapedName": "is-wsl", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/open"], "_resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "_shasum": "74a4c76e77ca9fd3f932f290c17ea326cd157271", "_spec": "is-wsl@^2.2.0", "_where": "D:\\代码\\datedu-hw\\cutTestPaper\\node_modules\\open", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-wsl/issues"}, "bundleDependencies": false, "dependencies": {"is-docker": "^2.0.0"}, "deprecated": false, "description": "Check if the process is running inside Windows Subsystem for Linux (Bash on Windows)", "devDependencies": {"ava": "^1.4.1", "clear-module": "^3.2.0", "proxyquire": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-wsl#readme", "keywords": ["check", "wsl", "windows", "subsystem", "linux", "detect", "bash", "process", "console", "terminal", "is"], "license": "MIT", "name": "is-wsl", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-wsl.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.2.0"}