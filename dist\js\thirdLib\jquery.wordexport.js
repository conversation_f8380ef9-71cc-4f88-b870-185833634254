"undefined"!=typeof jQuery&&"undefined"!=typeof saveAs?function(y){y.fn.wordExport=function(e){e=void 0!==e?e:"jQuery-Word-Export";for(var t={mhtml:{top:"Mime-Version: 1.0\nContent-Base: "+location.href+'\nContent-Type: Multipart/related; boundary="NEXT.ITEM-BOUNDARY";type="text/html"\n\n--NEXT.ITEM-BOUNDARY\nContent-Type: text/html; charset="utf-8"\nContent-Location: '+location.href+"\n\n<!DOCTYPE html>\n<html>\n_html_</html>",head:'<head>\n<meta http-equiv="Content-Type" content="text/html; charset=utf-8">\n<style>\n_styles_\n</style>\n</head>\n',body:"<body>_body_</body>"}},n=624,o=y(this).clone(),r=(o.each(function(){var e=y(this);e.is(":hidden")&&e.remove()}),Array()),i=o.find("img"),d=0;d<i.length;d++){i[d].setAttribute("crossOrigin","anonymous");var a=Math.min(i[d].width,n),s=i[d].height*(a/i[d].width),h=document.createElement("CANVAS");h.width=a,h.height=s;h.getContext("2d").drawImage(i[d],0,0,a,s);h=h.toDataURL("image/png");y(i[d]).attr("src",i[d].src),i[d].width=a,i[d].height=s,r[d]={type:h.substring(h.indexOf(":")+1,h.indexOf(";")),encoding:h.substring(h.indexOf(";")+1,h.indexOf(",")),location:y(i[d]).attr("src"),data:h.substring(h.indexOf(",")+1)}}for(var l="\n",d=0;d<r.length;d++)l=(l=(l=(l=(l+="--NEXT.ITEM-BOUNDARY\n")+"Content-Location: "+r[d].location+"\n")+"Content-Type: "+r[d].type+"\n")+"Content-Transfer-Encoding: "+r[d].encoding+"\n\n")+r[d].data+"\n\n";l+="--NEXT.ITEM-BOUNDARY--";t=t.mhtml.top.replace("_html_",t.mhtml.head.replace("_styles_","")+t.mhtml.body.replace("_body_",o.html()))+l,o=new Blob([t],{type:"application/msword;charset=utf-8"});saveAs(o,e+".doc")}}(jQuery):("undefined"==typeof jQuery&&console.error("jQuery Word Export: missing dependency (jQuery)"),"undefined"==typeof saveAs&&console.error("jQuery Word Export: missing dependency (FileSaver.js)"));