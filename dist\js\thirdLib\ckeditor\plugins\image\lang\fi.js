"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang('image', 'fi', {
  alt: '<PERSON><PERSON><PERSON><PERSON>htoinen teksti',
  border: 'Kehy<PERSON>',
  btnUpload: 'Lähetä palvelimelle',
  button2Img: 'Haluatko muuntaa valitun kuvanäppäimen kuvaksi?',
  hSpace: 'Vaakatila',
  img2Button: 'Haluatko muuntaa valitun kuvan kuvanäppäimeksi?',
  infoTab: '<PERSON><PERSON> tiedot',
  linkTab: '<PERSON><PERSON>',
  lockRatio: 'Lukitse suhteet',
  menu: 'Kuvan ominaisuudet',
  resetSize: '<PERSON><PERSON><PERSON><PERSON><PERSON> koko',
  title: '<PERSON><PERSON> ominaisuudet',
  titleButton: 'Kuvapainikke<PERSON> ominaisuudet',
  upload: 'Lisä<PERSON> kuva',
  urlMissing: '<PERSON>van lähdeosoite puuttuu.',
  vSpace: '<PERSON>ysty<PERSON><PERSON>',
  validateBorder: '<PERSON><PERSON>ksen täytyy olla kokonaisluku.',
  validateHSpace: 'HSpace-määrityksen täytyy olla kokonaisluku.',
  validateVSpace: 'VSpace-määrityksen täytyy olla kokonaisluku.'
});
