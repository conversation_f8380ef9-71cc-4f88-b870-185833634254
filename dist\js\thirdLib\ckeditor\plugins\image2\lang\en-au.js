"use strict";

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang('image2', 'en-au', {
  alt: 'Alternative Text',
  btnUpload: 'Send it to the Server',
  captioned: 'Captioned image',
  captionPlaceholder: 'Caption',
  infoTab: 'Image Info',
  lockRatio: 'Lock Ratio',
  menu: 'Image Properties',
  pathName: 'image',
  pathNameCaption: 'caption',
  resetSize: 'Reset Size',
  resizer: 'Click and drag to resize',
  title: 'Image Properties',
  uploadTab: 'Upload',
  urlMissing: 'Image source URL is missing.',
  altMissing: 'Alternative text is missing.'
});
