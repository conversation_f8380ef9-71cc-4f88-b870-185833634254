<!DOCTYPE HTML><html><head><title>题目设置</title><style>::-webkit-scrollbar{width:6px}::-webkit-scrollbar-button{display:none}::-webkit-scrollbar-thumb{background:#ebedf5;border-radius:8px}body,html{user-select:none;height:100%;overflow:hidden}body{font-family:"Microsoft YaHei",serif;user-select:none}body,dd,dl,h1,h2,h3,h4,h5,h6,p{margin:0}li,ol,ul{margin:0;padding:0;list-style:none}img{border:none}#container{height:calc(100% - 70px);width:100%;overflow:hidden auto;box-sizing:border-box;border-radius:3px;background-color:#fff;font-size:16px}.set-main{margin:0 20px}.set-main>.set-line{width:100%;display:flex;align-items:center;justify-content:flex-start;flex-wrap:wrap;min-height:35px;margin:5px 0}.set-line{font-size:16px}.set-line.ques-title{color:#565c66}.set-line-title{width:60px;text-align:left;color:#666d80}.set-line-title.set-line-text{color:#212530}.set-line-title.sq-title{text-align:center}.set-line-eval{color:#3166ea;margin:0 10px;cursor:pointer}.split-icon{margin:0 20px}.set-select{width:150px;height:36px;line-height:36;cursor:pointer;text-indent:20px;font-size:16px;border:1px solid #d9ddea}.set-input{width:50px;height:28px;margin-right:10px;font-size:16px;text-align:center;border-radius:4px;border:1px solid #d9ddea}select[disabled=disabled]{cursor:no-drop}option{font-size:16px;cursor:pointer}input[type=checkbox]{opacity:1}input[type=checkbox]:after{position:relative;top:-2px;width:15px;height:20px;content:'';display:block;background:url(../images/assign/select_icon.png) no-repeat 0 -54px/100%;cursor:pointer}input[type=checkbox]:hover:after{background-position-y:-110px}input[type=checkbox].checked:after,input[type=checkbox].checked:hover:after{background-position-y:1px}label{cursor:pointer}.set-operation{position:fixed;bottom:0;height:70px;border-top:1px solid #d9ddea;width:100%;display:flex;align-items:center;justify-content:flex-end;background:#fff}.set-operation a{width:130px;height:34px;border:1px solid #d9ddea;border-radius:3px;margin:0 20px;line-height:34px;text-align:center;font-size:16px;cursor:pointer}.set-operation a.cancel{background-color:#fff;color:#878787}.set-operation a.cancel:hover{background-color:#f6f7fa}.set-operation a.set-operation-ok{background-color:#3e73f6;color:#fff}.set-operation a.set-operation-ok:hover{background-color:#3166ea}</style></head><body><div id="container"><div v-for="(item, index) in quesList" class="set-main big-ques" :key="index"><div class="set-line ques-title"><label>{{item.bigTitle}}（共{{item.list.length}}题）</label></div><div class="set-line ques-set"><template v-if="item.type=='7'"><span class="set-line-title set-line-text">总分</span><div class="sinput-one short"><input v-model="item.score" @input="checkBigInput(item)" type="text" class="set-input"></div><span>分</span> <span class="set-line-eval" @click.prevent="setFillScore(item)">平均分配</span></template><template v-else><span class="set-line-title set-line-text">总分</span><div class="sinput-one short"><input v-model="item.score" @input="checkBigInput(item)" type="text" class="set-input"></div><span>分</span> <span class="set-line-eval" @click.prevent="setScore(item)">平均分配</span></template><template v-if="item.type=='1'" class="multiple-ques-set"><span class="set-line-title set-line-text">半对得</span><div class="sinput-one short"><input v-model="item.halfScore" @input="setHalfScore(item)" type="text" class="set-input"></div><span>分</span></template></div><template v-for="(sq,sqIndex) in item.list"><template v-if="item.type=='7'"><div v-for="(sf,sindex) in sq.list" class="set-line small-ques-score"><span class="set-line-title sq-title set-line-text">{{sq.quesNo||(index+1)}}-{{sindex+1}}</span><div class="sinput-one short"><input v-model="sf.score" @input="setFillSmallScore(sf,item)" type="text" class="set-input"></div><span class="set-line-title">分</span></div></template><div v-else class="set-line small-ques-score"><span class="set-line-title sq-title set-line-text">{{sq.quesNo||(index+1)}}</span><div class="sinput-one short"><input v-model="sq.score" @input="setSmallScore(sq,item)" type="text" class="set-input"></div><span class="set-line-title">分</span></div></template></div><div class="set-operation"><span>共{{getAllScore.allCount}}题，总分{{getAllScore.allScore}}分</span> <a class="cancel" @click="close">取消</a> <a class="set-operation-ok" @click="savePaperScore">确定</a></div></div></body></html><script type="text/javascript" src="js/thirdLib/jQuery/jquery-1.9.1.min.js"></script><script type="text/javascript" src="js/thirdLib/vue.js"></script><script type="text/javascript">var _parent=parent,app=new Vue({el:"#container",data:{quesList:[],id:"",token:"",url:""},computed:{getAllScore:function(){let s=0,r=0;return this.quesList.forEach(function(e,t){r+=e.list.length,s+=10*parseFloat(e.score)}),r=r,{allScore:s/=10,allCount:r}}},mounted(){this.id=this.getQueryString("id"),this.token=this.getQueryString("token"),this.url=this.getQueryString("url"),this.getPaperInfo()},methods:{getQueryString:function(e){e=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),e=window.location.search.substr(1).match(e);return null!=e?decodeURIComponent(e[2]):null},getPaperInfo:function(){let t=this;var e={testBankId:this.id,token:this.token};$.ajax({url:this.url+"/testbank/TBQuestion/findScore",data:e,type:"POST",datatype:"json",success:function(e){1==e.code&&(e.data.forEach(function(e,t){e.list.forEach(function(t){if(7==e.type){var s=t.smallScore.split(",");for(let e=0;e<s.length;e++)t.list.push({score:s[e]})}1==e.type&&(e.halfScore=e.list[0].halfScore)})}),t.quesList=e.data)},error:function(e,t,s){}})},savePaperScore(){var e;this.checkQuesScore()&&(e={testBankId:this.id,token:this.token,quesScoreJson:JSON.stringify(this.quesList)},$.ajax({url:this.url+"/testbank/TBQuestion/batchSetScore",data:e,type:"POST",datatype:"json",success:e=>{1==e.code&&(this.setLocalData(),this.close())},error:function(e,t,s){}}))},setLocalData(){var e=sessionStorage.getItem("cut17Data_"+this.id).replace(/[\r\n]/g,"").replace(/\&nbsp\;/g,"").replace(/[\s]+/g," "),s=e&&JSON.parse(e);this.quesList.forEach(function(e){e.list.forEach(function(t){s.forEach(function(e){""!=e.data.levelcode?e.data.qs.forEach(function(e){e.qid==t.id&&(e.score=t.score,7==t.type?e.setScoreJson=t.smallScore:1==t.type&&(e.halfScore=t.halfScore))}):e.qId==t.id&&(e.data.qs[0].score=t.score,7==t.type?e.data.qs[0].setScoreJson=t.smallScore:1==t.type&&(e.data.qs[0].halfScore=t.halfScore))})})}),sessionStorage.setItem("cut17Data_"+this.id,JSON.stringify(s))},checkQuesScore(){for(var e=0;e<this.quesList.length;e++){var s=this.quesList[e];let t=0;if(s.list.forEach(function(e){t+=parseFloat(10*e.score)}),t/10!=s.score)return parent.layer.msg(s.bigTitle+" 总分与小题分值不相符"),!1}return!0},close(){try{_parent.layer.closeAll()}catch(e){window.parent.postMessage("cancleSetScore","*")}},setFillScore(a){var e,l,t=0,s=(a.list.forEach(function(e){t+=e.list.length}),a.score/t);s<.1?parent.layer.msg("每小空分值不能小于 0.1 分"):(e=10*a.score,score=0==(l=e%t)?s:(e-l)/t/10,a.list.forEach(function(s,r){let o=0,c=[];s.list.forEach(function(e,t){r==a.list.length-1&&t==s.list.length-1?e.score=(10*score+l)/10:e.score=score,o+=10*e.score,c.push(e.score)}),s.score=o/10,s.smallScore=c.join(",")}))},setScore(s){var e,r,t=s.score/s.list.length;t<.1?parent.layer.msg("每小题分值不能小于 0.1 分"):s.halfScore>t?parent.layer.msg("每题分值不能小于半对分值"):(e=10*s.score,r=e%s.list.length,score=0==r?t:(e-r)/s.list.length/10,s.list.forEach(function(e,t){t==s.list.length-1?e.score=(10*score+r)/10:e.score=score}))},setHalfScore(t){t.halfScore=this.checkInput(t.halfScore,100),isNaN(t.halfScore)||""==t.halfScore||(0<t.list.filter(function(e){return e.score<t.halfScore}).length?(t.halfScore="",parent.layer.msg("半对分值不能超过满分分值!")):t.list.forEach(function(e){e.halfScore=t.halfScore}))},setSmallScore(e,t){var s;e.score=this.checkInput(e.score,100),isNaN(e.score)||""==e.score||(s=0,t.list.forEach(function(e){s+=10*parseFloat(e.score)}),t.score=s/10)},setFillSmallScore(e,t){var r;e.score=this.checkInput(e.score,100),isNaN(e.score)||""==e.score||(r=0,t.list.forEach(function(e){var t=0,s=[];e.list.forEach(function(e){t+=10*parseFloat(e.score),s.push(e.score)}),e.score=t/10,e.smallScore=s.join(","),r+=10*parseFloat(e.score)}),t.score=r/10)},checkBigInput(e){e.score=this.checkInput(e.score,100*e.list.length)},checkInput(e,t){return e=e.toString().replace(/[^\d.]/g,"").replace(/\.{2,}/g,".").replace(".","$#$").replace(/\./g,"").replace("$#$",".").replace(/^(\-)*(\d+)\.(\d).*$/,"$1$2.$3"),/\./g.test(e)||""==e?/^\.$/g.test(e)?"0"+e.substr(0,2):parseFloat(e)>t?t:e:Math.min(parseFloat(e),t)}}})</script>