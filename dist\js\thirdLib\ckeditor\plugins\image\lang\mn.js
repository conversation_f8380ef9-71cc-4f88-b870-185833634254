/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'mn', {
	alt: 'Зургийг орлох бичвэр',
	border: 'Хүрээ',
	btnUpload: 'Үүнийг сервэррүү илгээ',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'Хөндлөн зай',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Зурагны мэдээлэл',
	linkTab: 'Холбоос',
	lockRatio: 'Радио түгжих',
	menu: 'Зураг',
	resetSize: 'хэмжээ дахин оноох',
	title: 'Зураг',
	titleButton: 'Зурган товчны шинж чанар',
	upload: 'Хуулах',
	urlMissing: 'Зургийн эх сурвалжийн хаяг (URL) байхгүй байна.',
	vSpace: 'Босо<PERSON> зай',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );
