<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/protocol/stream.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">lib/protocol/</a> stream.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">87.01% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>201/231</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">82.42% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>150/182</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">86.96% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>20/23</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">87.44% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>195/223</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">264×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">263×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">263×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">404×</span>
<span class="cline-any cline-yes">404×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-yes">263×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">263×</span>
<span class="cline-any cline-yes">263×</span>
<span class="cline-any cline-yes">138×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-yes">255×</span>
<span class="cline-any cline-yes">255×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-yes">401×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">255×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">15983×</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">256×</span>
<span class="cline-any cline-yes">255×</span>
<span class="cline-any cline-yes">255×</span>
<span class="cline-any cline-yes">255×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-yes">320×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">805×</span>
<span class="cline-any cline-yes">805×</span>
<span class="cline-any cline-yes">805×</span>
<span class="cline-any cline-yes">805×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1086×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">530×</span>
<span class="cline-any cline-yes">535×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">17×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">270×</span>
<span class="cline-any cline-yes">262×</span>
<span class="cline-any cline-yes">262×</span>
<span class="cline-any cline-yes">120×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">262×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">270×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">152×</span>
<span class="cline-any cline-yes">132×</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">152×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">264×</span>
<span class="cline-any cline-yes">133×</span>
<span class="cline-any cline-yes">131×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">264×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">383×</span>
<span class="cline-any cline-yes">130×</span>
<span class="cline-any cline-yes">253×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">383×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">270×</span>
<span class="cline-any cline-yes">270×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">543×</span>
<span class="cline-any cline-yes">543×</span>
<span class="cline-any cline-yes">281×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">262×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">574×</span>
<span class="cline-any cline-yes">400×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1117×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">17×</span>
<span class="cline-any cline-yes">17×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var assert = require('assert');
&nbsp;
// The Stream class
// ================
&nbsp;
// Stream is a [Duplex stream](https://nodejs.org/api/stream.html#stream_class_stream_duplex)
// subclass that implements the [HTTP/2 Stream](https://tools.ietf.org/html/rfc7540#section-5)
// concept. It has two 'sides': one that is used by the user to send/receive data (the `stream`
// object itself) and one that is used by a Connection to read/write frames to/from the other peer
// (`stream.upstream`).
&nbsp;
var Duplex = require('stream').Duplex;
&nbsp;
exports.Stream = Stream;
&nbsp;
// Public API
// ----------
&nbsp;
// * **new Stream(log, connection)**: create a new Stream
//
// * **Event: 'headers' (headers)**: signals incoming headers
//
// * **Event: 'promise' (stream, headers)**: signals an incoming push promise
//
// * **Event: 'priority' (priority)**: signals a priority change. `priority` is a number between 0
//     (highest priority) and 2^31-1 (lowest priority). Default value is 2^30.
//
// * **Event: 'error' (type)**: signals an error
//
// * **headers(headers)**: send headers
//
// * **promise(headers): Stream**: promise a stream
//
// * **priority(priority)**: set the priority of the stream. Priority can be changed by the peer
//   too, but once it is set locally, it can not be changed remotely.
//
// * **reset(error)**: reset the stream with an error code
//
// * **upstream**: a [Flow](flow.js) that is used by the parent connection to write/read frames
//   that are to be sent/arrived to/from the peer and are related to this stream.
//
// Headers are always in the [regular node.js header format][1].
// [1]: https://nodejs.org/api/http.html#http_message_headers
&nbsp;
// Constructor
// -----------
&nbsp;
// The main aspects of managing the stream are:
function Stream(log, connection) {
  Duplex.call(this);
&nbsp;
  // * logging
  this._log = log.child({ component: 'stream', s: this });
&nbsp;
  // * receiving and sending stream management commands
  this._initializeManagement();
&nbsp;
  // * sending and receiving frames to/from the upstream connection
  this._initializeDataFlow();
&nbsp;
  // * maintaining the state of the stream (idle, open, closed, etc.) and error detection
  this._initializeState();
&nbsp;
  this.connection = connection;
}
&nbsp;
Stream.prototype = Object.create(Duplex.prototype, { constructor: { value: Stream } });
&nbsp;
// Managing the stream
// -------------------
&nbsp;
// the default stream priority is 2^30
var DEFAULT_PRIORITY = Math.pow(2, 30);
var MAX_PRIORITY = Math.pow(2, 31) - 1;
&nbsp;
// PUSH_PROMISE and HEADERS are forwarded to the user through events.
Stream.prototype._initializeManagement = function _initializeManagement() {
  this._resetSent = false;
  this._priority = DEFAULT_PRIORITY;
  this._letPeerPrioritize = true;
};
&nbsp;
Stream.prototype.promise = function promise(headers) {
  var stream = new Stream(this._log, this.connection);
  stream._priority = Math.min(this._priority + 1, MAX_PRIORITY);
  this._pushUpstream({
    type: 'PUSH_PROMISE',
    flags: {},
    stream: this.id,
    promised_stream: stream,
    headers: headers
  });
  return stream;
};
&nbsp;
Stream.prototype._onPromise = function _onPromise(frame) {
  this.emit('promise', frame.promised_stream, frame.headers);
};
&nbsp;
Stream.prototype.headers = function headers(headers) {
  this._pushUpstream({
    type: 'HEADERS',
    flags: {},
    stream: this.id,
    headers: headers
  });
};
&nbsp;
Stream.prototype._onHeaders = function _onHeaders(frame) {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (frame.priority !== undefined) {
<span class="cstat-no" title="statement not covered" >    this.priority(frame.priority, true);</span>
  }
  this.emit('headers', frame.headers);
};
&nbsp;
Stream.prototype.priority = <span class="fstat-no" title="function not covered" >function priority(priority, peer) {</span>
<span class="cstat-no" title="statement not covered" >  if ((peer &amp;&amp; this._letPeerPrioritize) || !peer) {</span>
<span class="cstat-no" title="statement not covered" >    if (!peer) {</span>
<span class="cstat-no" title="statement not covered" >      this._letPeerPrioritize = false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      var lastFrame = this.upstream.getLastQueuedFrame();</span>
<span class="cstat-no" title="statement not covered" >      if (lastFrame &amp;&amp; ((lastFrame.type === 'HEADERS') || (lastFrame.type === 'PRIORITY'))) {</span>
<span class="cstat-no" title="statement not covered" >        lastFrame.priority = priority;</span>
      } else {
<span class="cstat-no" title="statement not covered" >        this._pushUpstream({</span>
          type: 'PRIORITY',
          flags: {},
          stream: this.id,
          priority: priority
        });
      }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._log.debug({ priority: priority }, 'Changing priority');</span>
<span class="cstat-no" title="statement not covered" >    this.emit('priority', priority);</span>
<span class="cstat-no" title="statement not covered" >    this._priority = priority;</span>
  }
};
&nbsp;
Stream.prototype._onPriority = <span class="fstat-no" title="function not covered" >function _onPriority(frame) {</span>
<span class="cstat-no" title="statement not covered" >  this.priority(frame.priority, true);</span>
};
&nbsp;
// Resetting the stream. Normally, an endpoint SHOULD NOT send more than one RST_STREAM frame for
// any stream.
Stream.prototype.reset = function reset(error) {
  if (!this._resetSent) {
    this._resetSent = true;
    this._pushUpstream({
      type: 'RST_STREAM',
      flags: {},
      stream: this.id,
      error: error
    });
  }
};
&nbsp;
// Specify an alternate service for the origin of this stream
Stream.prototype.altsvc = <span class="fstat-no" title="function not covered" >function altsvc(host, port, protocolID, maxAge, origin) {</span>
<span class="cstat-no" title="statement not covered" >    var stream;</span>
<span class="cstat-no" title="statement not covered" >    if (origin) {</span>
<span class="cstat-no" title="statement not covered" >        stream = 0;</span>
    } else {
<span class="cstat-no" title="statement not covered" >        stream = this.id;</span>
    }
<span class="cstat-no" title="statement not covered" >    this._pushUpstream({</span>
        type: 'ALTSVC',
        flags: {},
        stream: stream,
        host: host,
        port: port,
        protocolID: protocolID,
        origin: origin,
        maxAge: maxAge
    });
};
&nbsp;
// Data flow
// ---------
&nbsp;
// The incoming and the generated outgoing frames are received/transmitted on the `this.upstream`
// [Flow](flow.html). The [Connection](connection.html) object instantiating the stream will read
// and write frames to/from it. The stream itself is a regular [Duplex stream][1], and is used by
// the user to write or read the body of the request.
// [1]: https://nodejs.org/api/stream.html#stream_class_stream_duplex
&nbsp;
//     upstream side                  stream                  user side
//
//                    +------------------------------------+
//                    |                                    |
//                    +------------------+                 |
//                    |     upstream     |                 |
//                    |                  |                 |
//                    +--+               |              +--|
//            read()  |  |  _send()      |    _write()  |  |  write(buf)
//     &lt;--------------|B |&lt;--------------|--------------| B|&lt;------------
//                    |  |               |              |  |
//            frames  +--+               |              +--|  buffers
//                    |  |               |              |  |
//     --------------&gt;|B |---------------|-------------&gt;| B|------------&gt;
//      write(frame)  |  |  _receive()   |     _read()  |  |  read()
//                    +--+               |              +--|
//                    |                  |                 |
//                    |                  |                 |
//                    +------------------+                 |
//                    |                                    |
//                    +------------------------------------+
//
//     B: input or output buffer
&nbsp;
var Flow = require('./flow').Flow;
&nbsp;
Stream.prototype._initializeDataFlow = function _initializeDataFlow() {
  this.id = undefined;
&nbsp;
  this._ended = false;
&nbsp;
  this.upstream = new Flow();
  this.upstream._log = this._log;
  this.upstream._send = this._send.bind(this);
  this.upstream._receive = this._receive.bind(this);
  this.upstream.write = this._writeUpstream.bind(this);
  this.upstream.on('error', this.emit.bind(this, 'error'));
&nbsp;
  this.on('finish', this._finishing);
};
&nbsp;
Stream.prototype._pushUpstream = function _pushUpstream(frame) {
  this.upstream.push(frame);
  this._transition(true, frame);
};
&nbsp;
// Overriding the upstream's `write` allows us to act immediately instead of waiting for the input
// queue to empty. This is important in case of control frames.
Stream.prototype._writeUpstream = function _writeUpstream(frame) {
  this._log.debug({ frame: frame }, 'Receiving frame');
&nbsp;
  var moreNeeded = Flow.prototype.write.call(this.upstream, frame);
&nbsp;
  // * Transition to a new state if that's the effect of receiving the frame
  this._transition(false, frame);
&nbsp;
  // * If it's a control frame. Call the appropriate handler method.
  if (frame.type === 'HEADERS') {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (this._processedHeaders &amp;&amp; !frame.flags['END_STREAM']) {
<span class="cstat-no" title="statement not covered" >      this.emit('error', 'PROTOCOL_ERROR');</span>
    }
    this._processedHeaders = true;
    this._onHeaders(frame);
  } else if (frame.type === 'PUSH_PROMISE') {
    this._onPromise(frame);
  } else <span class="missing-if-branch" title="if path not taken" >I</span>if (frame.type === 'PRIORITY') {
<span class="cstat-no" title="statement not covered" >    this._onPriority(frame);</span>
  } else <span class="missing-if-branch" title="if path not taken" >I</span>if (frame.type === 'ALTSVC') {
    // TODO
  } else <span class="missing-if-branch" title="if path not taken" >I</span>if (frame.type === 'BLOCKED') {
    // TODO
  }
&nbsp;
  // * If it's an invalid stream level frame, emit error
  else <span class="missing-if-branch" title="if path not taken" >I</span>if ((frame.type !== 'DATA') &amp;&amp;
           (<span class="branch-1 cbranch-no" title="branch not covered" >frame.type !== 'WINDOW_UPDATE')</span> &amp;&amp;
           (<span class="branch-2 cbranch-no" title="branch not covered" >frame.type !== 'RST_STREAM')</span>) {
<span class="cstat-no" title="statement not covered" >    this._log.error({ frame: frame }, 'Invalid stream level frame');</span>
<span class="cstat-no" title="statement not covered" >    this.emit('error', 'PROTOCOL_ERROR');</span>
  }
&nbsp;
  return moreNeeded;
};
&nbsp;
// The `_receive` method (= `upstream._receive`) gets called when there's an incoming frame.
Stream.prototype._receive = function _receive(frame, ready) {
  // * If it's a DATA frame, then push the payload into the output buffer on the other side.
  //   Call ready when the other side is ready to receive more.
  if (!this._ended &amp;&amp; (frame.type === 'DATA')) {
    var moreNeeded = this.push(frame.data);
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!moreNeeded) {
<span class="cstat-no" title="statement not covered" >      this._receiveMore = ready;</span>
    }
  }
&nbsp;
  // * Any frame may signal the end of the stream with the END_STREAM flag
  if (!this._ended &amp;&amp; (frame.flags.END_STREAM || (frame.type === 'RST_STREAM'))) {
    this.push(null);
    this._ended = true;
  }
&nbsp;
  // * Postpone calling `ready` if `push()` returned a falsy value
  <span class="missing-if-branch" title="else path not taken" >E</span>if (this._receiveMore !== ready) {
    ready();
  }
};
&nbsp;
// The `_read` method is called when the user side is ready to receive more data. If there's a
// pending write on the upstream, then call its pending ready callback to receive more frames.
Stream.prototype._read = function _read() {
  <span class="missing-if-branch" title="if path not taken" >I</span>if (this._receiveMore) {
<span class="cstat-no" title="statement not covered" >    var receiveMore = this._receiveMore;</span>
<span class="cstat-no" title="statement not covered" >    delete this._receiveMore;</span>
<span class="cstat-no" title="statement not covered" >    receiveMore();</span>
  }
};
&nbsp;
// The `write` method gets called when there's a write request from the user.
Stream.prototype._write = function _write(buffer, encoding, ready) {
  // * Chunking is done by the upstream Flow.
  var moreNeeded = this._pushUpstream({
    type: 'DATA',
    flags: {},
    stream: this.id,
    data: buffer
  });
&nbsp;
  // * Call ready when upstream is ready to receive more frames.
  <span class="missing-if-branch" title="if path not taken" >I</span>if (moreNeeded) {
<span class="cstat-no" title="statement not covered" >    ready();</span>
  } else {
    this._sendMore = ready;
  }
};
&nbsp;
// The `_send` (= `upstream._send`) method is called when upstream is ready to receive more frames.
// If there's a pending write on the user side, then call its pending ready callback to receive more
// writes.
Stream.prototype._send = function _send() {
  if (this._sendMore) {
    var sendMore = this._sendMore;
    delete this._sendMore;
    sendMore();
  }
};
&nbsp;
// When the stream is finishing (the user calls `end()` on it), then we have to set the `END_STREAM`
// flag on the last frame. If there's no frame in the queue, or if it doesn't support this flag,
// then we create a 0 length DATA frame. We could do this all the time, but putting the flag on an
// existing frame is a nice optimization.
var emptyBuffer = new Buffer(0);
Stream.prototype._finishing = function _finishing() {
  var endFrame = {
    type: 'DATA',
    flags: { END_STREAM: true },
    stream: this.id,
    data: emptyBuffer
  };
  var lastFrame = this.upstream.getLastQueuedFrame();
  if (lastFrame &amp;&amp; ((lastFrame.type === 'DATA') || (lastFrame.type === 'HEADERS'))) {
    this._log.debug({ frame: lastFrame }, 'Marking last frame with END_STREAM flag.');
    lastFrame.flags.END_STREAM = true;
    this._transition(true, endFrame);
  } else {
    this._pushUpstream(endFrame);
  }
};
&nbsp;
// [Stream States](https://tools.ietf.org/html/rfc7540#section-5.1)
// ----------------
//
//                           +--------+
//                     PP    |        |    PP
//                  ,--------|  idle  |--------.
//                 /         |        |         \
//                v          +--------+          v
//         +----------+          |           +----------+
//         |          |          | H         |          |
//     ,---| reserved |          |           | reserved |---.
//     |   | (local)  |          v           | (remote) |   |
//     |   +----------+      +--------+      +----------+   |
//     |      |          ES  |        |  ES          |      |
//     |      | H    ,-------|  open  |-------.      | H    |
//     |      |     /        |        |        \     |      |
//     |      v    v         +--------+         v    v      |
//     |   +----------+          |           +----------+   |
//     |   |   half   |          |           |   half   |   |
//     |   |  closed  |          | R         |  closed  |   |
//     |   | (remote) |          |           | (local)  |   |
//     |   +----------+          |           +----------+   |
//     |        |                v                 |        |
//     |        |  ES / R    +--------+  ES / R    |        |
//     |        `-----------&gt;|        |&lt;-----------'        |
//     |  R                  | closed |                  R  |
//     `--------------------&gt;|        |&lt;--------------------'
//                           +--------+
&nbsp;
// Streams begin in the IDLE state and transitions happen when there's an incoming or outgoing frame
Stream.prototype._initializeState = function _initializeState() {
  this.state = 'IDLE';
  this._initiated = undefined;
  this._closedByUs = undefined;
  this._closedWithRst = undefined;
  this._processedHeaders = false;
};
&nbsp;
// Only `_setState` should change `this.state` directly. It also logs the state change and notifies
// interested parties using the 'state' event.
Stream.prototype._setState = function transition(state) {
  assert(this.state !== state);
  this._log.debug({ from: this.state, to: state }, 'State transition');
  this.state = state;
  this.emit('state', state);
};
&nbsp;
// A state is 'active' if the stream in that state counts towards the concurrency limit. Streams
// that are in the "open" state, or either of the "half closed" states count toward this limit.
function activeState(state) {
  return ((state === 'HALF_CLOSED_LOCAL') || (state === 'HALF_CLOSED_REMOTE') || (state === 'OPEN'));
}
&nbsp;
// `_transition` is called every time there's an incoming or outgoing frame. It manages state
// transitions, and detects stream errors. A stream error is always caused by a frame that is not
// allowed in the current state.
Stream.prototype._transition = function transition(sending, frame) {
  var receiving = !sending;
  var connectionError;
  var streamError;
&nbsp;
  var DATA = false, HEADERS = false, PRIORITY = false, ALTSVC = false, BLOCKED = false;
  var RST_STREAM = false, PUSH_PROMISE = false, WINDOW_UPDATE = false;
  switch(frame.type) {
    case 'DATA'         : DATA          = true; break;
    case 'HEADERS'      : HEADERS       = true; break;
    case 'PRIORITY'     : PRIORITY      = true; break;
    case 'RST_STREAM'   : RST_STREAM    = true; break;
    case 'PUSH_PROMISE' : PUSH_PROMISE  = true; break;
    case 'WINDOW_UPDATE': WINDOW_UPDATE = true; break;
<span class="branch-6 cbranch-no" title="branch not covered" >    case 'ALTSVC'       : <span class="cstat-no" title="statement not covered" >ALTSVC        = true; <span class="cstat-no" title="statement not covered" ></span>break;</span></span>
<span class="branch-7 cbranch-no" title="branch not covered" >    case 'BLOCKED'      : <span class="cstat-no" title="statement not covered" >BLOCKED       = true; <span class="cstat-no" title="statement not covered" ></span>break;</span></span>
  }
&nbsp;
  var previousState = this.state;
&nbsp;
  switch (this.state) {
    // All streams start in the **idle** state. In this state, no frames have been exchanged.
    //
    // * Sending or receiving a HEADERS frame causes the stream to become "open".
    //
    // When the HEADERS frame contains the END_STREAM flags, then two state transitions happen.
    case 'IDLE':
      if (HEADERS) {
        this._setState('OPEN');
        if (frame.flags.END_STREAM) {
          this._setState(sending ? <span class="branch-0 cbranch-no" title="branch not covered" >'HALF_CLOSED_LOCAL' </span>: 'HALF_CLOSED_REMOTE');
        }
        this._initiated = sending;
      } else if (sending &amp;&amp; RST_STREAM) {
        this._setState('CLOSED');
      } else <span class="missing-if-branch" title="if path not taken" >I</span>if (PRIORITY) {
        /* No state change */
      } else {
        connectionError = 'PROTOCOL_ERROR';
      }
      break;
&nbsp;
    // A stream in the **reserved (local)** state is one that has been promised by sending a
    // PUSH_PROMISE frame.
    //
    // * The endpoint can send a HEADERS frame. This causes the stream to open in a "half closed
    //   (remote)" state.
    // * Either endpoint can send a RST_STREAM frame to cause the stream to become "closed". This
    //   releases the stream reservation.
    // * An endpoint may receive PRIORITY frame in this state.
    // * An endpoint MUST NOT send any other type of frame in this state.
    case 'RESERVED_LOCAL':
      if (sending &amp;&amp; HEADERS) {
        this._setState('HALF_CLOSED_REMOTE');
      } else if (RST_STREAM) {
        this._setState('CLOSED');
      } else <span class="missing-if-branch" title="if path not taken" >I</span>if (PRIORITY) {
        /* No state change */
      } else {
        connectionError = 'PROTOCOL_ERROR';
      }
      break;
&nbsp;
    // A stream in the **reserved (remote)** state has been reserved by a remote peer.
    //
    // * Either endpoint can send a RST_STREAM frame to cause the stream to become "closed". This
    //   releases the stream reservation.
    // * Receiving a HEADERS frame causes the stream to transition to "half closed (local)".
    // * An endpoint MAY send PRIORITY frames in this state to reprioritize the stream.
    // * Receiving any other type of frame MUST be treated as a stream error of type PROTOCOL_ERROR.
    case 'RESERVED_REMOTE':
      if (RST_STREAM) {
        this._setState('CLOSED');
      } else if (receiving &amp;&amp; HEADERS) {
        this._setState('HALF_CLOSED_LOCAL');
      } else <span class="missing-if-branch" title="if path not taken" >I</span>if (BLOCKED || PRIORITY) {
        /* No state change */
      } else {
        connectionError = 'PROTOCOL_ERROR';
      }
      break;
&nbsp;
    // The **open** state is where both peers can send frames. In this state, sending peers observe
    // advertised stream level flow control limits.
    //
    // * From this state either endpoint can send a frame with a END_STREAM flag set, which causes
    //   the stream to transition into one of the "half closed" states: an endpoint sending a
    //   END_STREAM flag causes the stream state to become "half closed (local)"; an endpoint
    //   receiving a END_STREAM flag causes the stream state to become "half closed (remote)".
    // * Either endpoint can send a RST_STREAM frame from this state, causing it to transition
    //   immediately to "closed".
    case 'OPEN':
      if (frame.flags.END_STREAM) {
        this._setState(sending ? 'HALF_CLOSED_LOCAL' : 'HALF_CLOSED_REMOTE');
      } else if (RST_STREAM) {
        this._setState('CLOSED');
      } else {
        /* No state change */
      }
      break;
&nbsp;
    // A stream that is **half closed (local)** cannot be used for sending frames.
    //
    // * A stream transitions from this state to "closed" when a frame that contains a END_STREAM
    //   flag is received, or when either peer sends a RST_STREAM frame.
    // * An endpoint MAY send or receive PRIORITY frames in this state to reprioritize the stream.
    // * WINDOW_UPDATE can be sent by a peer that has sent a frame bearing the END_STREAM flag.
    case 'HALF_CLOSED_LOCAL':
      if (RST_STREAM || (receiving &amp;&amp; frame.flags.END_STREAM)) {
        this._setState('CLOSED');
      } else if (BLOCKED || ALTSVC || receiving || PRIORITY || (sending &amp;&amp; WINDOW_UPDATE)) {
        /* No state change */
      } else {
        connectionError = 'PROTOCOL_ERROR';
      }
      break;
&nbsp;
    // A stream that is **half closed (remote)** is no longer being used by the peer to send frames.
    // In this state, an endpoint is no longer obligated to maintain a receiver flow control window
    // if it performs flow control.
    //
    // * If an endpoint receives additional frames for a stream that is in this state it MUST
    //   respond with a stream error of type STREAM_CLOSED.
    // * A stream can transition from this state to "closed" by sending a frame that contains a
    //   END_STREAM flag, or when either peer sends a RST_STREAM frame.
    // * An endpoint MAY send or receive PRIORITY frames in this state to reprioritize the stream.
    // * A receiver MAY receive a WINDOW_UPDATE frame on a "half closed (remote)" stream.
    case 'HALF_CLOSED_REMOTE':
      if (RST_STREAM || (sending &amp;&amp; frame.flags.END_STREAM)) {
        this._setState('CLOSED');
      } else if (BLOCKED || ALTSVC || sending || PRIORITY || (receiving &amp;&amp; WINDOW_UPDATE)) {
        /* No state change */
      } else {
        connectionError = 'PROTOCOL_ERROR';
      }
      break;
&nbsp;
    // The **closed** state is the terminal state.
    //
    // * An endpoint MUST NOT send frames on a closed stream. An endpoint that receives a frame
    //   after receiving a RST_STREAM or a frame containing a END_STREAM flag on that stream MUST
    //   treat that as a stream error of type STREAM_CLOSED.
    // * WINDOW_UPDATE, PRIORITY or RST_STREAM frames can be received in this state for a short
    //   period after a frame containing an END_STREAM flag is sent.  Until the remote peer receives
    //   and processes the frame bearing the END_STREAM flag, it might send either frame type.
    //   Endpoints MUST ignore WINDOW_UPDATE frames received in this state, though endpoints MAY
    //   choose to treat WINDOW_UPDATE frames that arrive a significant time after sending
    //   END_STREAM as a connection error of type PROTOCOL_ERROR.
    // * If this state is reached as a result of sending a RST_STREAM frame, the peer that receives
    //   the RST_STREAM might have already sent - or enqueued for sending - frames on the stream
    //   that cannot be withdrawn. An endpoint that sends a RST_STREAM frame MUST ignore frames that
    //   it receives on closed streams after it has sent a RST_STREAM frame. An endpoint MAY choose
    //   to limit the period over which it ignores frames and treat frames that arrive after this
    //   time as being in error.
    // * An endpoint might receive a PUSH_PROMISE frame after it sends RST_STREAM. PUSH_PROMISE
    //   causes a stream to become "reserved". If promised streams are not desired, a RST_STREAM
    //   can be used to close any of those streams.
    case 'CLOSED':
      if (PRIORITY || (sending &amp;&amp; RST_STREAM) ||
          (sending &amp;&amp; this._closedWithRst) ||
          (receiving &amp;&amp; WINDOW_UPDATE) ||
          (receiving &amp;&amp; this._closedByUs &amp;&amp;
           (this._closedWithRst || RST_STREAM || ALTSVC))) {
        /* No state change */
      } else {
        streamError = 'STREAM_CLOSED';
      }
      break;
  }
&nbsp;
  // Noting that the connection was closed by the other endpoint. It may be important in edge cases.
  // For example, when the peer tries to cancel a promised stream, but we already sent every data
  // on it, then the stream is in CLOSED state, yet we want to ignore the incoming RST_STREAM.
  if ((this.state === 'CLOSED') &amp;&amp; (previousState !== 'CLOSED')) {
    this._closedByUs = sending;
    this._closedWithRst = RST_STREAM;
  }
&nbsp;
  // Sending/receiving a PUSH_PROMISE
  //
  // * Sending a PUSH_PROMISE frame marks the associated stream for later use. The stream state
  //   for the reserved stream transitions to "reserved (local)".
  // * Receiving a PUSH_PROMISE frame marks the associated stream as reserved by the remote peer.
  //   The state of the stream becomes "reserved (remote)".
  if (PUSH_PROMISE &amp;&amp; !connectionError &amp;&amp; !streamError) {
    /* This assertion must hold, because _transition is called immediately when a frame is written
       to the stream. If it would be called when a frame gets out of the input queue, the state
       of the reserved could have been changed by then. */
    assert(frame.promised_stream.state === 'IDLE', frame.promised_stream.state);
    frame.promised_stream._setState(sending ? 'RESERVED_LOCAL' : 'RESERVED_REMOTE');
    frame.promised_stream._initiated = sending;
  }
&nbsp;
  // Signaling how sending/receiving this frame changes the active stream count (-1, 0 or +1)
  if (this._initiated) {
    var change = (activeState(this.state) - activeState(previousState));
    if (sending) {
      frame.count_change = change;
    } else {
      frame.count_change(change);
    }
  } else if (sending) {
    frame.count_change = 0;
  }
&nbsp;
  // Common error handling.
  if (connectionError || streamError) {
    var info = {
      error: connectionError,
      frame: frame,
      state: this.state,
      closedByUs: this._closedByUs,
      closedWithRst: this._closedWithRst
    };
&nbsp;
    // * When sending something invalid, throwing an exception, since it is probably a bug.
    if (sending) {
      this._log.error(info, 'Sending illegal frame.');
      return this.emit('error', new Error('Sending illegal frame (' + frame.type + ') in ' + this.state + ' state.'));
    }
&nbsp;
    // * In case of a serious problem, emitting and error and letting someone else handle it
    //   (e.g. closing the connection)
    // * When receiving something invalid, sending an RST_STREAM using the `reset` method.
    //   This will automatically cause a transition to the CLOSED state.
    else {
      this._log.error(info, 'Received illegal frame.');
      if (connectionError) {
        this.emit('connectionError', connectionError);
      } else {
        this.reset(streamError);
        this.emit('error', streamError);
      }
    }
  }
};
&nbsp;
// Bunyan serializers
// ------------------
&nbsp;
exports.serializers = {};
&nbsp;
var nextId = 0;
exports.serializers.s = function(stream) {
  if (!('_id' in stream)) {
    stream._id = nextId;
    nextId += 1;
  }
  return stream._id;
};
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Aug 23 2017 13:12:39 GMT-0700 (PDT)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
